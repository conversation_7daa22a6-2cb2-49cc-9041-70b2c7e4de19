{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    zIndex: 100\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 添加处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current) {\n      setSelectedIntersection(intersection);\n\n      // 将经纬度转换为场景坐标\n      const coords = converter.current.convertToSceneCoordinates(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n\n      // 创建相机动画\n      const currentPos = cameraRef.current.position.clone();\n      const targetPos = new THREE.Vector3(coords.x, 100, coords.z); // 设置高度为100\n\n      new TWEEN.Tween(currentPos).to(targetPos, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 设置相机朝向\n      if (controls) {\n        const targetLookAt = new THREE.Vector3(coords.x, 0, coords.z);\n        new TWEEN.Tween(controls.target).to(targetLookAt, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          controls.target.copy(targetLookAt);\n          controls.update();\n        }).start();\n      }\n      console.log('切换到路口视角:', {\n        路口名称: intersection.name,\n        经度: intersection.longitude,\n        纬度: intersection.latitude,\n        场景坐标: coords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      const messageData = JSON.parse(message.toString());\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n        const rsuid = messageData.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n\n      // 清理场景中的所有车辆模型\n      vehicleModels.forEach((model, id) => {\n        scene.remove(model);\n      });\n      vehicleModels.clear();\n\n      // 清理场景\n      while (scene.children.length > 0) {\n        scene.remove(scene.children[0]);\n      }\n      scene = null; // 重置scene\n      preloadedVehicleModel = null; // 重置预加载的模型\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u9009\\u62E9\\u8DEF\\u53E3\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      }))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 786,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"jdGWqGlpLYHi6cjPE5JHj3ruDak=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse(child => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "scene", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "BASE_URL", "vehicleModels", "Map", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "intersectionSelectStyle", "top", "width", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "find", "i", "name", "coords", "convertToSceneCoordinates", "parseFloat", "targetPos", "Vector3", "targetLookAt", "路口名称", "经度", "纬度", "场景坐标", "handleMqttMessage", "topic", "message", "error", "toString", "messageData", "JSON", "parse", "type", "_messageData$data", "participants", "data", "rsuid", "now", "Date", "for<PERSON>ach", "participant", "id", "partPtcId", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "preloadedModel", "model", "get", "newModel", "height", "set", "rotation", "add", "lastUpdate", "updateMatrix", "updateMatrixWorld", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "newState", "partLong", "partLat", "newPosition", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "event", "payload", "stringify", "onerror", "onclose", "setTimeout", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "children", "length", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "toArray", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "clearInterval", "end", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "style", "placeholder", "onChange", "options", "label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    zIndex: 100,\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 添加处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current) {\n      setSelectedIntersection(intersection);\n      \n      // 将经纬度转换为场景坐标\n      const coords = converter.current.convertToSceneCoordinates(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      // 创建相机动画\n      const currentPos = cameraRef.current.position.clone();\n      const targetPos = new THREE.Vector3(coords.x, 100, coords.z); // 设置高度为100\n\n      new TWEEN.Tween(currentPos)\n        .to(targetPos, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 设置相机朝向\n      if (controls) {\n        const targetLookAt = new THREE.Vector3(coords.x, 0, coords.z);\n        new TWEEN.Tween(controls.target)\n          .to(targetLookAt, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(targetLookAt);\n            controls.update();\n          })\n          .start();\n      }\n\n      console.log('切换到路口视角:', {\n        路口名称: intersection.name,\n        经度: intersection.longitude,\n        纬度: intersection.latitude,\n        场景坐标: coords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    \n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      const messageData = JSON.parse(message.toString());\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        const participants = messageData.data?.participants || [];\n        const rsuid = messageData.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n      \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n      \n      // 清理场景中的所有车辆模型\n      vehicleModels.forEach((model, id) => {\n        scene.remove(model);\n      });\n      vehicleModels.clear();\n      \n      // 清理场景\n      while(scene.children.length > 0) { \n        scene.remove(scene.children[0]); \n      }\n      \n      scene = null; // 重置scene\n      preloadedVehicleModel = null; // 重置预加载的模型\n    };\n  }, []);\n\n  return (\n    <>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"选择路口\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE;EACP;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGzC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM0C,UAAU,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM2C,SAAS,GAAG3C,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMuC,aAAa,GAAG5C,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM6C,eAAe,GAAG7C,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM8C,aAAa,GAAG9C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC;IAC/CkD,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMwD,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG1E,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC2E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM4E,uBAAuB,GAAG;IAC9BnB,QAAQ,EAAE,OAAO;IACjBoB,GAAG,EAAE,MAAM;IACXlB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BkB,KAAK,EAAE,OAAO;IACdjB,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAMkB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BxB,WAAW,CAAC,QAAQ,CAAC;IACrBnC,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC2D,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1B,WAAW,CAAC,QAAQ,CAAC;IACrBnC,UAAU,GAAG,QAAQ;IAErB,IAAIqD,SAAS,CAACS,OAAO,IAAI7D,QAAQ,EAAE;MACjC;MACA,MAAM8D,UAAU,GAAGV,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAAC2B,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGZ,SAAS,CAACS,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAI/E,KAAK,CAACkF,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAACvF,KAAK,CAACwF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvB,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAACwC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAI7F,KAAK,CAACkF,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACvF,KAAK,CAACwF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvB,SAAS,CAACS,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAG9E,QAAQ,CAAC+E,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAI/E,KAAK,CAACkF,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACvF,KAAK,CAACwF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd3E,QAAQ,CAAC+E,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACA1B,SAAS,CAACS,OAAO,CAACmB,MAAM,CAAChF,QAAQ,CAAC+E,MAAM,CAAC;QACzC/E,QAAQ,CAACiF,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACA7E,QAAQ,CAAC2D,OAAO,GAAG,IAAI;;MAEvB;MACA3D,QAAQ,CAACkF,WAAW,GAAG,EAAE;MACzBlF,QAAQ,CAACmF,WAAW,GAAG,GAAG;MAC1BnF,QAAQ,CAACoF,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtCtF,QAAQ,CAACuF,aAAa,GAAG,CAAC;MAC1BvF,QAAQ,CAACiF,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAG5G,iBAAiB,CAAC6G,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKL,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAI3C,SAAS,CAACS,OAAO,EAAE;MACrCP,uBAAuB,CAACyC,YAAY,CAAC;;MAErC;MACA,MAAMK,MAAM,GAAG/E,SAAS,CAACwC,OAAO,CAACwC,yBAAyB,CACxDC,UAAU,CAACP,YAAY,CAAClE,SAAS,CAAC,EAClCyE,UAAU,CAACP,YAAY,CAACjE,QAAQ,CAClC,CAAC;;MAED;MACA,MAAMgC,UAAU,GAAGV,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAAC2B,KAAK,CAAC,CAAC;MACrD,MAAMwC,SAAS,GAAG,IAAI3H,KAAK,CAAC4H,OAAO,CAACJ,MAAM,CAAChC,CAAC,EAAE,GAAG,EAAEgC,MAAM,CAAC9B,CAAC,CAAC,CAAC,CAAC;;MAE9D,IAAItF,KAAK,CAACkF,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAACoC,SAAS,EAAE,IAAI,CAAC,CACnBhC,MAAM,CAACvF,KAAK,CAACwF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvB,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAACwC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAI7E,QAAQ,EAAE;QACZ,MAAMyG,YAAY,GAAG,IAAI7H,KAAK,CAAC4H,OAAO,CAACJ,MAAM,CAAChC,CAAC,EAAE,CAAC,EAAEgC,MAAM,CAAC9B,CAAC,CAAC;QAC7D,IAAItF,KAAK,CAACkF,KAAK,CAAClE,QAAQ,CAAC+E,MAAM,CAAC,CAC7BZ,EAAE,CAACsC,YAAY,EAAE,IAAI,CAAC,CACtBlC,MAAM,CAACvF,KAAK,CAACwF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACd3E,QAAQ,CAAC+E,MAAM,CAACH,IAAI,CAAC6B,YAAY,CAAC;UAClCzG,QAAQ,CAACiF,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;MACZ;MAEAW,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;QACtBiB,IAAI,EAAEX,YAAY,CAACI,IAAI;QACvBQ,EAAE,EAAEZ,YAAY,CAAClE,SAAS;QAC1B+E,EAAE,EAAEb,YAAY,CAACjE,QAAQ;QACzB+E,IAAI,EAAET;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMU,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC5C,IAAI,CAAC5G,KAAK,EAAE;MACVoF,OAAO,CAACyB,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IAEAzB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBsB,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACE,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIH,KAAK,KAAK1G,WAAW,CAACM,MAAM,CAACE,GAAG,IAAIsG,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAAA,IAAAC,iBAAA;QAClE/B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0B,WAAW,CAAC;QAEpC,MAAMK,YAAY,GAAG,EAAAD,iBAAA,GAAAJ,WAAW,CAACM,IAAI,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBC,YAAY,KAAI,EAAE;QACzD,MAAME,KAAK,GAAGP,WAAW,CAACM,IAAI,CAACC,KAAK;;QAEpC;QACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAH,YAAY,CAACK,OAAO,CAACC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIL,KAAK,GAAGI,WAAW,CAACE,SAAS;UACzC,MAAMV,IAAI,GAAGQ,WAAW,CAACG,WAAW;UAEpC,IAAGX,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAMY,KAAK,GAAG;cACZrG,SAAS,EAAEyE,UAAU,CAACwB,WAAW,CAACK,WAAW,CAAC;cAC9CrG,QAAQ,EAAEwE,UAAU,CAACwB,WAAW,CAACM,UAAU,CAAC;cAC5CrG,KAAK,EAAEuE,UAAU,CAACwB,WAAW,CAACO,SAAS,CAAC;cACxCrG,OAAO,EAAEsE,UAAU,CAACwB,WAAW,CAACQ,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGlH,SAAS,CAACwC,OAAO,CAAC2E,YAAY,CAACN,KAAK,CAACrG,SAAS,EAAEqG,KAAK,CAACpG,QAAQ,CAAC;;YAEhF;YACA,IAAI2G,cAAc;YAClB,QAAQnB,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRmB,cAAc,GAAGxI,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRwI,cAAc,GAAGvI,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRuI,cAAc,GAAGtI,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIuI,KAAK,GAAG3H,aAAa,CAAC4H,GAAG,CAACZ,EAAE,CAAC;YAEjC,IAAI,CAACW,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAMG,QAAQ,GAAGH,cAAc,CAAC1E,KAAK,CAAC,CAAC;cACvC;cACA,MAAM8E,MAAM,GAAGvB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzCsB,QAAQ,CAACxG,QAAQ,CAAC0G,GAAG,CAACP,QAAQ,CAACnE,CAAC,EAAEyE,MAAM,EAAE,CAACN,QAAQ,CAAClE,CAAC,CAAC;cACtDuE,QAAQ,CAACG,QAAQ,CAAC1E,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG4C,KAAK,CAAClG,OAAO,GAAGqD,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7DlF,KAAK,CAAC4I,GAAG,CAACJ,QAAQ,CAAC;cAEnB7H,aAAa,CAAC+H,GAAG,CAACf,EAAE,EAAE;gBACpBW,KAAK,EAAEE,QAAQ;gBACfK,UAAU,EAAEtB,GAAG;gBACfL,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIoB,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAACtG,QAAQ,CAAC0G,GAAG,CAACP,QAAQ,CAACnE,CAAC,EAAEsE,KAAK,CAACpB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACiB,QAAQ,CAAClE,CAAC,CAAC;cACjFqE,KAAK,CAACA,KAAK,CAACK,QAAQ,CAAC1E,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG4C,KAAK,CAAClG,OAAO,GAAGqD,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEoD,KAAK,CAACO,UAAU,GAAGtB,GAAG;cACtBe,KAAK,CAACA,KAAK,CAACQ,YAAY,CAAC,CAAC;cAC1BR,KAAK,CAACA,KAAK,CAACS,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAMC,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAC9B,YAAY,CAAC+B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxB,SAAS,CAAC,CAAC;QAE9DjH,aAAa,CAAC8G,OAAO,CAAC,CAAC4B,SAAS,EAAE1B,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG8B,SAAS,CAACR,UAAU,GAAGG,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAAC3B,EAAE,CAAC,EAAE;YACzE3H,KAAK,CAACuJ,MAAM,CAACF,SAAS,CAACf,KAAK,CAAC;YAC7B3H,aAAa,CAAC6I,MAAM,CAAC7B,EAAE,CAAC;YACxBvC,OAAO,CAACC,GAAG,CAAC,oBAAoBsC,EAAE,QAAQ0B,SAAS,CAACnC,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAIP,KAAK,KAAK1G,WAAW,CAACM,MAAM,CAACC,GAAG,IAAIuG,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAClE9B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0B,WAAW,CAAC;QAEpC,MAAM0C,OAAO,GAAG1C,WAAW,CAACM,IAAI;QAChC,MAAMqC,QAAQ,GAAG;UACfjI,SAAS,EAAEyE,UAAU,CAACuD,OAAO,CAACE,QAAQ,CAAC;UACvCjI,QAAQ,EAAEwE,UAAU,CAACuD,OAAO,CAACG,OAAO,CAAC;UACrCjI,KAAK,EAAEuE,UAAU,CAACuD,OAAO,CAACxB,SAAS,CAAC;UACpCrG,OAAO,EAAEsE,UAAU,CAACuD,OAAO,CAACvB,WAAW;QACzC,CAAC;QAED9C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqE,QAAQ,CAAC;;QAElC;QACA,IAAItK,gBAAgB,EAAE;UACpB,MAAM+I,QAAQ,GAAGlH,SAAS,CAACwC,OAAO,CAAC2E,YAAY,CAACsB,QAAQ,CAACjI,SAAS,EAAEiI,QAAQ,CAAChI,QAAQ,CAAC;UACtF,MAAMmI,WAAW,GAAG,IAAIrL,KAAK,CAAC4H,OAAO,CAAC+B,QAAQ,CAACnE,CAAC,EAAE,GAAG,EAAE,CAACmE,QAAQ,CAAClE,CAAC,CAAC;;UAEnE;UACA7E,gBAAgB,CAAC4C,QAAQ,CAACwC,IAAI,CAACqF,WAAW,CAAC;UAC3CzK,gBAAgB,CAACuJ,QAAQ,CAAC1E,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGwE,QAAQ,CAAC9H,OAAO,GAAGqD,IAAI,CAACC,EAAE,GAAG,GAAG;UACxE9F,gBAAgB,CAAC0J,YAAY,CAAC,CAAC;UAC/B1J,gBAAgB,CAAC2J,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAvH,eAAe,CAACkI,QAAQ,CAAC;UACzBtE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwE,WAAW,CAAC;QACtC;QACA;MACF;;MAEA;MACAzE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBsB,KAAK;QACLO,IAAI,EAAEH,WAAW,CAACG,IAAI;QACtBG,IAAI,EAAEN;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCzB,OAAO,CAACyB,KAAK,CAAC,SAAS,EAAED,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMgD,cAAc,GAAGA,CAAA,KAAM;IAC3B1E,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM0E,KAAK,GAAG,QAAQ9J,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnE8E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0E,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB9E,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED2E,EAAE,CAACG,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMxD,OAAO,GAAGI,IAAI,CAACC,KAAK,CAACmD,KAAK,CAAC/C,IAAI,CAAC;;QAEtC;QACA,IAAIT,OAAO,CAACM,IAAI,KAAK,SAAS,EAAE;UAC9B9B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEuB,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACM,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIN,OAAO,CAACM,IAAI,KAAK,SAAS,IAAIN,OAAO,CAACD,KAAK,IAAIC,OAAO,CAACyD,OAAO,EAAE;UAClE;UACA3D,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEK,IAAI,CAACsD,SAAS,CAAC1D,OAAO,CAACyD,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOxD,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDmD,EAAE,CAACO,OAAO,GAAI1D,KAAK,IAAK;MACtBzB,OAAO,CAACyB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDmD,EAAE,CAACQ,OAAO,GAAG,MAAM;MACjBpF,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAoF,UAAU,CAACX,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACA1I,aAAa,CAACqC,OAAO,GAAGuG,EAAE;EAC5B,CAAC;EAED3L,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0C,YAAY,CAAC0C,OAAO,EAAE;;IAE3B;IACAiH,aAAa,CAAC,CAAC;;IAEf;IACA1K,KAAK,GAAG,IAAIxB,KAAK,CAACmM,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIpM,KAAK,CAACqM,iBAAiB,CACxC,EAAE,EACF1K,MAAM,CAAC2K,UAAU,GAAG3K,MAAM,CAAC4K,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC5I,QAAQ,CAAC0G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCkC,MAAM,CAAChG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB5B,SAAS,CAACS,OAAO,GAAGmH,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIxM,KAAK,CAACyM,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAChL,MAAM,CAAC2K,UAAU,EAAE3K,MAAM,CAAC4K,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAClL,MAAM,CAACmL,gBAAgB,CAAC;IAC/CvK,YAAY,CAAC0C,OAAO,CAAC8H,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIjN,KAAK,CAACkN,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D1L,KAAK,CAAC4I,GAAG,CAAC6C,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAInN,KAAK,CAACoN,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC3J,QAAQ,CAAC0G,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C1I,KAAK,CAAC4I,GAAG,CAAC+C,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIrN,KAAK,CAACoN,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC7J,QAAQ,CAAC0G,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C1I,KAAK,CAAC4I,GAAG,CAACiD,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAItN,KAAK,CAACuN,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC9J,QAAQ,CAAC0G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCoD,SAAS,CAACE,KAAK,GAAG/G,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B4G,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBnM,KAAK,CAAC4I,GAAG,CAACkD,SAAS,CAAC;;IAEpB;IACAlM,QAAQ,GAAG,IAAIlB,aAAa,CAACkM,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD5L,QAAQ,CAACwM,aAAa,GAAG,IAAI;IAC7BxM,QAAQ,CAACyM,aAAa,GAAG,IAAI;IAC7BzM,QAAQ,CAAC0M,kBAAkB,GAAG,KAAK;IACnC1M,QAAQ,CAACkF,WAAW,GAAG,EAAE;IACzBlF,QAAQ,CAACmF,WAAW,GAAG,GAAG;IAC1BnF,QAAQ,CAACoF,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtCtF,QAAQ,CAACuF,aAAa,GAAG,CAAC;IAC1BvF,QAAQ,CAAC+E,MAAM,CAAC+D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B9I,QAAQ,CAACiF,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBuF,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBhL,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBoD,SAAS,EAAE,CAAC,CAACA,SAAS,CAACS;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM8I,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIlO,UAAU,CAAC,CAAC;QACtCkO,aAAa,CAACC,IAAI,CAChB,GAAGlM,QAAQ,uBAAuB,EACjCmM,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAC7M,KAAK;;UAE/B;UACA,MAAM+M,gBAAgB,GAAG,IAAIvO,KAAK,CAACwO,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI7O,KAAK,CAAC8O,oBAAoB,CAAC;kBACjDC,KAAK,EAAE,QAAQ;kBAAO;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIR,KAAK,CAACE,QAAQ,CAACjE,GAAG,EAAE;kBACtBkE,WAAW,CAAClE,GAAG,GAAG+D,KAAK,CAACE,QAAQ,CAACjE,GAAG;gBACtC;;gBAEA;gBACA+D,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5BjI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6H,KAAK,CAACnH,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM+G,YAAY,CAACa,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMV,KAAK,GAAGJ,YAAY,CAACa,QAAQ,CAAC,CAAC,CAAC;YACtCZ,gBAAgB,CAACnE,GAAG,CAACsE,KAAK,CAAC;UAC7B;;UAEA;UACAlN,KAAK,CAAC4I,GAAG,CAACmE,gBAAgB,CAAC;;UAE3B;UACA3N,gBAAgB,GAAG2N,gBAAgB;UAEnC3H,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B/D,kBAAkB,CAAC,IAAI,CAAC;UACxBmL,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAc,GAAG,IAAK;UACPzI,OAAO,CAACC,GAAG,CAAC,aAAa,CAACwI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDtB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMuB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMlB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAzC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIiD,gBAAgB,EAAE;UACpB,MAAMmB,YAAY,GAAG;YACnBzM,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAMuM,UAAU,GAAGlN,SAAS,CAACwC,OAAO,CAAC2E,YAAY,CAAC8F,YAAY,CAACzM,SAAS,EAAEyM,YAAY,CAACxM,QAAQ,CAAC;UAChG;UACAqL,gBAAgB,CAAC/K,QAAQ,CAAC0G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxCqE,gBAAgB,CAACpE,QAAQ,CAAC1E,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAGgJ,YAAY,CAACtM,OAAO,GAAGqD,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9E6H,gBAAgB,CAACjE,YAAY,CAAC,CAAC;UAC/BiE,gBAAgB,CAAChE,iBAAiB,CAAC,IAAI,CAAC;UACxCtJ,eAAe,GAAGsN,gBAAgB,CAAC/K,QAAQ,CAAC2B,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOkD,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMuH,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI9B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM6B,WAAW,GAAIC,WAAW,IAAK;UACnCpJ,OAAO,CAACC,GAAG,CAAC,WAAWgJ,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAIhQ,UAAU,CAAC,CAAC;UAC/BgQ,MAAM,CAAC7B,IAAI,CACTyB,GAAG,EACFxB,IAAI,IAAK;YACRzH,OAAO,CAACC,GAAG,CAAC,WAAWgJ,GAAG,EAAE,CAAC;YAC7B5B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAgB,GAAG,IAAK;YACPzI,OAAO,CAACC,GAAG,CAAC,SAAS,CAACwI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAnH,KAAK,IAAK;YACTzB,OAAO,CAACyB,KAAK,CAAC,SAASwH,GAAG,EAAE,EAAExH,KAAK,CAAC;YACpC,IAAI2H,WAAW,GAAG,CAAC,EAAE;cACnBpJ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BoF,UAAU,CAAC,MAAM8D,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL9B,MAAM,CAAC7F,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED0H,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAIhQ,UAAU,CAAC,CAAC;IAC/BgQ,MAAM,CAAC7B,IAAI,CACT,GAAGlM,QAAQ,4BAA4B,EACvC,MAAOmM,IAAI,IAAK;MACd,IAAI;QACF,MAAMvE,KAAK,GAAGuE,IAAI,CAAC7M,KAAK;QACxBsI,KAAK,CAACoG,KAAK,CAAChG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBJ,KAAK,CAACtG,QAAQ,CAAC0G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3B1I,KAAK,CAAC4I,GAAG,CAACN,KAAK,CAAC;;QAEhB;QACA,MAAM2F,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOpH,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAgH,GAAG,IAAK;MACPzI,OAAO,CAACC,GAAG,CAAC,SAAS,CAACwI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAnH,KAAK,IAAK;MACTzB,OAAO,CAACyB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BzB,OAAO,CAACyB,KAAK,CAAC,OAAO,EAAE;QACrB8H,IAAI,EAAE9H,KAAK,CAACK,IAAI;QAChB0H,IAAI,EAAE/H,KAAK,CAACD,OAAO;QACnBiI,KAAK,EAAE,GAAGnO,QAAQ,4BAA4B;QAC9CoO,KAAK,EAAE,GAAGpO,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMqO,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACAnQ,KAAK,CAACiG,MAAM,CAAC,CAAC;MAEd,IAAIlF,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAAC2D,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM0L,UAAU,GAAG7P,gBAAgB,CAAC4C,QAAQ,CAAC2B,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMuL,eAAe,GAAG9P,gBAAgB,CAACuJ,QAAQ,CAAC1E,CAAC;;QAEnD;QACA;QACA,MAAMkL,gBAAgB,GAAG,EAAED,eAAe,GAAGjK,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMkK,YAAY,GAAG,IAAI5Q,KAAK,CAAC4H,OAAO,CACpC,CAAC,EAAE,GAAGnB,IAAI,CAACoK,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAGlK,IAAI,CAACqK,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAvE,MAAM,CAAC5I,QAAQ,CAACwC,IAAI,CAACyK,UAAU,CAAC,CAACrG,GAAG,CAACwG,YAAY,CAAC;;QAElD;QACAxE,MAAM,CAAC/G,EAAE,CAAC6E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAM6G,YAAY,GAAGN,UAAU,CAACtL,KAAK,CAAC,CAAC;QACvCiH,MAAM,CAAChG,MAAM,CAAC2K,YAAY,CAAC;;QAE3B;QACA3E,MAAM,CAAC4E,sBAAsB,CAAC,CAAC;QAC/B5E,MAAM,CAAC9B,YAAY,CAAC,CAAC;QACrB8B,MAAM,CAAC7B,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAnJ,QAAQ,CAAC2D,OAAO,GAAG,KAAK;;QAExB;QACA3D,QAAQ,CAAC+E,MAAM,CAACH,IAAI,CAACyK,UAAU,CAAC;QAChCrP,QAAQ,CAACiF,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBoK,IAAI,EAAER,UAAU,CAACS,OAAO,CAAC,CAAC;UAC1BC,IAAI,EAAE/E,MAAM,CAAC5I,QAAQ,CAAC0N,OAAO,CAAC,CAAC;UAC/BE,IAAI,EAAEL,YAAY,CAACG,OAAO,CAAC,CAAC;UAC5BG,IAAI,EAAEjF,MAAM,CAACkF,iBAAiB,CAAC,IAAItR,KAAK,CAAC4H,OAAO,CAAC,CAAC,CAAC,CAACsJ,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI/P,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAAC2D,OAAO,GAAG,IAAI;;QAEvB;QACAqH,MAAM,CAAC/G,EAAE,CAAC6E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIzD,IAAI,CAAC8K,GAAG,CAACnF,MAAM,CAAC5I,QAAQ,CAACiC,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC2G,MAAM,CAAC5I,QAAQ,CAAC0G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9B9I,QAAQ,CAAC+E,MAAM,CAAC+D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BkC,MAAM,CAAChG,MAAM,CAAChF,QAAQ,CAAC+E,MAAM,CAAC;UAC9B/E,QAAQ,CAACiF,MAAM,CAAC,CAAC;QACnB;MACF;MAEAjF,QAAQ,CAACiF,MAAM,CAAC,CAAC;MACjBmG,QAAQ,CAACgF,MAAM,CAAChQ,KAAK,EAAE4K,MAAM,CAAC;IAChC,CAAC;IAEDmE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzBrF,MAAM,CAACsF,MAAM,GAAG/P,MAAM,CAAC2K,UAAU,GAAG3K,MAAM,CAAC4K,WAAW;MACtDH,MAAM,CAAC4E,sBAAsB,CAAC,CAAC;MAC/BxE,QAAQ,CAACG,OAAO,CAAChL,MAAM,CAAC2K,UAAU,EAAE3K,MAAM,CAAC4K,WAAW,CAAC;IACzD,CAAC;IACD5K,MAAM,CAACgQ,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA9P,MAAM,CAACiQ,aAAa,GAAG,MAAM;MAC3B,IAAIpN,SAAS,CAACS,OAAO,EAAE;QACrBT,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAAC0G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC1F,SAAS,CAACS,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC5B,SAAS,CAACS,OAAO,CAACqF,YAAY,CAAC,CAAC;QAChC9F,SAAS,CAACS,OAAO,CAACsF,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAInJ,QAAQ,EAAE;UACZA,QAAQ,CAAC+E,MAAM,CAAC+D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B9I,QAAQ,CAAC2D,OAAO,GAAG,IAAI;UACvB3D,QAAQ,CAACiF,MAAM,CAAC,CAAC;QACnB;QAEAlF,UAAU,GAAG,QAAQ;QACrByF,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAAgL,qBAAA;MACX,IAAI9Q,oBAAoB,EAAE;QACxB+Q,aAAa,CAAC/Q,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACA,IAAI6B,aAAa,CAACqC,OAAO,EAAE;QACzBrC,aAAa,CAACqC,OAAO,CAAC8M,GAAG,CAAC,CAAC;MAC7B;MACApQ,MAAM,CAACqQ,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAAtP,YAAY,CAAC0C,OAAO,cAAA4M,qBAAA,uBAApBA,qBAAA,CAAsBI,WAAW,CAACzF,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAAC0F,OAAO,CAAC,CAAC;;MAElB;MACA/P,aAAa,CAAC8G,OAAO,CAAC,CAACa,KAAK,EAAEX,EAAE,KAAK;QACnC3H,KAAK,CAACuJ,MAAM,CAACjB,KAAK,CAAC;MACrB,CAAC,CAAC;MACF3H,aAAa,CAACgQ,KAAK,CAAC,CAAC;;MAErB;MACA,OAAM3Q,KAAK,CAAC2N,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/B5N,KAAK,CAACuJ,MAAM,CAACvJ,KAAK,CAAC2N,QAAQ,CAAC,CAAC,CAAC,CAAC;MACjC;MAEA3N,KAAK,GAAG,IAAI,CAAC,CAAC;MACdH,qBAAqB,GAAG,IAAI,CAAC,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA,CAAAE,SAAA;IAAAwO,QAAA,gBACE1O,OAAA,CAACH,MAAM;MACL8R,KAAK,EAAEzN,uBAAwB;MAC/B0N,WAAW,EAAC,0BAAM;MAClBC,QAAQ,EAAErL,wBAAyB;MACnCsL,OAAO,EAAEhS,iBAAiB,CAAC6G,aAAa,CAACuD,GAAG,CAACxD,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACI,IAAI;QACxBiL,KAAK,EAAErL,YAAY,CAACI;MACtB,CAAC,CAAC;IAAE;MAAAkL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACFnS,OAAA;MAAKoS,GAAG,EAAEtQ,YAAa;MAAC6P,KAAK,EAAE;QAAEvN,KAAK,EAAE,MAAM;QAAEoF,MAAM,EAAE;MAAO;IAAE;MAAAwI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpEnS,OAAA;MAAK2R,KAAK,EAAE7O,oBAAqB;MAAA4L,QAAA,gBAC/B1O,OAAA;QACE2R,KAAK,EAAE;UACL,GAAGrO,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E0L,KAAK,EAAE1L,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFyP,OAAO,EAAEhO,kBAAmB;QAAAqK,QAAA,EAC7B;MAED;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnS,OAAA;QACE2R,KAAK,EAAE;UACL,GAAGrO,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E0L,KAAK,EAAE1L,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFyP,OAAO,EAAE9N,kBAAmB;QAAAmK,QAAA,EAC7B;MAED;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAtQ,EAAA,CA1wBMD,WAAW;AAAA0Q,EAAA,GAAX1Q,WAAW;AA2wBjB,SAAS2Q,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACrO,KAAK,GAAG,GAAG;EAClBqO,MAAM,CAACjJ,MAAM,GAAG,EAAE;;EAElB;EACAoJ,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACrO,KAAK,GAAC,CAAC,EAAEqO,MAAM,CAACjJ,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAM0J,OAAO,GAAG,IAAI3T,KAAK,CAAC4T,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAI7T,KAAK,CAAC8T,cAAc,CAAC;IAC9CnJ,GAAG,EAAEgJ,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAIhU,KAAK,CAACiU,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAC9D,KAAK,CAAChG,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAO8J,MAAM;AACf;;AAEA;AACArS,MAAM,CAACuS,WAAW,GAAG,CAAC1O,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAI9E,gBAAgB,EAAE;IACpBA,gBAAgB,CAAC4C,QAAQ,CAAC0G,GAAG,CAAC1E,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtC9E,gBAAgB,CAAC0J,YAAY,CAAC,CAAC;IAC/B1J,gBAAgB,CAAC2J,iBAAiB,CAAC,IAAI,CAAC;IACxC3D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA/D,MAAM,CAACwS,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM/H,MAAM,GAAG+G,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIlI,MAAM,EAAE;MACV;MACA,MAAMmI,MAAM,GAAGnI,MAAM,CAAC5I,QAAQ,CAAC2B,KAAK,CAAC,CAAC;;MAEtC;MACAiH,MAAM,CAAC5I,QAAQ,CAAC0G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BkC,MAAM,CAAC/G,EAAE,CAAC6E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBkC,MAAM,CAAChG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAgG,MAAM,CAAC9B,YAAY,CAAC,CAAC;MACrB8B,MAAM,CAAC7B,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMnJ,QAAQ,GAAG+R,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIpT,QAAQ,EAAE;QACZA,QAAQ,CAAC+E,MAAM,CAAC+D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B9I,QAAQ,CAACiF,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB4N,GAAG,EAAEF,MAAM,CAACrD,OAAO,CAAC,CAAC;QACrBwD,GAAG,EAAEtI,MAAM,CAAC5I,QAAQ,CAAC0N,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOyD,CAAC,EAAE;IACV/N,OAAO,CAACyB,KAAK,CAAC,YAAY,EAAEsM,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMzI,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFtF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMoJ,MAAM,GAAG,IAAIhQ,UAAU,CAAC,CAAC;;IAE/B;IACA,MAAM,CAAC2U,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAM9G,OAAO,CAAC+G,GAAG,CAAC,CAC/D9E,MAAM,CAAC+E,SAAS,CAAC,GAAG9S,QAAQ,uBAAuB,CAAC,EACpD+N,MAAM,CAAC+E,SAAS,CAAC,GAAG9S,QAAQ,uBAAuB,CAAC,EACpD+N,MAAM,CAAC+E,SAAS,CAAC,GAAG9S,QAAQ,sBAAsB,CAAC,CACpD,CAAC;;IAEF;IACAb,qBAAqB,GAAGuT,WAAW,CAACpT,KAAK;IACzCH,qBAAqB,CAACoN,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;QAChBD,KAAK,CAACE,QAAQ,GAAG,IAAI5O,KAAK,CAAC8O,oBAAoB,CAAC;UAC9CC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,GAAG;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC;;QAEF;QACA;QACA;QACA;MACF;IACF,CAAC,CAAC;;IAEF;IACA5N,qBAAqB,GAAGuT,WAAW,CAACrT,KAAK;IACzC;IACAF,qBAAqB,CAAC4O,KAAK,CAAChG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxC;IACA5I,qBAAqB,CAACmN,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,SAAS,GAAG,GAAG;QAC9BP,KAAK,CAACE,QAAQ,CAACM,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;;IAEF;IACA3N,oBAAoB,GAAGuT,UAAU,CAACtT,KAAK;IACvC;IACAD,oBAAoB,CAAC2O,KAAK,CAAChG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC;IACA3I,oBAAoB,CAACkN,QAAQ,CAAEC,KAAK,IAAK;MACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,SAAS,GAAG,GAAG;QAC9BP,KAAK,CAACE,QAAQ,CAACM,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;IAEFtI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOwB,KAAK,EAAE;IACdzB,OAAO,CAACyB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;AAED,eAAehG,WAAW;AAAC,IAAA0Q,EAAA;AAAAkC,YAAA,CAAAlC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}