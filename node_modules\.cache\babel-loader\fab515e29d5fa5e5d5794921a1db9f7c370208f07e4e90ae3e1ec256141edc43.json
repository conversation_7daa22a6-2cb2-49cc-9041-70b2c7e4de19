{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\nlet trafficParticipants = new Map(); // 使用 Map 存储所有交通参与者\nlet participantMeshes = new Map(); // 存储交通参与者的 3D 模型\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  wsUrl: 'ws://localhost:8083/mqtt',\n  // WebSocket 服务器地址\n  username: 'cloud',\n  password: 'A825FBCBB97D1975',\n  clientId: 'V2X_CENTER_CSLG_opentest',\n  topics: ['changli/cloud/v2x/rsu/rsm', 'changli/cloud/v2x/obu/bsm']\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 加载车辆模型\nconst loadVehicleModel = (url, position, heading) => {\n  return new Promise((resolve, reject) => {\n    const loader = new GLTFLoader();\n    loader.load(url, gltf => {\n      try {\n        const model = gltf.scene;\n\n        // 调整模型材质\n        model.traverse(child => {\n          if (child.isMesh) {\n            child.material = new THREE.MeshStandardMaterial({\n              color: 0xffffff,\n              metalness: 0.2,\n              roughness: 0.1,\n              envMapIntensity: 1.0\n            });\n          }\n        });\n\n        // 设置位置和朝向\n        model.position.copy(position);\n        model.rotation.y = Math.PI - heading * Math.PI / 180;\n        resolve(model);\n      } catch (error) {\n        reject(error);\n      }\n    }, undefined, reject);\n  });\n};\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const rendererRef = useRef(null);\n  const controlsRef = useRef(null);\n  const converterRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [pendingMessages, setPendingMessages] = useState([]);\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    const wsUrl = `ws://${window.location.hostname}:8083/mqtt`;\n    console.log('正在连接WebSocket...', wsUrl);\n    try {\n      if (mqttClientRef.current) {\n        console.log('关闭现有WebSocket连接');\n        mqttClientRef.current.close();\n      }\n      const ws = new WebSocket(wsUrl);\n      ws.onopen = () => {\n        console.log('WebSocket连接成功');\n        // 发送认证信息\n        ws.send(JSON.stringify({\n          type: 'auth',\n          clientId: MQTT_CONFIG.clientId,\n          username: MQTT_CONFIG.username,\n          password: MQTT_CONFIG.password\n        }));\n      };\n      ws.onclose = event => {\n        console.log('WebSocket连接关闭:', {\n          code: event.code,\n          reason: event.reason,\n          wasClean: event.wasClean\n        });\n\n        // 尝试重新连接\n        setTimeout(() => {\n          console.log('尝试重新连接...');\n          initMqttClient();\n        }, 5000);\n      };\n      ws.onerror = error => {\n        console.error('WebSocket错误:', {\n          error: error.message || '未知错误',\n          readyState: ws.readyState,\n          url: wsUrl\n        });\n      };\n      ws.onmessage = event => {\n        try {\n          if (!event.data) {\n            console.warn('收到空消息');\n            return;\n          }\n          const message = JSON.parse(event.data);\n          if (!message.topic || !message.message) {\n            console.warn('无效的消息格式:', message);\n            return;\n          }\n          let data;\n          try {\n            data = JSON.parse(message.message);\n          } catch (error) {\n            console.error('解析消息内容失败:', {\n              error: error.message,\n              message: message.message\n            });\n            return;\n          }\n          switch (message.topic) {\n            case 'changli/cloud/v2x/rsu/rsm':\n              if (!isInitialized) {\n                console.warn('场景未初始化完成，跳过RSM消息');\n                return;\n              }\n              handleRSMMessage(data);\n              break;\n            case 'changli/cloud/v2x/obu/bsm':\n              handleBSMMessage(data);\n              break;\n            default:\n              console.warn('未知的消息主题:', message.topic);\n          }\n        } catch (error) {\n          console.error('处理WebSocket消息失败:', error);\n        }\n      };\n      mqttClientRef.current = ws;\n    } catch (error) {\n      console.error('创建WebSocket连接失败:', {\n        error: error.message,\n        stack: error.stack,\n        wsUrl\n      });\n    }\n  };\n\n  // 处理 BSM 消息\n  const handleBSMMessage = data => {\n    if (data.type === 'BSM' && data.data) {\n      const bsmData = data.data;\n      const newState = {\n        longitude: parseFloat(bsmData.partLong),\n        latitude: parseFloat(bsmData.partLat),\n        speed: parseFloat(bsmData.partSpeed),\n        heading: parseFloat(bsmData.partHeading)\n      };\n      console.log('解析后的车辆状态:', newState);\n\n      // 更新车辆位置和状态\n      if (globalVehicleRef) {\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        globalVehicleRef.position.copy(newPosition);\n        globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n        globalVehicleRef.updateMatrix();\n        globalVehicleRef.updateMatrixWorld(true);\n        setVehicleState(newState);\n        console.log('车辆位置已更新:', newPosition);\n      }\n    }\n  };\n\n  // 处理 RSM 消息\n  const handleRSMMessage = data => {\n    try {\n      // 验证消息格式\n      if (!data || !data.data || !Array.isArray(data.data.participants)) {\n        console.warn('无效的RSM消息格式:', data);\n        return;\n      }\n\n      // 打印基本信息\n      console.log('RSM消息:', {\n        设备ID: data.data.rsuId,\n        时间戳: data.tm,\n        消息来源: data.source,\n        基准点: {\n          经度: data.data.posLong,\n          纬度: data.data.posLat\n        },\n        参与者数量: data.data.participants.length\n      });\n\n      // 打印每个参与者的信息\n      data.data.participants.forEach((participant, index) => {\n        console.log(`参与者 ${index + 1}:`, {\n          ID: participant.partPtcId,\n          类型: {\n            原始值: participant.partPtcType,\n            含义: getParticipantTypeName(participant.partPtcType)\n          },\n          尺寸: {\n            长: `${participant.partLength}cm`,\n            宽: `${participant.partWidth}cm`,\n            高: `${participant.partHeight}cm`,\n            类型: getParticipantSizeTypeName(participant.partSizeType)\n          },\n          位置: {\n            经度: participant.partPosLong,\n            纬度: participant.partPosLat\n          },\n          运动: {\n            速度: `${participant.partSpeed}m/s`,\n            航向: `${participant.partHeading}°`\n          },\n          时间: participant.partSecmark\n        });\n      });\n    } catch (error) {\n      console.error('处理RSM消息失败:', {\n        错误: error.message,\n        数据: data\n      });\n    }\n  };\n\n  // 获取参与者类型名称\n  const getParticipantTypeName = type => {\n    const types = {\n      '0': '未知',\n      '1': '机动车',\n      '2': '非机动车',\n      '3': '行人'\n    };\n    return types[type] || '未知';\n  };\n\n  // 获取参与者尺寸类型名称\n  const getParticipantSizeTypeName = sizeType => {\n    const types = {\n      '1': '小型车(长度<600cm)',\n      '2': '中型车(600cm≤长度<1300cm)',\n      '3': '大型车(长度≥1300cm)'\n    };\n    return types[sizeType] || '未知';\n  };\n\n  // 更新交通参与者\n  const updateTrafficParticipant = async participant => {\n    if (!sceneRef.current || !converterRef.current) {\n      console.warn('场景或坐标转换器未初始化');\n      return;\n    }\n    const id = participant.partPtcId;\n    const type = parseInt(participant.partPtcType);\n    const sizeType = parseInt(participant.partSizeType || '1');\n    try {\n      // 转换坐标\n      const modelPos = converterRef.current.wgs84ToModel(parseFloat(participant.partPosLong), parseFloat(participant.partPosLat));\n\n      // 设置不同类型参与者的默认尺寸和颜色\n      const participantConfig = {\n        1: {\n          // 机动车\n          model: 'vehicle',\n          size: {\n            // 根据 partSizeType 设置不同尺寸\n            1: {\n              length: 4.5,\n              width: 1.8,\n              height: 1.5\n            },\n            // 小车\n            2: {\n              length: 8.0,\n              width: 2.3,\n              height: 2.5\n            },\n            // 中型车\n            3: {\n              length: 12.0,\n              width: 2.5,\n              height: 3.0\n            } // 大型车\n          },\n          color: 0x4444ff,\n          scale: 0.7\n        },\n        2: {\n          // 非机动车\n          model: 'box',\n          size: {\n            length: 1.8,\n            width: 0.6,\n            height: 1.2\n          },\n          color: 0x44ff44\n        },\n        3: {\n          // 行人\n          model: 'cylinder',\n          size: {\n            length: 0.5,\n            width: 0.5,\n            height: 1.7\n          },\n          color: 0xff4444\n        }\n      };\n      const config = participantConfig[type] || participantConfig[1];\n      // 获取实际尺寸（单位转换：cm -> m）\n      const actualSize = {\n        length: parseFloat(participant.partLength || '0') / 100,\n        width: parseFloat(participant.partWidth || '0') / 100,\n        height: parseFloat(participant.partHeight || '0') / 100\n      };\n\n      // 使用实际尺寸或默认尺寸\n      const size = {\n        length: actualSize.length > 0 ? actualSize.length : type === 1 ? config.size[sizeType].length : config.size.length,\n        width: actualSize.width > 0 ? actualSize.width : type === 1 ? config.size[sizeType].width : config.size.width,\n        height: actualSize.height > 0 ? actualSize.height : type === 1 ? config.size[sizeType].height : config.size.height\n      };\n\n      // 创建或更新参与者数据\n      const participantData = {\n        position: new THREE.Vector3(modelPos.x, 0.5, -modelPos.y),\n        heading: parseFloat(participant.partHeading || 0),\n        speed: parseFloat(participant.partSpeed || 0),\n        type: type,\n        sizeType: sizeType,\n        config: config,\n        size: size,\n        lastUpdate: Date.now()\n      };\n\n      // 更新或创建3D模型\n      if (!participantMeshes.has(id)) {\n        const mesh = await createParticipantMesh(participantData);\n        if (mesh) {\n          mesh.name = `participant_${id}_${type}_${sizeType}`;\n          sceneRef.current.add(mesh);\n          participantMeshes.set(id, mesh);\n          console.log('创建新参与者:', {\n            ID: id,\n            类型: type,\n            尺寸类型: sizeType,\n            实际尺寸: size,\n            位置: modelPos,\n            速度: participantData.speed\n          });\n        }\n      } else {\n        const mesh = participantMeshes.get(id);\n        updateParticipantMesh(mesh, participantData);\n      }\n      trafficParticipants.set(id, participantData);\n    } catch (error) {\n      console.error('更新交通参与者失败:', {\n        错误: error.message,\n        参与者ID: id,\n        类型: type,\n        尺寸类型: sizeType,\n        原始数据: participant\n      });\n    }\n  };\n\n  // 创建交通参与者的3D模型\n  const createParticipantMesh = async data => {\n    let mesh;\n    try {\n      switch (data.config.model) {\n        case 'vehicle':\n          try {\n            // 使用现有的车辆模型\n            const vehicleModel = await loadVehicleModel(`${BASE_URL}/changli2/Audi R8.glb`, data.position, data.heading);\n            mesh = new THREE.Group();\n            mesh.add(vehicleModel);\n            vehicleModel.scale.set(data.config.scale, data.config.scale, data.config.scale);\n          } catch (error) {\n            console.error('加载车辆模型失败:', error);\n            // 使用备用几何体\n            const geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n            const material = new THREE.MeshPhongMaterial({\n              color: data.config.color,\n              transparent: true,\n              opacity: 0.8\n            });\n            mesh = new THREE.Mesh(geometry, material);\n          }\n          break;\n        case 'box':\n          // 非机动车使用长方体\n          const geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n          const material = new THREE.MeshPhongMaterial({\n            color: data.config.color,\n            transparent: true,\n            opacity: 0.8\n          });\n          mesh = new THREE.Mesh(geometry, material);\n          break;\n        case 'cylinder':\n          // 行人使用圆柱体\n          const cylGeometry = new THREE.CylinderGeometry(0.25, 0.25, data.size.height, 8);\n          const cylMaterial = new THREE.MeshPhongMaterial({\n            color: data.config.color,\n            transparent: true,\n            opacity: 0.8\n          });\n          mesh = new THREE.Mesh(cylGeometry, cylMaterial);\n          break;\n      }\n      if (mesh) {\n        mesh.position.copy(data.position);\n        mesh.rotation.y = Math.PI - data.heading * Math.PI / 180;\n        mesh.castShadow = true;\n        mesh.receiveShadow = true;\n      }\n      return mesh;\n    } catch (error) {\n      console.error('创建参与者模型失败:', error);\n      return null;\n    }\n  };\n\n  // 更新交通参与者的 3D 模型\n  const updateParticipantMesh = (mesh, data) => {\n    // 使用 TWEEN 创建平滑过渡\n    new TWEEN.Tween(mesh.position).to(data.position, 100).easing(TWEEN.Easing.Linear.None).start();\n    new TWEEN.Tween(mesh.rotation).to({\n      y: Math.PI - data.heading * Math.PI / 180\n    }, 100).easing(TWEEN.Easing.Linear.None).start();\n  };\n\n  // 初始化场景\n  useEffect(() => {\n    const initScene = async () => {\n      try {\n        console.log('开始初始化场景...');\n\n        // 创建场景\n        const scene = new THREE.Scene();\n        sceneRef.current = scene;\n        scene.background = new THREE.Color(0xf0f0f0);\n\n        // 创建相机\n        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);\n        cameraRef.current = camera;\n        camera.position.set(0, 300, 0);\n\n        // 创建渲染器\n        const renderer = new THREE.WebGLRenderer({\n          antialias: true\n        });\n        rendererRef.current = renderer;\n        renderer.setSize(window.innerWidth, window.innerHeight);\n        renderer.shadowMap.enabled = true;\n        containerRef.current.appendChild(renderer.domElement);\n\n        // 创建控制器\n        const controls = new OrbitControls(camera, renderer.domElement);\n        controlsRef.current = controls;\n        controls.enableDamping = true;\n        controls.dampingFactor = 0.05;\n\n        // 初始化坐标转换器\n        converterRef.current = new CoordinateConverter();\n        await converterRef.current.initialize();\n\n        // 加载初始车辆模型\n        const vehicleModel = await loadVehicleModel(`${BASE_URL}/changli2/Audi R8.glb`, new THREE.Vector3(0, 0, 0), 0);\n        scene.add(vehicleModel);\n        setIsVehicleLoaded(true);\n\n        // 初始化完成\n        setIsInitialized(true);\n        console.log('场景初始化完成');\n\n        // 初始化 WebSocket 连接\n        initMqttClient();\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n    initScene();\n  }, []);\n\n  // 处理 WebSocket 消息\n  const handleWebSocketMessage = message => {\n    try {\n      // 处理连接确认消息\n      if (message.type === 'connection') {\n        console.log('WebSocket连接确认:', message);\n        return;\n      }\n\n      // 处理普通消息\n      if (!message.topic || !message.message) {\n        console.warn('无效的消息格式:', message);\n        return;\n      }\n      let data;\n      try {\n        data = JSON.parse(message.message);\n      } catch (error) {\n        console.error('解析消息内容失败:', {\n          error: error.message,\n          message: message.message\n        });\n        return;\n      }\n\n      // 如果场景未初始化完成，暂存消息\n      if (!isInitialized) {\n        console.log('场景未初始化完成，暂存消息');\n        setPendingMessages(prev => [...prev, {\n          topic: message.topic,\n          data\n        }]);\n        return;\n      }\n\n      // 处理消息\n      switch (message.topic) {\n        case 'changli/cloud/v2x/rsu/rsm':\n          handleRSMMessage(data);\n          break;\n        case 'changli/cloud/v2x/obu/bsm':\n          handleBSMMessage(data);\n          break;\n        default:\n          console.warn('未知的消息主题:', message.topic);\n      }\n    } catch (error) {\n      console.error('处理WebSocket消息失败:', error);\n    }\n  };\n\n  // 监听初始化状态，处理暂存的消息\n  useEffect(() => {\n    if (isInitialized && pendingMessages.length > 0) {\n      console.log('处理暂存的消息:', pendingMessages.length);\n      pendingMessages.forEach(msg => {\n        handleRSMMessage(msg.data);\n      });\n      setPendingMessages([]);\n    }\n  }, [isInitialized]);\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        cameraRef.current.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        cameraRef.current.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        cameraRef.current.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        cameraRef.current.updateProjectionMatrix();\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: cameraRef.current.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: cameraRef.current.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        cameraRef.current.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(cameraRef.current.position.y) < 50) {\n          cameraRef.current.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n      rendererRef.current.render(sceneRef.current, cameraRef.current);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      cameraRef.current.aspect = window.innerWidth / window.innerHeight;\n      cameraRef.current.updateProjectionMatrix();\n      rendererRef.current.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        console.log('关闭WebSocket连接');\n        mqttClientRef.current.close();\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(rendererRef.current.domElement);\n      rendererRef.current.dispose();\n      // 清理所有参与者模型\n      participantMeshes.forEach(mesh => {\n        sceneRef.current.remove(mesh);\n        mesh.geometry.dispose();\n        mesh.material.dispose();\n      });\n      participantMeshes.clear();\n      trafficParticipants.clear();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 860,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 862,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 861,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"eX+Lc6vG+Fiu30/dUiLb3EMjWYM=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "trafficParticipants", "Map", "participant<PERSON><PERSON><PERSON>", "MQTT_CONFIG", "wsUrl", "username", "password", "clientId", "topics", "BASE_URL", "loadVehicleModel", "url", "position", "heading", "Promise", "resolve", "reject", "loader", "load", "gltf", "model", "scene", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "copy", "rotation", "y", "Math", "PI", "error", "undefined", "CampusModel", "_s", "containerRef", "sceneRef", "cameraRef", "rendererRef", "controlsRef", "converterRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "isInitialized", "setIsInitialized", "pendingMessages", "setPendingMessages", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "viewMode", "setViewMode", "buttonContainerStyle", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "initMqttClient", "window", "location", "hostname", "close", "ws", "WebSocket", "onopen", "send", "JSON", "stringify", "type", "onclose", "event", "code", "reason", "<PERSON><PERSON><PERSON>", "setTimeout", "onerror", "message", "readyState", "onmessage", "data", "warn", "parse", "topic", "handleRSMMessage", "handleBSMMessage", "stack", "bsmData", "newState", "parseFloat", "partLong", "partLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "newPosition", "Vector3", "updateMatrix", "updateMatrixWorld", "Array", "isArray", "participants", "设备ID", "rsuId", "时间戳", "tm", "消息来源", "source", "基准点", "经度", "posLong", "纬度", "posLat", "参与者数量", "length", "for<PERSON>ach", "participant", "index", "ID", "partPtcId", "类型", "原始值", "partPtcType", "含义", "getParticipantTypeName", "尺寸", "长", "partLength", "宽", "partWidth", "高", "partHeight", "getParticipantSizeTypeName", "partSizeType", "位置", "partPosLong", "partPosLat", "运动", "速度", "航向", "时间", "partSecmark", "错误", "数据", "types", "sizeType", "updateTrafficParticipant", "id", "parseInt", "participantConfig", "size", "width", "height", "scale", "config", "actualSize", "participantData", "lastUpdate", "Date", "now", "has", "mesh", "createParticipantMesh", "name", "add", "set", "尺寸类型", "实际尺寸", "get", "updateParticipantMesh", "参与者ID", "原始数据", "vehicleModel", "Group", "geometry", "BoxGeometry", "MeshPhongMaterial", "transparent", "opacity", "<PERSON><PERSON>", "cylGeometry", "CylinderGeometry", "cylMaterial", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "Linear", "None", "initScene", "Scene", "background", "Color", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "shadowMap", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "enableDamping", "dampingFactor", "initialize", "handleWebSocketMessage", "prev", "msg", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "toArray", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "clearInterval", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "remove", "clear", "children", "ref", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "map", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\nlet trafficParticipants = new Map();  // 使用 Map 存储所有交通参与者\nlet participantMeshes = new Map();    // 存储交通参与者的 3D 模型\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  wsUrl: 'ws://localhost:8083/mqtt',  // WebSocket 服务器地址\n  username: 'cloud',\n  password: 'A825FBCBB97D1975',\n  clientId: 'V2X_CENTER_CSLG_opentest',\n  topics: [\n    'changli/cloud/v2x/rsu/rsm',\n    'changli/cloud/v2x/obu/bsm'\n  ]\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 加载车辆模型\nconst loadVehicleModel = (url, position, heading) => {\n  return new Promise((resolve, reject) => {\n    const loader = new GLTFLoader();\n    loader.load(\n      url,\n      (gltf) => {\n        try {\n          const model = gltf.scene;\n          \n          // 调整模型材质\n          model.traverse((child) => {\n            if (child.isMesh) {\n              child.material = new THREE.MeshStandardMaterial({\n                color: 0xffffff,\n                metalness: 0.2,\n                roughness: 0.1,\n                envMapIntensity: 1.0\n              });\n            }\n          });\n          \n          // 设置位置和朝向\n          model.position.copy(position);\n          model.rotation.y = Math.PI - heading * Math.PI / 180;\n          \n          resolve(model);\n        } catch (error) {\n          reject(error);\n        }\n      },\n      undefined,\n      reject\n    );\n  });\n};\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const rendererRef = useRef(null);\n  const controlsRef = useRef(null);\n  const converterRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [pendingMessages, setPendingMessages] = useState([]);\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    const wsUrl = `ws://${window.location.hostname}:8083/mqtt`;\n    console.log('正在连接WebSocket...', wsUrl);\n    \n    try {\n      if (mqttClientRef.current) {\n        console.log('关闭现有WebSocket连接');\n        mqttClientRef.current.close();\n      }\n\n      const ws = new WebSocket(wsUrl);\n      \n      ws.onopen = () => {\n        console.log('WebSocket连接成功');\n        // 发送认证信息\n        ws.send(JSON.stringify({\n          type: 'auth',\n          clientId: MQTT_CONFIG.clientId,\n          username: MQTT_CONFIG.username,\n          password: MQTT_CONFIG.password\n        }));\n      };\n\n      ws.onclose = (event) => {\n        console.log('WebSocket连接关闭:', {\n          code: event.code,\n          reason: event.reason,\n          wasClean: event.wasClean\n        });\n        \n        // 尝试重新连接\n        setTimeout(() => {\n          console.log('尝试重新连接...');\n          initMqttClient();\n        }, 5000);\n      };\n\n      ws.onerror = (error) => {\n        console.error('WebSocket错误:', {\n          error: error.message || '未知错误',\n          readyState: ws.readyState,\n          url: wsUrl\n        });\n      };\n\n      ws.onmessage = (event) => {\n        try {\n          if (!event.data) {\n            console.warn('收到空消息');\n            return;\n          }\n\n          const message = JSON.parse(event.data);\n          if (!message.topic || !message.message) {\n            console.warn('无效的消息格式:', message);\n            return;\n          }\n\n          let data;\n          try {\n            data = JSON.parse(message.message);\n          } catch (error) {\n            console.error('解析消息内容失败:', {\n              error: error.message,\n              message: message.message\n            });\n            return;\n          }\n\n          switch(message.topic) {\n            case 'changli/cloud/v2x/rsu/rsm':\n              if (!isInitialized) {\n                console.warn('场景未初始化完成，跳过RSM消息');\n                return;\n              }\n              handleRSMMessage(data);\n              break;\n            case 'changli/cloud/v2x/obu/bsm':\n              handleBSMMessage(data);\n              break;\n            default:\n              console.warn('未知的消息主题:', message.topic);\n          }\n        } catch (error) {\n          console.error('处理WebSocket消息失败:', error);\n        }\n      };\n\n      mqttClientRef.current = ws;\n    } catch (error) {\n      console.error('创建WebSocket连接失败:', {\n        error: error.message,\n        stack: error.stack,\n        wsUrl\n      });\n    }\n  };\n\n  // 处理 BSM 消息\n  const handleBSMMessage = (data) => {\n    if (data.type === 'BSM' && data.data) {\n      const bsmData = data.data;\n      const newState = {\n        longitude: parseFloat(bsmData.partLong),\n        latitude: parseFloat(bsmData.partLat),\n        speed: parseFloat(bsmData.partSpeed),\n        heading: parseFloat(bsmData.partHeading)\n      };\n      \n      console.log('解析后的车辆状态:', newState);\n      \n      // 更新车辆位置和状态\n      if (globalVehicleRef) {\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        \n        globalVehicleRef.position.copy(newPosition);\n        globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n        globalVehicleRef.updateMatrix();\n        globalVehicleRef.updateMatrixWorld(true);\n        \n        setVehicleState(newState);\n        console.log('车辆位置已更新:', newPosition);\n      }\n    }\n  };\n\n  // 处理 RSM 消息\n  const handleRSMMessage = (data) => {\n    try {\n      // 验证消息格式\n      if (!data || !data.data || !Array.isArray(data.data.participants)) {\n        console.warn('无效的RSM消息格式:', data);\n        return;\n      }\n\n      // 打印基本信息\n      console.log('RSM消息:', {\n        设备ID: data.data.rsuId,\n        时间戳: data.tm,\n        消息来源: data.source,\n        基准点: {\n          经度: data.data.posLong,\n          纬度: data.data.posLat\n        },\n        参与者数量: data.data.participants.length\n      });\n\n      // 打印每个参与者的信息\n      data.data.participants.forEach((participant, index) => {\n        console.log(`参与者 ${index + 1}:`, {\n          ID: participant.partPtcId,\n          类型: {\n            原始值: participant.partPtcType,\n            含义: getParticipantTypeName(participant.partPtcType)\n          },\n          尺寸: {\n            长: `${participant.partLength}cm`,\n            宽: `${participant.partWidth}cm`,\n            高: `${participant.partHeight}cm`,\n            类型: getParticipantSizeTypeName(participant.partSizeType)\n          },\n          位置: {\n            经度: participant.partPosLong,\n            纬度: participant.partPosLat\n          },\n          运动: {\n            速度: `${participant.partSpeed}m/s`,\n            航向: `${participant.partHeading}°`\n          },\n          时间: participant.partSecmark\n        });\n      });\n    } catch (error) {\n      console.error('处理RSM消息失败:', {\n        错误: error.message,\n        数据: data\n      });\n    }\n  };\n\n  // 获取参与者类型名称\n  const getParticipantTypeName = (type) => {\n    const types = {\n      '0': '未知',\n      '1': '机动车',\n      '2': '非机动车',\n      '3': '行人'\n    };\n    return types[type] || '未知';\n  };\n\n  // 获取参与者尺寸类型名称\n  const getParticipantSizeTypeName = (sizeType) => {\n    const types = {\n      '1': '小型车(长度<600cm)',\n      '2': '中型车(600cm≤长度<1300cm)',\n      '3': '大型车(长度≥1300cm)'\n    };\n    return types[sizeType] || '未知';\n  };\n\n  // 更新交通参与者\n  const updateTrafficParticipant = async (participant) => {\n    if (!sceneRef.current || !converterRef.current) {\n      console.warn('场景或坐标转换器未初始化');\n      return;\n    }\n\n    const id = participant.partPtcId;\n    const type = parseInt(participant.partPtcType);\n    const sizeType = parseInt(participant.partSizeType || '1');\n\n    try {\n      // 转换坐标\n      const modelPos = converterRef.current.wgs84ToModel(\n        parseFloat(participant.partPosLong),\n        parseFloat(participant.partPosLat)\n      );\n\n      // 设置不同类型参与者的默认尺寸和颜色\n      const participantConfig = {\n        1: { // 机动车\n          model: 'vehicle',\n          size: {\n            // 根据 partSizeType 设置不同尺寸\n            1: { length: 4.5, width: 1.8, height: 1.5 },  // 小车\n            2: { length: 8.0, width: 2.3, height: 2.5 },  // 中型车\n            3: { length: 12.0, width: 2.5, height: 3.0 }  // 大型车\n          },\n          color: 0x4444ff,\n          scale: 0.7\n        },\n        2: { // 非机动车\n          model: 'box',\n          size: { length: 1.8, width: 0.6, height: 1.2 },\n          color: 0x44ff44\n        },\n        3: { // 行人\n          model: 'cylinder',\n          size: { length: 0.5, width: 0.5, height: 1.7 },\n          color: 0xff4444\n        }\n      };\n\n      const config = participantConfig[type] || participantConfig[1];\n      // 获取实际尺寸（单位转换：cm -> m）\n      const actualSize = {\n        length: parseFloat(participant.partLength || '0') / 100,\n        width: parseFloat(participant.partWidth || '0') / 100,\n        height: parseFloat(participant.partHeight || '0') / 100\n      };\n\n      // 使用实际尺寸或默认尺寸\n      const size = {\n        length: actualSize.length > 0 ? actualSize.length : \n                (type === 1 ? config.size[sizeType].length : config.size.length),\n        width: actualSize.width > 0 ? actualSize.width :\n               (type === 1 ? config.size[sizeType].width : config.size.width),\n        height: actualSize.height > 0 ? actualSize.height :\n                (type === 1 ? config.size[sizeType].height : config.size.height)\n      };\n\n      // 创建或更新参与者数据\n      const participantData = {\n        position: new THREE.Vector3(modelPos.x, 0.5, -modelPos.y),\n        heading: parseFloat(participant.partHeading || 0),\n        speed: parseFloat(participant.partSpeed || 0),\n        type: type,\n        sizeType: sizeType,\n        config: config,\n        size: size,\n        lastUpdate: Date.now()\n      };\n\n      // 更新或创建3D模型\n      if (!participantMeshes.has(id)) {\n        const mesh = await createParticipantMesh(participantData);\n        if (mesh) {\n          mesh.name = `participant_${id}_${type}_${sizeType}`;\n          sceneRef.current.add(mesh);\n          participantMeshes.set(id, mesh);\n          console.log('创建新参与者:', {\n            ID: id,\n            类型: type,\n            尺寸类型: sizeType,\n            实际尺寸: size,\n            位置: modelPos,\n            速度: participantData.speed\n          });\n        }\n      } else {\n        const mesh = participantMeshes.get(id);\n        updateParticipantMesh(mesh, participantData);\n      }\n\n      trafficParticipants.set(id, participantData);\n    } catch (error) {\n      console.error('更新交通参与者失败:', {\n        错误: error.message,\n        参与者ID: id,\n        类型: type,\n        尺寸类型: sizeType,\n        原始数据: participant\n      });\n    }\n  };\n\n  // 创建交通参与者的3D模型\n  const createParticipantMesh = async (data) => {\n    let mesh;\n\n    try {\n      switch (data.config.model) {\n        case 'vehicle':\n          try {\n            // 使用现有的车辆模型\n            const vehicleModel = await loadVehicleModel(\n              `${BASE_URL}/changli2/Audi R8.glb`,\n              data.position,\n              data.heading\n            );\n            mesh = new THREE.Group();\n            mesh.add(vehicleModel);\n            vehicleModel.scale.set(\n              data.config.scale,\n              data.config.scale,\n              data.config.scale\n            );\n          } catch (error) {\n            console.error('加载车辆模型失败:', error);\n            // 使用备用几何体\n            const geometry = new THREE.BoxGeometry(\n              data.size.length,\n              data.size.height,\n              data.size.width\n            );\n            const material = new THREE.MeshPhongMaterial({\n              color: data.config.color,\n              transparent: true,\n              opacity: 0.8\n            });\n            mesh = new THREE.Mesh(geometry, material);\n          }\n          break;\n\n        case 'box':\n          // 非机动车使用长方体\n          const geometry = new THREE.BoxGeometry(\n            data.size.length,\n            data.size.height,\n            data.size.width\n          );\n          const material = new THREE.MeshPhongMaterial({\n            color: data.config.color,\n            transparent: true,\n            opacity: 0.8\n          });\n          mesh = new THREE.Mesh(geometry, material);\n          break;\n\n        case 'cylinder':\n          // 行人使用圆柱体\n          const cylGeometry = new THREE.CylinderGeometry(\n            0.25, 0.25,\n            data.size.height,\n            8\n          );\n          const cylMaterial = new THREE.MeshPhongMaterial({\n            color: data.config.color,\n            transparent: true,\n            opacity: 0.8\n          });\n          mesh = new THREE.Mesh(cylGeometry, cylMaterial);\n          break;\n      }\n\n      if (mesh) {\n        mesh.position.copy(data.position);\n        mesh.rotation.y = Math.PI - data.heading * Math.PI / 180;\n        mesh.castShadow = true;\n        mesh.receiveShadow = true;\n      }\n\n      return mesh;\n    } catch (error) {\n      console.error('创建参与者模型失败:', error);\n      return null;\n    }\n  };\n\n  // 更新交通参与者的 3D 模型\n  const updateParticipantMesh = (mesh, data) => {\n    // 使用 TWEEN 创建平滑过渡\n    new TWEEN.Tween(mesh.position)\n      .to(data.position, 100)\n      .easing(TWEEN.Easing.Linear.None)\n      .start();\n    \n    new TWEEN.Tween(mesh.rotation)\n      .to({ y: Math.PI - data.heading * Math.PI / 180 }, 100)\n      .easing(TWEEN.Easing.Linear.None)\n      .start();\n  };\n\n  // 初始化场景\n  useEffect(() => {\n    const initScene = async () => {\n      try {\n        console.log('开始初始化场景...');\n        \n        // 创建场景\n        const scene = new THREE.Scene();\n        sceneRef.current = scene;\n        scene.background = new THREE.Color(0xf0f0f0);\n\n        // 创建相机\n        const camera = new THREE.PerspectiveCamera(\n          75,\n          window.innerWidth / window.innerHeight,\n          0.1,\n          1000\n        );\n        cameraRef.current = camera;\n        camera.position.set(0, 300, 0);\n\n        // 创建渲染器\n        const renderer = new THREE.WebGLRenderer({ antialias: true });\n        rendererRef.current = renderer;\n        renderer.setSize(window.innerWidth, window.innerHeight);\n        renderer.shadowMap.enabled = true;\n        containerRef.current.appendChild(renderer.domElement);\n\n        // 创建控制器\n        const controls = new OrbitControls(camera, renderer.domElement);\n        controlsRef.current = controls;\n        controls.enableDamping = true;\n        controls.dampingFactor = 0.05;\n\n        // 初始化坐标转换器\n        converterRef.current = new CoordinateConverter();\n        await converterRef.current.initialize();\n\n        // 加载初始车辆模型\n        const vehicleModel = await loadVehicleModel(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          new THREE.Vector3(0, 0, 0),\n          0\n        );\n        scene.add(vehicleModel);\n        setIsVehicleLoaded(true);\n\n        // 初始化完成\n        setIsInitialized(true);\n        console.log('场景初始化完成');\n\n        // 初始化 WebSocket 连接\n        initMqttClient();\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    initScene();\n  }, []);\n\n  // 处理 WebSocket 消息\n  const handleWebSocketMessage = (message) => {\n    try {\n      // 处理连接确认消息\n      if (message.type === 'connection') {\n        console.log('WebSocket连接确认:', message);\n        return;\n      }\n\n      // 处理普通消息\n      if (!message.topic || !message.message) {\n        console.warn('无效的消息格式:', message);\n        return;\n      }\n\n      let data;\n      try {\n        data = JSON.parse(message.message);\n      } catch (error) {\n        console.error('解析消息内容失败:', {\n          error: error.message,\n          message: message.message\n        });\n        return;\n      }\n\n      // 如果场景未初始化完成，暂存消息\n      if (!isInitialized) {\n        console.log('场景未初始化完成，暂存消息');\n        setPendingMessages(prev => [...prev, { topic: message.topic, data }]);\n        return;\n      }\n\n      // 处理消息\n      switch(message.topic) {\n        case 'changli/cloud/v2x/rsu/rsm':\n          handleRSMMessage(data);\n          break;\n        case 'changli/cloud/v2x/obu/bsm':\n          handleBSMMessage(data);\n          break;\n        default:\n          console.warn('未知的消息主题:', message.topic);\n      }\n    } catch (error) {\n      console.error('处理WebSocket消息失败:', error);\n    }\n  };\n\n  // 监听初始化状态，处理暂存的消息\n  useEffect(() => {\n    if (isInitialized && pendingMessages.length > 0) {\n      console.log('处理暂存的消息:', pendingMessages.length);\n      pendingMessages.forEach(msg => {\n        handleRSMMessage(msg.data);\n      });\n      setPendingMessages([]);\n    }\n  }, [isInitialized]);\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        cameraRef.current.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        cameraRef.current.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        cameraRef.current.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        cameraRef.current.updateProjectionMatrix();\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: cameraRef.current.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: cameraRef.current.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        cameraRef.current.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(cameraRef.current.position.y) < 50) {\n          cameraRef.current.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      rendererRef.current.render(sceneRef.current, cameraRef.current);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      cameraRef.current.aspect = window.innerWidth / window.innerHeight;\n      cameraRef.current.updateProjectionMatrix();\n      rendererRef.current.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        console.log('关闭WebSocket连接');\n        mqttClientRef.current.close();\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(rendererRef.current.domElement);\n      rendererRef.current.dispose();\n      // 清理所有参与者模型\n      participantMeshes.forEach(mesh => {\n        sceneRef.current.remove(mesh);\n        mesh.geometry.dispose();\n        mesh.material.dispose();\n      });\n      participantMeshes.clear();\n      trafficParticipants.clear();\n    };\n  }, []);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;AACrB,IAAIC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAE;AACtC,IAAIC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAI;;AAEtC;AACA,MAAME,WAAW,GAAG;EAClBC,KAAK,EAAE,0BAA0B;EAAG;EACpCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,0BAA0B;EACpCC,MAAM,EAAE,CACN,2BAA2B,EAC3B,2BAA2B;AAE/B,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,gBAAgB,GAAGA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACnD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,MAAM,GAAG,IAAInC,UAAU,CAAC,CAAC;IAC/BmC,MAAM,CAACC,IAAI,CACTP,GAAG,EACFQ,IAAI,IAAK;MACR,IAAI;QACF,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK;;QAExB;QACAD,KAAK,CAACE,QAAQ,CAAEC,KAAK,IAAK;UACxB,IAAIA,KAAK,CAACC,MAAM,EAAE;YAChBD,KAAK,CAACE,QAAQ,GAAG,IAAI5C,KAAK,CAAC6C,oBAAoB,CAAC;cAC9CC,KAAK,EAAE,QAAQ;cACfC,SAAS,EAAE,GAAG;cACdC,SAAS,EAAE,GAAG;cACdC,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;;QAEF;QACAV,KAAK,CAACR,QAAQ,CAACmB,IAAI,CAACnB,QAAQ,CAAC;QAC7BQ,KAAK,CAACY,QAAQ,CAACC,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAGtB,OAAO,GAAGqB,IAAI,CAACC,EAAE,GAAG,GAAG;QAEpDpB,OAAO,CAACK,KAAK,CAAC;MAChB,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACdpB,MAAM,CAACoB,KAAK,CAAC;MACf;IACF,CAAC,EACDC,SAAS,EACTrB,MACF,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAMsB,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAG7D,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM8D,QAAQ,GAAG9D,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM+D,SAAS,GAAG/D,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgE,WAAW,GAAGhE,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMiE,WAAW,GAAGjE,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkE,YAAY,GAAGlE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMmE,UAAU,GAAGnE,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMoE,SAAS,GAAGpE,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMgE,aAAa,GAAGrE,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMsE,eAAe,GAAGtE,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMuE,aAAa,GAAGvE,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC;IAC/C+E,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRhD,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGnF,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMoF,oBAAoB,GAAG;IAC3BpD,QAAQ,EAAE,OAAO;IACjBqD,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjB,WAAW,CAAC,QAAQ,CAAC;IACrBjE,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACkF,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,WAAW,CAAC,QAAQ,CAAC;IACrBjE,UAAU,GAAG,QAAQ;IAErB,IAAI4C,SAAS,CAACyC,OAAO,IAAIpF,QAAQ,EAAE;MACjC;MACA,MAAMqF,UAAU,GAAG1C,SAAS,CAACyC,OAAO,CAACvE,QAAQ,CAACyE,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAG5C,SAAS,CAACyC,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIpG,KAAK,CAACuG,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEzD,CAAC,EAAE,GAAG;QAAE0D,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAAC3G,KAAK,CAAC4G,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdtD,SAAS,CAACyC,OAAO,CAACvE,QAAQ,CAACmB,IAAI,CAACqD,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIhH,KAAK,CAACuG,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEzD,CAAC,EAAE,CAAC;QAAE0D,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC3G,KAAK,CAAC4G,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdtD,SAAS,CAACyC,OAAO,CAACI,EAAE,CAACxD,IAAI,CAACuD,SAAS,CAAC;MACtC,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGnG,QAAQ,CAACoG,MAAM,CAACd,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIpG,KAAK,CAACuG,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEzD,CAAC,EAAE,CAAC;QAAE0D,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC3G,KAAK,CAAC4G,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdjG,QAAQ,CAACoG,MAAM,CAACpE,IAAI,CAACmE,aAAa,CAAC;QACnC;QACAxD,SAAS,CAACyC,OAAO,CAACiB,MAAM,CAACrG,QAAQ,CAACoG,MAAM,CAAC;QACzCpG,QAAQ,CAACsG,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAlG,QAAQ,CAACkF,OAAO,GAAG,IAAI;;MAEvB;MACAlF,QAAQ,CAACuG,WAAW,GAAG,EAAE;MACzBvG,QAAQ,CAACwG,WAAW,GAAG,GAAG;MAC1BxG,QAAQ,CAACyG,aAAa,GAAGtE,IAAI,CAACC,EAAE,GAAG,GAAG;MACtCpC,QAAQ,CAAC0G,aAAa,GAAG,CAAC;MAC1B1G,QAAQ,CAACsG,MAAM,CAAC,CAAC;MAEjBK,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAM3G,KAAK,GAAG,QAAQ4G,MAAM,CAACC,QAAQ,CAACC,QAAQ,YAAY;IAC1DR,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEvG,KAAK,CAAC;IAEtC,IAAI;MACF,IAAI8C,aAAa,CAACiC,OAAO,EAAE;QACzBuB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;QAC9BzD,aAAa,CAACiC,OAAO,CAACgC,KAAK,CAAC,CAAC;MAC/B;MAEA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACjH,KAAK,CAAC;MAE/BgH,EAAE,CAACE,MAAM,GAAG,MAAM;QAChBZ,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC5B;QACAS,EAAE,CAACG,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;UACrBC,IAAI,EAAE,MAAM;UACZnH,QAAQ,EAAEJ,WAAW,CAACI,QAAQ;UAC9BF,QAAQ,EAAEF,WAAW,CAACE,QAAQ;UAC9BC,QAAQ,EAAEH,WAAW,CAACG;QACxB,CAAC,CAAC,CAAC;MACL,CAAC;MAED8G,EAAE,CAACO,OAAO,GAAIC,KAAK,IAAK;QACtBlB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;UAC5BkB,IAAI,EAAED,KAAK,CAACC,IAAI;UAChBC,MAAM,EAAEF,KAAK,CAACE,MAAM;UACpBC,QAAQ,EAAEH,KAAK,CAACG;QAClB,CAAC,CAAC;;QAEF;QACAC,UAAU,CAAC,MAAM;UACftB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxBI,cAAc,CAAC,CAAC;QAClB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MAEDK,EAAE,CAACa,OAAO,GAAI7F,KAAK,IAAK;QACtBsE,OAAO,CAACtE,KAAK,CAAC,cAAc,EAAE;UAC5BA,KAAK,EAAEA,KAAK,CAAC8F,OAAO,IAAI,MAAM;UAC9BC,UAAU,EAAEf,EAAE,CAACe,UAAU;UACzBxH,GAAG,EAAEP;QACP,CAAC,CAAC;MACJ,CAAC;MAEDgH,EAAE,CAACgB,SAAS,GAAIR,KAAK,IAAK;QACxB,IAAI;UACF,IAAI,CAACA,KAAK,CAACS,IAAI,EAAE;YACf3B,OAAO,CAAC4B,IAAI,CAAC,OAAO,CAAC;YACrB;UACF;UAEA,MAAMJ,OAAO,GAAGV,IAAI,CAACe,KAAK,CAACX,KAAK,CAACS,IAAI,CAAC;UACtC,IAAI,CAACH,OAAO,CAACM,KAAK,IAAI,CAACN,OAAO,CAACA,OAAO,EAAE;YACtCxB,OAAO,CAAC4B,IAAI,CAAC,UAAU,EAAEJ,OAAO,CAAC;YACjC;UACF;UAEA,IAAIG,IAAI;UACR,IAAI;YACFA,IAAI,GAAGb,IAAI,CAACe,KAAK,CAACL,OAAO,CAACA,OAAO,CAAC;UACpC,CAAC,CAAC,OAAO9F,KAAK,EAAE;YACdsE,OAAO,CAACtE,KAAK,CAAC,WAAW,EAAE;cACzBA,KAAK,EAAEA,KAAK,CAAC8F,OAAO;cACpBA,OAAO,EAAEA,OAAO,CAACA;YACnB,CAAC,CAAC;YACF;UACF;UAEA,QAAOA,OAAO,CAACM,KAAK;YAClB,KAAK,2BAA2B;cAC9B,IAAI,CAACnF,aAAa,EAAE;gBAClBqD,OAAO,CAAC4B,IAAI,CAAC,kBAAkB,CAAC;gBAChC;cACF;cACAG,gBAAgB,CAACJ,IAAI,CAAC;cACtB;YACF,KAAK,2BAA2B;cAC9BK,gBAAgB,CAACL,IAAI,CAAC;cACtB;YACF;cACE3B,OAAO,CAAC4B,IAAI,CAAC,UAAU,EAAEJ,OAAO,CAACM,KAAK,CAAC;UAC3C;QACF,CAAC,CAAC,OAAOpG,KAAK,EAAE;UACdsE,OAAO,CAACtE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QAC1C;MACF,CAAC;MAEDc,aAAa,CAACiC,OAAO,GAAGiC,EAAE;IAC5B,CAAC,CAAC,OAAOhF,KAAK,EAAE;MACdsE,OAAO,CAACtE,KAAK,CAAC,kBAAkB,EAAE;QAChCA,KAAK,EAAEA,KAAK,CAAC8F,OAAO;QACpBS,KAAK,EAAEvG,KAAK,CAACuG,KAAK;QAClBvI;MACF,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMsI,gBAAgB,GAAIL,IAAI,IAAK;IACjC,IAAIA,IAAI,CAACX,IAAI,KAAK,KAAK,IAAIW,IAAI,CAACA,IAAI,EAAE;MACpC,MAAMO,OAAO,GAAGP,IAAI,CAACA,IAAI;MACzB,MAAMQ,QAAQ,GAAG;QACflF,SAAS,EAAEmF,UAAU,CAACF,OAAO,CAACG,QAAQ,CAAC;QACvCnF,QAAQ,EAAEkF,UAAU,CAACF,OAAO,CAACI,OAAO,CAAC;QACrCnF,KAAK,EAAEiF,UAAU,CAACF,OAAO,CAACK,SAAS,CAAC;QACpCpI,OAAO,EAAEiI,UAAU,CAACF,OAAO,CAACM,WAAW;MACzC,CAAC;MAEDxC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkC,QAAQ,CAAC;;MAElC;MACA,IAAItJ,gBAAgB,EAAE;QACpB,MAAM4J,QAAQ,GAAGpG,SAAS,CAACoC,OAAO,CAACiE,YAAY,CAACP,QAAQ,CAAClF,SAAS,EAAEkF,QAAQ,CAACjF,QAAQ,CAAC;QACtF,MAAMyF,WAAW,GAAG,IAAIxK,KAAK,CAACyK,OAAO,CAACH,QAAQ,CAACzD,CAAC,EAAE,GAAG,EAAE,CAACyD,QAAQ,CAAClH,CAAC,CAAC;QAEnE1C,gBAAgB,CAACqB,QAAQ,CAACmB,IAAI,CAACsH,WAAW,CAAC;QAC3C9J,gBAAgB,CAACyC,QAAQ,CAACC,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG0G,QAAQ,CAAChI,OAAO,GAAGqB,IAAI,CAACC,EAAE,GAAG,GAAG;QACxE5C,gBAAgB,CAACgK,YAAY,CAAC,CAAC;QAC/BhK,gBAAgB,CAACiK,iBAAiB,CAAC,IAAI,CAAC;QAExC9F,eAAe,CAACmF,QAAQ,CAAC;QACzBnC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0C,WAAW,CAAC;MACtC;IACF;EACF,CAAC;;EAED;EACA,MAAMZ,gBAAgB,GAAIJ,IAAI,IAAK;IACjC,IAAI;MACF;MACA,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACA,IAAI,IAAI,CAACoB,KAAK,CAACC,OAAO,CAACrB,IAAI,CAACA,IAAI,CAACsB,YAAY,CAAC,EAAE;QACjEjD,OAAO,CAAC4B,IAAI,CAAC,aAAa,EAAED,IAAI,CAAC;QACjC;MACF;;MAEA;MACA3B,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE;QACpBiD,IAAI,EAAEvB,IAAI,CAACA,IAAI,CAACwB,KAAK;QACrBC,GAAG,EAAEzB,IAAI,CAAC0B,EAAE;QACZC,IAAI,EAAE3B,IAAI,CAAC4B,MAAM;QACjBC,GAAG,EAAE;UACHC,EAAE,EAAE9B,IAAI,CAACA,IAAI,CAAC+B,OAAO;UACrBC,EAAE,EAAEhC,IAAI,CAACA,IAAI,CAACiC;QAChB,CAAC;QACDC,KAAK,EAAElC,IAAI,CAACA,IAAI,CAACsB,YAAY,CAACa;MAChC,CAAC,CAAC;;MAEF;MACAnC,IAAI,CAACA,IAAI,CAACsB,YAAY,CAACc,OAAO,CAAC,CAACC,WAAW,EAAEC,KAAK,KAAK;QACrDjE,OAAO,CAACC,GAAG,CAAC,OAAOgE,KAAK,GAAG,CAAC,GAAG,EAAE;UAC/BC,EAAE,EAAEF,WAAW,CAACG,SAAS;UACzBC,EAAE,EAAE;YACFC,GAAG,EAAEL,WAAW,CAACM,WAAW;YAC5BC,EAAE,EAAEC,sBAAsB,CAACR,WAAW,CAACM,WAAW;UACpD,CAAC;UACDG,EAAE,EAAE;YACFC,CAAC,EAAE,GAAGV,WAAW,CAACW,UAAU,IAAI;YAChCC,CAAC,EAAE,GAAGZ,WAAW,CAACa,SAAS,IAAI;YAC/BC,CAAC,EAAE,GAAGd,WAAW,CAACe,UAAU,IAAI;YAChCX,EAAE,EAAEY,0BAA0B,CAAChB,WAAW,CAACiB,YAAY;UACzD,CAAC;UACDC,EAAE,EAAE;YACFzB,EAAE,EAAEO,WAAW,CAACmB,WAAW;YAC3BxB,EAAE,EAAEK,WAAW,CAACoB;UAClB,CAAC;UACDC,EAAE,EAAE;YACFC,EAAE,EAAE,GAAGtB,WAAW,CAACzB,SAAS,KAAK;YACjCgD,EAAE,EAAE,GAAGvB,WAAW,CAACxB,WAAW;UAChC,CAAC;UACDgD,EAAE,EAAExB,WAAW,CAACyB;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO/J,KAAK,EAAE;MACdsE,OAAO,CAACtE,KAAK,CAAC,YAAY,EAAE;QAC1BgK,EAAE,EAAEhK,KAAK,CAAC8F,OAAO;QACjBmE,EAAE,EAAEhE;MACN,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM6C,sBAAsB,GAAIxD,IAAI,IAAK;IACvC,MAAM4E,KAAK,GAAG;MACZ,GAAG,EAAE,IAAI;MACT,GAAG,EAAE,KAAK;MACV,GAAG,EAAE,MAAM;MACX,GAAG,EAAE;IACP,CAAC;IACD,OAAOA,KAAK,CAAC5E,IAAI,CAAC,IAAI,IAAI;EAC5B,CAAC;;EAED;EACA,MAAMgE,0BAA0B,GAAIa,QAAQ,IAAK;IAC/C,MAAMD,KAAK,GAAG;MACZ,GAAG,EAAE,eAAe;MACpB,GAAG,EAAE,sBAAsB;MAC3B,GAAG,EAAE;IACP,CAAC;IACD,OAAOA,KAAK,CAACC,QAAQ,CAAC,IAAI,IAAI;EAChC,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAO9B,WAAW,IAAK;IACtD,IAAI,CAACjI,QAAQ,CAAC0C,OAAO,IAAI,CAACtC,YAAY,CAACsC,OAAO,EAAE;MAC9CuB,OAAO,CAAC4B,IAAI,CAAC,cAAc,CAAC;MAC5B;IACF;IAEA,MAAMmE,EAAE,GAAG/B,WAAW,CAACG,SAAS;IAChC,MAAMnD,IAAI,GAAGgF,QAAQ,CAAChC,WAAW,CAACM,WAAW,CAAC;IAC9C,MAAMuB,QAAQ,GAAGG,QAAQ,CAAChC,WAAW,CAACiB,YAAY,IAAI,GAAG,CAAC;IAE1D,IAAI;MACF;MACA,MAAMxC,QAAQ,GAAGtG,YAAY,CAACsC,OAAO,CAACiE,YAAY,CAChDN,UAAU,CAAC4B,WAAW,CAACmB,WAAW,CAAC,EACnC/C,UAAU,CAAC4B,WAAW,CAACoB,UAAU,CACnC,CAAC;;MAED;MACA,MAAMa,iBAAiB,GAAG;QACxB,CAAC,EAAE;UAAE;UACHvL,KAAK,EAAE,SAAS;UAChBwL,IAAI,EAAE;YACJ;YACA,CAAC,EAAE;cAAEpC,MAAM,EAAE,GAAG;cAAEqC,KAAK,EAAE,GAAG;cAAEC,MAAM,EAAE;YAAI,CAAC;YAAG;YAC9C,CAAC,EAAE;cAAEtC,MAAM,EAAE,GAAG;cAAEqC,KAAK,EAAE,GAAG;cAAEC,MAAM,EAAE;YAAI,CAAC;YAAG;YAC9C,CAAC,EAAE;cAAEtC,MAAM,EAAE,IAAI;cAAEqC,KAAK,EAAE,GAAG;cAAEC,MAAM,EAAE;YAAI,CAAC,CAAE;UAChD,CAAC;UACDnL,KAAK,EAAE,QAAQ;UACfoL,KAAK,EAAE;QACT,CAAC;QACD,CAAC,EAAE;UAAE;UACH3L,KAAK,EAAE,KAAK;UACZwL,IAAI,EAAE;YAAEpC,MAAM,EAAE,GAAG;YAAEqC,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAI,CAAC;UAC9CnL,KAAK,EAAE;QACT,CAAC;QACD,CAAC,EAAE;UAAE;UACHP,KAAK,EAAE,UAAU;UACjBwL,IAAI,EAAE;YAAEpC,MAAM,EAAE,GAAG;YAAEqC,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAI,CAAC;UAC9CnL,KAAK,EAAE;QACT;MACF,CAAC;MAED,MAAMqL,MAAM,GAAGL,iBAAiB,CAACjF,IAAI,CAAC,IAAIiF,iBAAiB,CAAC,CAAC,CAAC;MAC9D;MACA,MAAMM,UAAU,GAAG;QACjBzC,MAAM,EAAE1B,UAAU,CAAC4B,WAAW,CAACW,UAAU,IAAI,GAAG,CAAC,GAAG,GAAG;QACvDwB,KAAK,EAAE/D,UAAU,CAAC4B,WAAW,CAACa,SAAS,IAAI,GAAG,CAAC,GAAG,GAAG;QACrDuB,MAAM,EAAEhE,UAAU,CAAC4B,WAAW,CAACe,UAAU,IAAI,GAAG,CAAC,GAAG;MACtD,CAAC;;MAED;MACA,MAAMmB,IAAI,GAAG;QACXpC,MAAM,EAAEyC,UAAU,CAACzC,MAAM,GAAG,CAAC,GAAGyC,UAAU,CAACzC,MAAM,GACxC9C,IAAI,KAAK,CAAC,GAAGsF,MAAM,CAACJ,IAAI,CAACL,QAAQ,CAAC,CAAC/B,MAAM,GAAGwC,MAAM,CAACJ,IAAI,CAACpC,MAAO;QACxEqC,KAAK,EAAEI,UAAU,CAACJ,KAAK,GAAG,CAAC,GAAGI,UAAU,CAACJ,KAAK,GACtCnF,IAAI,KAAK,CAAC,GAAGsF,MAAM,CAACJ,IAAI,CAACL,QAAQ,CAAC,CAACM,KAAK,GAAGG,MAAM,CAACJ,IAAI,CAACC,KAAM;QACrEC,MAAM,EAAEG,UAAU,CAACH,MAAM,GAAG,CAAC,GAAGG,UAAU,CAACH,MAAM,GACxCpF,IAAI,KAAK,CAAC,GAAGsF,MAAM,CAACJ,IAAI,CAACL,QAAQ,CAAC,CAACO,MAAM,GAAGE,MAAM,CAACJ,IAAI,CAACE;MACnE,CAAC;;MAED;MACA,MAAMI,eAAe,GAAG;QACtBtM,QAAQ,EAAE,IAAI/B,KAAK,CAACyK,OAAO,CAACH,QAAQ,CAACzD,CAAC,EAAE,GAAG,EAAE,CAACyD,QAAQ,CAAClH,CAAC,CAAC;QACzDpB,OAAO,EAAEiI,UAAU,CAAC4B,WAAW,CAACxB,WAAW,IAAI,CAAC,CAAC;QACjDrF,KAAK,EAAEiF,UAAU,CAAC4B,WAAW,CAACzB,SAAS,IAAI,CAAC,CAAC;QAC7CvB,IAAI,EAAEA,IAAI;QACV6E,QAAQ,EAAEA,QAAQ;QAClBS,MAAM,EAAEA,MAAM;QACdJ,IAAI,EAAEA,IAAI;QACVO,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC;MACvB,CAAC;;MAED;MACA,IAAI,CAACnN,iBAAiB,CAACoN,GAAG,CAACb,EAAE,CAAC,EAAE;QAC9B,MAAMc,IAAI,GAAG,MAAMC,qBAAqB,CAACN,eAAe,CAAC;QACzD,IAAIK,IAAI,EAAE;UACRA,IAAI,CAACE,IAAI,GAAG,eAAehB,EAAE,IAAI/E,IAAI,IAAI6E,QAAQ,EAAE;UACnD9J,QAAQ,CAAC0C,OAAO,CAACuI,GAAG,CAACH,IAAI,CAAC;UAC1BrN,iBAAiB,CAACyN,GAAG,CAAClB,EAAE,EAAEc,IAAI,CAAC;UAC/B7G,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;YACrBiE,EAAE,EAAE6B,EAAE;YACN3B,EAAE,EAAEpD,IAAI;YACRkG,IAAI,EAAErB,QAAQ;YACdsB,IAAI,EAAEjB,IAAI;YACVhB,EAAE,EAAEzC,QAAQ;YACZ6C,EAAE,EAAEkB,eAAe,CAACrJ;UACtB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM0J,IAAI,GAAGrN,iBAAiB,CAAC4N,GAAG,CAACrB,EAAE,CAAC;QACtCsB,qBAAqB,CAACR,IAAI,EAAEL,eAAe,CAAC;MAC9C;MAEAlN,mBAAmB,CAAC2N,GAAG,CAAClB,EAAE,EAAES,eAAe,CAAC;IAC9C,CAAC,CAAC,OAAO9K,KAAK,EAAE;MACdsE,OAAO,CAACtE,KAAK,CAAC,YAAY,EAAE;QAC1BgK,EAAE,EAAEhK,KAAK,CAAC8F,OAAO;QACjB8F,KAAK,EAAEvB,EAAE;QACT3B,EAAE,EAAEpD,IAAI;QACRkG,IAAI,EAAErB,QAAQ;QACd0B,IAAI,EAAEvD;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM8C,qBAAqB,GAAG,MAAOnF,IAAI,IAAK;IAC5C,IAAIkF,IAAI;IAER,IAAI;MACF,QAAQlF,IAAI,CAAC2E,MAAM,CAAC5L,KAAK;QACvB,KAAK,SAAS;UACZ,IAAI;YACF;YACA,MAAM8M,YAAY,GAAG,MAAMxN,gBAAgB,CACzC,GAAGD,QAAQ,uBAAuB,EAClC4H,IAAI,CAACzH,QAAQ,EACbyH,IAAI,CAACxH,OACP,CAAC;YACD0M,IAAI,GAAG,IAAI1O,KAAK,CAACsP,KAAK,CAAC,CAAC;YACxBZ,IAAI,CAACG,GAAG,CAACQ,YAAY,CAAC;YACtBA,YAAY,CAACnB,KAAK,CAACY,GAAG,CACpBtF,IAAI,CAAC2E,MAAM,CAACD,KAAK,EACjB1E,IAAI,CAAC2E,MAAM,CAACD,KAAK,EACjB1E,IAAI,CAAC2E,MAAM,CAACD,KACd,CAAC;UACH,CAAC,CAAC,OAAO3K,KAAK,EAAE;YACdsE,OAAO,CAACtE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;YACjC;YACA,MAAMgM,QAAQ,GAAG,IAAIvP,KAAK,CAACwP,WAAW,CACpChG,IAAI,CAACuE,IAAI,CAACpC,MAAM,EAChBnC,IAAI,CAACuE,IAAI,CAACE,MAAM,EAChBzE,IAAI,CAACuE,IAAI,CAACC,KACZ,CAAC;YACD,MAAMpL,QAAQ,GAAG,IAAI5C,KAAK,CAACyP,iBAAiB,CAAC;cAC3C3M,KAAK,EAAE0G,IAAI,CAAC2E,MAAM,CAACrL,KAAK;cACxB4M,WAAW,EAAE,IAAI;cACjBC,OAAO,EAAE;YACX,CAAC,CAAC;YACFjB,IAAI,GAAG,IAAI1O,KAAK,CAAC4P,IAAI,CAACL,QAAQ,EAAE3M,QAAQ,CAAC;UAC3C;UACA;QAEF,KAAK,KAAK;UACR;UACA,MAAM2M,QAAQ,GAAG,IAAIvP,KAAK,CAACwP,WAAW,CACpChG,IAAI,CAACuE,IAAI,CAACpC,MAAM,EAChBnC,IAAI,CAACuE,IAAI,CAACE,MAAM,EAChBzE,IAAI,CAACuE,IAAI,CAACC,KACZ,CAAC;UACD,MAAMpL,QAAQ,GAAG,IAAI5C,KAAK,CAACyP,iBAAiB,CAAC;YAC3C3M,KAAK,EAAE0G,IAAI,CAAC2E,MAAM,CAACrL,KAAK;YACxB4M,WAAW,EAAE,IAAI;YACjBC,OAAO,EAAE;UACX,CAAC,CAAC;UACFjB,IAAI,GAAG,IAAI1O,KAAK,CAAC4P,IAAI,CAACL,QAAQ,EAAE3M,QAAQ,CAAC;UACzC;QAEF,KAAK,UAAU;UACb;UACA,MAAMiN,WAAW,GAAG,IAAI7P,KAAK,CAAC8P,gBAAgB,CAC5C,IAAI,EAAE,IAAI,EACVtG,IAAI,CAACuE,IAAI,CAACE,MAAM,EAChB,CACF,CAAC;UACD,MAAM8B,WAAW,GAAG,IAAI/P,KAAK,CAACyP,iBAAiB,CAAC;YAC9C3M,KAAK,EAAE0G,IAAI,CAAC2E,MAAM,CAACrL,KAAK;YACxB4M,WAAW,EAAE,IAAI;YACjBC,OAAO,EAAE;UACX,CAAC,CAAC;UACFjB,IAAI,GAAG,IAAI1O,KAAK,CAAC4P,IAAI,CAACC,WAAW,EAAEE,WAAW,CAAC;UAC/C;MACJ;MAEA,IAAIrB,IAAI,EAAE;QACRA,IAAI,CAAC3M,QAAQ,CAACmB,IAAI,CAACsG,IAAI,CAACzH,QAAQ,CAAC;QACjC2M,IAAI,CAACvL,QAAQ,CAACC,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAGkG,IAAI,CAACxH,OAAO,GAAGqB,IAAI,CAACC,EAAE,GAAG,GAAG;QACxDoL,IAAI,CAACsB,UAAU,GAAG,IAAI;QACtBtB,IAAI,CAACuB,aAAa,GAAG,IAAI;MAC3B;MAEA,OAAOvB,IAAI;IACb,CAAC,CAAC,OAAOnL,KAAK,EAAE;MACdsE,OAAO,CAACtE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAM2L,qBAAqB,GAAGA,CAACR,IAAI,EAAElF,IAAI,KAAK;IAC5C;IACA,IAAIpJ,KAAK,CAACuG,KAAK,CAAC+H,IAAI,CAAC3M,QAAQ,CAAC,CAC3B6E,EAAE,CAAC4C,IAAI,CAACzH,QAAQ,EAAE,GAAG,CAAC,CACtBgF,MAAM,CAAC3G,KAAK,CAAC4G,MAAM,CAACkJ,MAAM,CAACC,IAAI,CAAC,CAChC/I,KAAK,CAAC,CAAC;IAEV,IAAIhH,KAAK,CAACuG,KAAK,CAAC+H,IAAI,CAACvL,QAAQ,CAAC,CAC3ByD,EAAE,CAAC;MAAExD,CAAC,EAAEC,IAAI,CAACC,EAAE,GAAGkG,IAAI,CAACxH,OAAO,GAAGqB,IAAI,CAACC,EAAE,GAAG;IAAI,CAAC,EAAE,GAAG,CAAC,CACtDyD,MAAM,CAAC3G,KAAK,CAAC4G,MAAM,CAACkJ,MAAM,CAACC,IAAI,CAAC,CAChC/I,KAAK,CAAC,CAAC;EACZ,CAAC;;EAED;EACAvH,SAAS,CAAC,MAAM;IACd,MAAMuQ,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFvI,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;QAEzB;QACA,MAAMtF,KAAK,GAAG,IAAIxC,KAAK,CAACqQ,KAAK,CAAC,CAAC;QAC/BzM,QAAQ,CAAC0C,OAAO,GAAG9D,KAAK;QACxBA,KAAK,CAAC8N,UAAU,GAAG,IAAItQ,KAAK,CAACuQ,KAAK,CAAC,QAAQ,CAAC;;QAE5C;QACA,MAAMC,MAAM,GAAG,IAAIxQ,KAAK,CAACyQ,iBAAiB,CACxC,EAAE,EACFtI,MAAM,CAACuI,UAAU,GAAGvI,MAAM,CAACwI,WAAW,EACtC,GAAG,EACH,IACF,CAAC;QACD9M,SAAS,CAACyC,OAAO,GAAGkK,MAAM;QAC1BA,MAAM,CAACzO,QAAQ,CAAC+M,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;;QAE9B;QACA,MAAM8B,QAAQ,GAAG,IAAI5Q,KAAK,CAAC6Q,aAAa,CAAC;UAAEC,SAAS,EAAE;QAAK,CAAC,CAAC;QAC7DhN,WAAW,CAACwC,OAAO,GAAGsK,QAAQ;QAC9BA,QAAQ,CAACG,OAAO,CAAC5I,MAAM,CAACuI,UAAU,EAAEvI,MAAM,CAACwI,WAAW,CAAC;QACvDC,QAAQ,CAACI,SAAS,CAAC5K,OAAO,GAAG,IAAI;QACjCzC,YAAY,CAAC2C,OAAO,CAAC2K,WAAW,CAACL,QAAQ,CAACM,UAAU,CAAC;;QAErD;QACA,MAAMhQ,QAAQ,GAAG,IAAIhB,aAAa,CAACsQ,MAAM,EAAEI,QAAQ,CAACM,UAAU,CAAC;QAC/DnN,WAAW,CAACuC,OAAO,GAAGpF,QAAQ;QAC9BA,QAAQ,CAACiQ,aAAa,GAAG,IAAI;QAC7BjQ,QAAQ,CAACkQ,aAAa,GAAG,IAAI;;QAE7B;QACApN,YAAY,CAACsC,OAAO,GAAG,IAAInG,mBAAmB,CAAC,CAAC;QAChD,MAAM6D,YAAY,CAACsC,OAAO,CAAC+K,UAAU,CAAC,CAAC;;QAEvC;QACA,MAAMhC,YAAY,GAAG,MAAMxN,gBAAgB,CACzC,GAAGD,QAAQ,uBAAuB,EAClC,IAAI5B,KAAK,CAACyK,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC1B,CACF,CAAC;QACDjI,KAAK,CAACqM,GAAG,CAACQ,YAAY,CAAC;QACvB9K,kBAAkB,CAAC,IAAI,CAAC;;QAExB;QACAE,gBAAgB,CAAC,IAAI,CAAC;QACtBoD,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;;QAEtB;QACAI,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC,OAAO3E,KAAK,EAAE;QACdsE,OAAO,CAACtE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;IAED6M,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkB,sBAAsB,GAAIjI,OAAO,IAAK;IAC1C,IAAI;MACF;MACA,IAAIA,OAAO,CAACR,IAAI,KAAK,YAAY,EAAE;QACjChB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuB,OAAO,CAAC;QACtC;MACF;;MAEA;MACA,IAAI,CAACA,OAAO,CAACM,KAAK,IAAI,CAACN,OAAO,CAACA,OAAO,EAAE;QACtCxB,OAAO,CAAC4B,IAAI,CAAC,UAAU,EAAEJ,OAAO,CAAC;QACjC;MACF;MAEA,IAAIG,IAAI;MACR,IAAI;QACFA,IAAI,GAAGb,IAAI,CAACe,KAAK,CAACL,OAAO,CAACA,OAAO,CAAC;MACpC,CAAC,CAAC,OAAO9F,KAAK,EAAE;QACdsE,OAAO,CAACtE,KAAK,CAAC,WAAW,EAAE;UACzBA,KAAK,EAAEA,KAAK,CAAC8F,OAAO;UACpBA,OAAO,EAAEA,OAAO,CAACA;QACnB,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAI,CAAC7E,aAAa,EAAE;QAClBqD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC5BnD,kBAAkB,CAAC4M,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAE5H,KAAK,EAAEN,OAAO,CAACM,KAAK;UAAEH;QAAK,CAAC,CAAC,CAAC;QACrE;MACF;;MAEA;MACA,QAAOH,OAAO,CAACM,KAAK;QAClB,KAAK,2BAA2B;UAC9BC,gBAAgB,CAACJ,IAAI,CAAC;UACtB;QACF,KAAK,2BAA2B;UAC9BK,gBAAgB,CAACL,IAAI,CAAC;UACtB;QACF;UACE3B,OAAO,CAAC4B,IAAI,CAAC,UAAU,EAAEJ,OAAO,CAACM,KAAK,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOpG,KAAK,EAAE;MACdsE,OAAO,CAACtE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C;EACF,CAAC;;EAED;EACA1D,SAAS,CAAC,MAAM;IACd,IAAI2E,aAAa,IAAIE,eAAe,CAACiH,MAAM,GAAG,CAAC,EAAE;MAC/C9D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEpD,eAAe,CAACiH,MAAM,CAAC;MAC/CjH,eAAe,CAACkH,OAAO,CAAC4F,GAAG,IAAI;QAC7B5H,gBAAgB,CAAC4H,GAAG,CAAChI,IAAI,CAAC;MAC5B,CAAC,CAAC;MACF7E,kBAAkB,CAAC,EAAE,CAAC;IACxB;EACF,CAAC,EAAE,CAACH,aAAa,CAAC,CAAC;EAEnB3E,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8D,YAAY,CAAC2C,OAAO,EAAE;;IAE3B;IACA,MAAMmL,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACArR,KAAK,CAACoH,MAAM,CAAC,CAAC;MAEd,IAAIvG,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACkF,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMuL,UAAU,GAAGjR,gBAAgB,CAACqB,QAAQ,CAACyE,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMoL,eAAe,GAAGlR,gBAAgB,CAACyC,QAAQ,CAACC,CAAC;;QAEnD;QACA;QACA,MAAMyO,gBAAgB,GAAG,EAAED,eAAe,GAAGvO,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMwO,YAAY,GAAG,IAAI9R,KAAK,CAACyK,OAAO,CACpC,CAAC,EAAE,GAAGpH,IAAI,CAAC0O,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAGxO,IAAI,CAAC2O,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAhO,SAAS,CAACyC,OAAO,CAACvE,QAAQ,CAACmB,IAAI,CAACyO,UAAU,CAAC,CAAC9C,GAAG,CAACiD,YAAY,CAAC;;QAE7D;QACAjO,SAAS,CAACyC,OAAO,CAACI,EAAE,CAACoI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEjC;QACA,MAAMmD,YAAY,GAAGN,UAAU,CAACnL,KAAK,CAAC,CAAC;QACvC3C,SAAS,CAACyC,OAAO,CAACiB,MAAM,CAAC0K,YAAY,CAAC;;QAEtC;QACApO,SAAS,CAACyC,OAAO,CAAC4L,sBAAsB,CAAC,CAAC;QAC1CrO,SAAS,CAACyC,OAAO,CAACoE,YAAY,CAAC,CAAC;QAChC7G,SAAS,CAACyC,OAAO,CAACqE,iBAAiB,CAAC,IAAI,CAAC;;QAEzC;QACAzJ,QAAQ,CAACkF,OAAO,GAAG,KAAK;;QAExB;QACAlF,QAAQ,CAACoG,MAAM,CAACpE,IAAI,CAACyO,UAAU,CAAC;QAChCzQ,QAAQ,CAACsG,MAAM,CAAC,CAAC;QAEjBK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBqK,IAAI,EAAER,UAAU,CAACS,OAAO,CAAC,CAAC;UAC1BC,IAAI,EAAExO,SAAS,CAACyC,OAAO,CAACvE,QAAQ,CAACqQ,OAAO,CAAC,CAAC;UAC1CE,IAAI,EAAEL,YAAY,CAACG,OAAO,CAAC,CAAC;UAC5BG,IAAI,EAAE1O,SAAS,CAACyC,OAAO,CAACkM,iBAAiB,CAAC,IAAIxS,KAAK,CAACyK,OAAO,CAAC,CAAC,CAAC,CAAC2H,OAAO,CAAC;QACzE,CAAC,CAAC;MACJ,CAAC,MAAM,IAAInR,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACkF,OAAO,GAAG,IAAI;;QAEvB;QACAvC,SAAS,CAACyC,OAAO,CAACI,EAAE,CAACoI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEjC;QACA,IAAIzL,IAAI,CAACoP,GAAG,CAAC5O,SAAS,CAACyC,OAAO,CAACvE,QAAQ,CAACqB,CAAC,CAAC,GAAG,EAAE,EAAE;UAC/CS,SAAS,CAACyC,OAAO,CAACvE,QAAQ,CAAC+M,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACzC5N,QAAQ,CAACoG,MAAM,CAACwH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BjL,SAAS,CAACyC,OAAO,CAACiB,MAAM,CAACrG,QAAQ,CAACoG,MAAM,CAAC;UACzCpG,QAAQ,CAACsG,MAAM,CAAC,CAAC;QACnB;MACF;MAEAtG,QAAQ,CAACsG,MAAM,CAAC,CAAC;MACjB1D,WAAW,CAACwC,OAAO,CAACoM,MAAM,CAAC9O,QAAQ,CAAC0C,OAAO,EAAEzC,SAAS,CAACyC,OAAO,CAAC;IACjE,CAAC;IAEDmL,OAAO,CAAC,CAAC;;IAET;IACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzB9O,SAAS,CAACyC,OAAO,CAACsM,MAAM,GAAGzK,MAAM,CAACuI,UAAU,GAAGvI,MAAM,CAACwI,WAAW;MACjE9M,SAAS,CAACyC,OAAO,CAAC4L,sBAAsB,CAAC,CAAC;MAC1CpO,WAAW,CAACwC,OAAO,CAACyK,OAAO,CAAC5I,MAAM,CAACuI,UAAU,EAAEvI,MAAM,CAACwI,WAAW,CAAC;IACpE,CAAC;IACDxI,MAAM,CAAC0K,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACAxK,MAAM,CAAC2K,aAAa,GAAG,MAAM;MAC3B,IAAIjP,SAAS,CAACyC,OAAO,EAAE;QACrBzC,SAAS,CAACyC,OAAO,CAACvE,QAAQ,CAAC+M,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCjL,SAAS,CAACyC,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC1D,SAAS,CAACyC,OAAO,CAACoE,YAAY,CAAC,CAAC;QAChC7G,SAAS,CAACyC,OAAO,CAACqE,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIzJ,QAAQ,EAAE;UACZA,QAAQ,CAACoG,MAAM,CAACwH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B5N,QAAQ,CAACkF,OAAO,GAAG,IAAI;UACvBlF,QAAQ,CAACsG,MAAM,CAAC,CAAC;QACnB;QAEAvG,UAAU,GAAG,QAAQ;QACrB4G,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAAiL,qBAAA;MACX,IAAIlS,oBAAoB,EAAE;QACxBmS,aAAa,CAACnS,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACA,IAAIwD,aAAa,CAACiC,OAAO,EAAE;QACzBuB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC5BzD,aAAa,CAACiC,OAAO,CAACgC,KAAK,CAAC,CAAC;MAC/B;MACAH,MAAM,CAAC8K,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAApP,YAAY,CAAC2C,OAAO,cAAAyM,qBAAA,uBAApBA,qBAAA,CAAsBG,WAAW,CAACpP,WAAW,CAACwC,OAAO,CAAC4K,UAAU,CAAC;MACjEpN,WAAW,CAACwC,OAAO,CAAC6M,OAAO,CAAC,CAAC;MAC7B;MACA9R,iBAAiB,CAACuK,OAAO,CAAC8C,IAAI,IAAI;QAChC9K,QAAQ,CAAC0C,OAAO,CAAC8M,MAAM,CAAC1E,IAAI,CAAC;QAC7BA,IAAI,CAACa,QAAQ,CAAC4D,OAAO,CAAC,CAAC;QACvBzE,IAAI,CAAC9L,QAAQ,CAACuQ,OAAO,CAAC,CAAC;MACzB,CAAC,CAAC;MACF9R,iBAAiB,CAACgS,KAAK,CAAC,CAAC;MACzBlS,mBAAmB,CAACkS,KAAK,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE9S,OAAA,CAAAE,SAAA;IAAA6S,QAAA,gBACE/S,OAAA;MAAKgT,GAAG,EAAE5P,YAAa;MAAC6P,KAAK,EAAE;QAAExF,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO;IAAE;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpErT,OAAA;MAAKiT,KAAK,EAAErO,oBAAqB;MAAAmO,QAAA,gBAC/B/S,OAAA;QACEiT,KAAK,EAAE;UACL,GAAG9N,WAAW;UACdE,eAAe,EAAEX,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EnC,KAAK,EAAEmC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF4O,OAAO,EAAE1N,kBAAmB;QAAAmN,QAAA,EAC7B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrT,OAAA;QACEiT,KAAK,EAAE;UACL,GAAG9N,WAAW;UACdE,eAAe,EAAEX,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EnC,KAAK,EAAEmC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF4O,OAAO,EAAExN,kBAAmB;QAAAiN,QAAA,EAC7B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAlQ,EAAA,CA7yBMD,WAAW;AAAAqQ,EAAA,GAAXrQ,WAAW;AA8yBjB,SAASsQ,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACjG,KAAK,GAAG,GAAG;EAClBiG,MAAM,CAAChG,MAAM,GAAG,EAAE;;EAElB;EACAmG,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACjG,KAAK,GAAC,CAAC,EAAEiG,MAAM,CAAChG,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMyG,OAAO,GAAG,IAAI1U,KAAK,CAAC2U,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAI5U,KAAK,CAAC6U,cAAc,CAAC;IAC9CC,GAAG,EAAEJ,OAAO;IACZhF,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMqF,MAAM,GAAG,IAAI/U,KAAK,CAACgV,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAC7G,KAAK,CAACY,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOiG,MAAM;AACf;;AAEA;AACA5M,MAAM,CAAC8M,WAAW,GAAG,CAACpO,CAAC,EAAEzD,CAAC,EAAE0D,CAAC,KAAK;EAChC,IAAIpG,gBAAgB,EAAE;IACpBA,gBAAgB,CAACqB,QAAQ,CAAC+M,GAAG,CAACjI,CAAC,EAAEzD,CAAC,EAAE0D,CAAC,CAAC;IACtCpG,gBAAgB,CAACgK,YAAY,CAAC,CAAC;IAC/BhK,gBAAgB,CAACiK,iBAAiB,CAAC,IAAI,CAAC;IACxC9C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACjB,CAAC;MAAEzD,CAAC;MAAE0D;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACAqB,MAAM,CAAC+M,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM1E,MAAM,GAAG0D,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAI7E,MAAM,EAAE;MACV;MACA,MAAM8E,MAAM,GAAG9E,MAAM,CAACzO,QAAQ,CAACyE,KAAK,CAAC,CAAC;;MAEtC;MACAgK,MAAM,CAACzO,QAAQ,CAAC+M,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9B0B,MAAM,CAAC9J,EAAE,CAACoI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtB0B,MAAM,CAACjJ,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAiJ,MAAM,CAAC9F,YAAY,CAAC,CAAC;MACrB8F,MAAM,CAAC7F,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMzJ,QAAQ,GAAGgT,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIrU,QAAQ,EAAE;QACZA,QAAQ,CAACoG,MAAM,CAACwH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B5N,QAAQ,CAACsG,MAAM,CAAC,CAAC;MACnB;MAEAK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB0N,GAAG,EAAEF,MAAM,CAAClD,OAAO,CAAC,CAAC;QACrBqD,GAAG,EAAEjF,MAAM,CAACzO,QAAQ,CAACqQ,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOsD,CAAC,EAAE;IACV7N,OAAO,CAACtE,KAAK,CAAC,YAAY,EAAEmS,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAejS,WAAW;AAAC,IAAAqQ,EAAA;AAAA6B,YAAA,CAAA7B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}