{"ast": null, "code": "import React,{useEffect,useRef,useState,useCallback}from'react';import*as THREE from'three';import{GLTFLoader}from'three/examples/jsm/loaders/GLTFLoader';import{OrbitControls}from'three/examples/jsm/controls/OrbitControls';import{CoordinateConverter}from'../utils/CoordinateConverter';import*as TWEEN from'@tweenjs/tween.js';import*as SkeletonUtils from'three/examples/jsm/utils/SkeletonUtils.js';import mqtt from'mqtt';import{Select,Popover}from'antd';import axios from'axios';// 新增 axios 导入\nimport VideoPlayer from'./VideoPlayer';// 引入摄像头视频播放组件\nimport DevicePopoverContent from'./DevicePopoverContent';// 全局变量\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";let globalVehicleRef=null;let globalTrajectory=[];let currentPointIndex=0;let globalUpdateInterval=null;let targetPosition=null;// 新增：目标位置\nlet currentPosition=null;// 新增：当前位置\nlet isMoving=false;// 新增：移动状态标志\nlet cameraMode='global';// 'global' 或 'follow'\nlet controls=null;// 保存 controls 的引用\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel=null;let preloadedCyclistModel=null;// 新增：非机动车模型\nlet preloadedPeopleModel=null;// 新增：行人模型\nlet preloadedTrafficLightModel=null;// 新增：红绿灯模型\nlet scene=null;// 添加scene全局变量\nlet peopleBaseModel=null;// 存储原始模型数据\nlet skeleton=null;// 添加滤波相关的变量\nlet lastPosition=null;let lastRotation=null;const ALPHA=0.08;// 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions=new Map();// 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations=new Map();// 使用Map存储每个车辆ID的上一次旋转角度\n// MQTT配置\nconst MQTT_CONFIG={broker:window.location.hostname,port:8083,// 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\nbsm:'changli/cloud/v2x/obu/bsm',rsm:'changli/cloud/v2x/rsu/rsm',scene:'changli/cloud/v2x/obu/scene',rsi:'changli/cloud/v2x/rsu/rsi',// 添加 RSI 主题\nspat:'changli/cloud/v2x/rsu/spat'// 添加 SPAT 主题\n};// 修改所有资源的基础URL\nconst BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000';console.log('API_URLxxxx:',process.env.REACT_APP_API_URL);// 添加全局变量来存储所有车辆\nconst vehicleModels=new Map();// 使用Map存储车辆ID和对应的3D模型\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps=new Map();// 用于存储每个设备的最新时间戳\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId=null;// 添加红绿灯相关的全局变量\nlet trafficLightsMap=new Map();// 存储路口ID与红绿灯模型的映射\nlet trafficLightStates=new Map();// 存储路口ID与红绿灯状态的映射\nconst clock=new THREE.Clock();// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId=async()=>{try{const response=await fetch(`${BASE_URL}/api/vehicles/list`);const data=await response.json();if(data&&data.vehicles&&Array.isArray(data.vehicles)){const mainVehicle=data.vehicles.find(v=>v.isMainVehicle===true);if(mainVehicle&&mainVehicle.bsmId){mainVehicleBsmId=mainVehicle.bsmId;console.log('获取主车bsmId成功:',mainVehicleBsmId);return mainVehicleBsmId;}}console.log('未找到主车，使用默认值 BSM01');mainVehicleBsmId='BSM01';// 默认值\nreturn mainVehicleBsmId;}catch(error){console.error('获取主车信息失败:',error);mainVehicleBsmId='BSM01';// 出错时使用默认值\nreturn mainVehicleBsmId;}};// 添加滤波器函数\nconst lowPassFilter=(newValue,lastValue,alpha)=>{if(lastValue===null)return newValue;return alpha*newValue+(1-alpha)*lastValue;};// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition=(newPos,vehicleId)=>{// 如果没有提供车辆ID，使用全局变量（用于主车）\nif(!vehicleId){if(!lastPosition){lastPosition=newPos.clone();return newPos;}const filteredX=lowPassFilter(newPos.x,lastPosition.x,ALPHA);const filteredY=lowPassFilter(newPos.y,lastPosition.y,ALPHA);const filteredZ=lowPassFilter(newPos.z,lastPosition.z,ALPHA);lastPosition.set(filteredX,filteredY,filteredZ);return lastPosition.clone();}// 针对特定车辆ID的滤波\nif(!vehicleLastPositions.has(vehicleId)){vehicleLastPositions.set(vehicleId,newPos.clone());return newPos;}const lastPos=vehicleLastPositions.get(vehicleId);// 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\nconst distance=lastPos.distanceTo(newPos);const MAX_DISTANCE_THRESHOLD=50;// 最大距离阈值，超过此距离认为是位置跳变\nif(distance>MAX_DISTANCE_THRESHOLD){console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);vehicleLastPositions.set(vehicleId,newPos.clone());return newPos;}// 正常滤波处理\nconst filteredX=lowPassFilter(newPos.x,lastPos.x,ALPHA);const filteredY=lowPassFilter(newPos.y,lastPos.y,ALPHA);const filteredZ=lowPassFilter(newPos.z,lastPos.z,ALPHA);const filteredPos=new THREE.Vector3(filteredX,filteredY,filteredZ);vehicleLastPositions.set(vehicleId,filteredPos.clone());return filteredPos;};// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation=(newRotation,vehicleId)=>{// 如果没有提供车辆ID，使用全局变量（用于主车）\nif(!vehicleId){if(lastRotation===null){lastRotation=newRotation;return newRotation;}// 处理角度跳变（从360度到0度或反之）\nlet diff=newRotation-lastRotation;if(diff>Math.PI)diff-=2*Math.PI;if(diff<-Math.PI)diff+=2*Math.PI;const filteredRotation=lowPassFilter(lastRotation+diff,lastRotation,ALPHA);lastRotation=filteredRotation;return filteredRotation;}// 针对特定车辆ID的滤波\nif(!vehicleLastRotations.has(vehicleId)){vehicleLastRotations.set(vehicleId,newRotation);return newRotation;}const lastRot=vehicleLastRotations.get(vehicleId);// 处理角度跳变（从360度到0度或反之）\nlet diff=newRotation-lastRot;if(diff>Math.PI)diff-=2*Math.PI;if(diff<-Math.PI)diff+=2*Math.PI;// 检查是否是大角度变化，如果是则不进行过滤\nconst MAX_ANGLE_THRESHOLD=Math.PI/2;// 90度\nif(Math.abs(diff)>MAX_ANGLE_THRESHOLD){console.log(`车辆${vehicleId}朝向变化过大(${(diff*180/Math.PI).toFixed(2)}度)，不进行滤波`);vehicleLastRotations.set(vehicleId,newRotation);return newRotation;}const filteredRotation=lowPassFilter(lastRot+diff,lastRot,ALPHA);vehicleLastRotations.set(vehicleId,filteredRotation);return filteredRotation;};// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL=1;// 每秒更新一次\n// ... existing code ...\n// 在文件顶部添加\nconst mixersToCleanup=new Set();const bonesMap=new Map();// 在文件顶部添加资源管理器\nconst resourceManager={mixers:new Set(),bones:new Map(),actions:new Map(),models:new Set(),addMixer(mixer,model){this.mixers.add(mixer);if(model){this.models.add(model);// 记录骨骼\nmodel.traverse(object=>{if(object.isBone){this.bones.set(object.uuid,object);}});}return mixer;},addAction(action,mixer){if(!this.actions.has(mixer)){this.actions.set(mixer,new Set());}this.actions.get(mixer).add(action);return action;},removeMixer(mixer){if(this.mixers.has(mixer)){try{// 停止并清理所有动作\nif(this.actions.has(mixer)){this.actions.get(mixer).forEach(action=>{if(action&&typeof action.stop==='function'){action.stop();}});this.actions.delete(mixer);}// 停止混合器\nif(typeof mixer.stopAllAction==='function'){mixer.stopAllAction();}// 清理混合器的根对象\nconst root=mixer.getRoot();if(root){this.models.delete(root);root.traverse(object=>{if(object&&object.isBone){this.bones.delete(object.uuid);}if(object&&object.animations){object.animations.length=0;}});// 安全地清理缓存\ntry{if(typeof mixer.uncacheRoot==='function'){mixer.uncacheRoot(root);}if(typeof mixer.uncacheAction==='function'){mixer.uncacheAction(null,root);}// 这里是发生错误的地方，添加防御性检查\nif(typeof mixer.uncacheClip==='function'){// 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\nconst clips=mixer._actions.map(a=>a&&a._clip).filter(Boolean);clips.forEach(clip=>{if(clip&&clip.uuid){mixer.uncacheClip(clip);}});}}catch(e){console.log('在清理动画混合器时发生非致命错误:',e);// 继续执行其余清理操作\n}}this.mixers.delete(mixer);}catch(error){console.error('清理动画混合器时发生错误:',error);// 确保即使出错也会从集合中移除\nthis.mixers.delete(mixer);}}},cleanup(){try{// 清理动作\nthis.actions.forEach((actions,mixer)=>{try{actions.forEach(action=>{if(action&&typeof action.stop==='function'){action.stop();}});actions.clear();}catch(e){console.log('清理动作时发生非致命错误:',e);}});this.actions.clear();// 清理混合器\nthis.mixers.forEach(mixer=>{try{if(typeof mixer.stopAllAction==='function'){mixer.stopAllAction();}const root=mixer.getRoot();if(root){root.traverse(object=>{if(object&&object.animations){object.animations.length=0;}});// 安全清理\nif(typeof mixer.uncacheRoot==='function'){mixer.uncacheRoot(root);}if(typeof mixer.uncacheAction==='function'){mixer.uncacheAction(null,root);}// 安全清理动画片段\ntry{if(mixer._actions&&Array.isArray(mixer._actions)){const clips=mixer._actions.map(a=>a&&a._clip).filter(Boolean);clips.forEach(clip=>{if(clip&&clip.uuid&&typeof mixer.uncacheClip==='function'){mixer.uncacheClip(clip);}});}}catch(e){console.log('清理动画片段时发生非致命错误:',e);}}}catch(e){console.log('清理混合器时发生非致命错误:',e);}});this.mixers.clear();// 清理骨骼\nthis.bones.forEach(bone=>{if(bone.parent){bone.parent.remove(bone);}if(bone.matrix)bone.matrix.identity();if(bone.matrixWorld)bone.matrixWorld.identity();});this.bones.clear();// 清理模型\nthis.models.forEach(model=>{if(model.parent){model.parent.remove(model);}model.traverse(object=>{if(object.isMesh){if(object.geometry){object.geometry.dispose();}if(object.material){if(Array.isArray(object.material)){object.material.forEach(material=>{if(material.map)material.map.dispose();material.dispose();});}else{if(object.material.map)object.material.map.dispose();object.material.dispose();}}}if(object.animations){object.animations.length=0;}});});this.models.clear();}catch(error){console.error('全局清理过程中发生错误:',error);// 即使发生错误也尝试清空集合\nthis.actions.clear();this.mixers.clear();this.bones.clear();this.models.clear();}}};// 修改创建动画混合器的函数\nconst createAnimationMixer=model=>{const mixer=new THREE.AnimationMixer(model);return resourceManager.addMixer(mixer,model);};// 修改动画动作创建的函数\nconst createAction=(clip,mixer,model)=>{const action=mixer.clipAction(clip,model);return resourceManager.addAction(action,mixer);};// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds=new Set();// 添加事件去重相关的全局变量\nlet eventListCache=[];// 事件列表缓存，存储所有事件的完整信息\nlet eventIdCounter=1;// 事件ID计数器\nlet eventMarkers=new Map();// 存储事件标记的映射，key为eventId，value为THREE对象\nconst CampusModel=_ref=>{let{className,onCurrentRSUChange,selectedRSUs}=_ref;const containerRef=useRef(null);const vehicleRef=useRef(null);const converter=useRef(new CoordinateConverter());const trajectoryRef=useRef([]);const currentPointRef=useRef(0);const mqttClientRef=useRef(null);const animationFrameRef=useRef(null);const mapRef=useRef(null);// 添加相机平滑过渡的变量\nconst lastCameraPosition=useRef(null);const lastCameraTarget=useRef(null);const cameraSmoothing=0.98;// 平滑系数，值越大越平滑 (0-1之间)\n// 添加行人动画相关的引用\nconst prevAnimationTimeRef=useRef(Date.now());const peopleAnimationMixers=new Map();// 使用全局Map存储所有行人的动画混合器（不再使用useRef）\nconst peopleAnimations=useRef([]);// 存储行人动画数据\n// 设备数据 state\nconst[devicesData,setDevicesData]=useState({devices:[]});// 动态加载设备数据\nconst loadDevicesData=async()=>{let devicesArray=[];try{// const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst apiUrl=BASE_URL;const response=await axios.get(`${apiUrl}/api/devices`);if(response.data&&response.data.success){devicesArray=response.data.data;}console.log('devicesArrayapiUrl',apiUrl);console.log('devicesArray',devicesArray);}catch(error){try{const response=await fetch('/src/data/devices.json');const json=await response.json();devicesArray=json.devices||[];}catch(e){console.error('设备数据加载失败',e);}}setDevicesData({devices:devicesArray});};useEffect(()=>{loadDevicesData();},[]);// 路口数据 state\nconst[intersections,setIntersections]=useState([]);// 动态加载路口数据\nuseEffect(()=>{const fetchIntersections=async()=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/intersections`);if(response.data&&response.data.success){setIntersections(response.data.data||[]);}}catch(error){console.error('获取路口信息失败:',error);}};fetchIntersections();},[]);// 添加车辆状态\nconst[vehicleState,setVehicleState]=useState({longitude:0,latitude:0,speed:0,heading:0});// 在 CampusModel 组件中添加状态\nconst[viewMode,setViewMode]=useState('global');// 添加视角切换按钮的样式\nconst buttonContainerStyle={position:'fixed',bottom:'20px',left:'50%',transform:'translateX(-50%)',zIndex:1000,// 改为1000，避免遮挡点击\ndisplay:'flex',gap:'10px'};const buttonStyle={padding:'8px 16px',backgroundColor:'rgba(255, 255, 255, 0.9)',border:'1px solid #ddd',borderRadius:'4px',cursor:'pointer',fontSize:'14px',boxShadow:'0 2px 4px rgba(0,0,0,0.1)',transition:'all 0.3s ease'};// 添加相机引用\nconst cameraRef=useRef(null);// 添加路口选择相关代码\nconst[selectedIntersection,setSelectedIntersection]=useState(null);// 添加红绿灯状态弹出窗口相关状态\nconst[trafficLightPopover,setTrafficLightPopover]=useState({visible:false,interId:null,position:{x:0,y:0},content:null,phases:[]});// 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\nconst currentPopoverIdRef=useRef(null);// 添加红绿灯状态自动更新定时器引用\nconst trafficLightUpdateTimerRef=useRef(null);// 全局存储setTrafficLightPopover函数引用\nwindow._setTrafficLightPopover=setTrafficLightPopover;// 将Ref暴露给全局以便弹窗函数使用\nwindow.currentPopoverIdRef=currentPopoverIdRef;window.trafficLightUpdateTimerRef=trafficLightUpdateTimerRef;// 修改路口选择器的样式\nconst intersectionSelectStyle={position:'fixed',top:'65px',// 从 60px 改为 65px\nleft:'50%',transform:'translateX(-50%)',width:'200px',// 从 300px 改为 200px\nzIndex:1001,backgroundColor:'white',borderRadius:'4px',boxShadow:'0 2px 8px rgba(0,0,0,0.1)'};// 添加文字标签样式\nconst labelStyle={position:'fixed',top:'65px',left:'calc(50% - 90px)',// 从 140px 改为 110px，让文字更靠近选择框\ntransform:'translateX(-100%)',padding:'0 5px',// 从 10px 改为 5px，减少内边距\nlineHeight:'32px',color:'#fff',fontSize:'14px',fontWeight:'bold',textShadow:'0 1px 2px rgba(0,0,0,0.3)',zIndex:1001};// 添加交通灯映射状态\nconst[trafficLightsMap]=useState(new Map());// 添加当前RSU状态\nconst[currentRSU,setCurrentRSU]=useState(null);// 添加设备时间戳状态\nconst[deviceTimestamps,setDeviceTimestamps]=useState(new Map());// 添加最后一条消息状态\nconst[lastMessage,setLastMessage]=useState({topic:'',content:''});// 设备信息弹框状态\nconst[devicePopover,setDevicePopover]=useState({visible:false,deviceId:null,position:{x:0,y:0},content:null});// 设备弹框关闭函数\nconst handleCloseDevicePopover=()=>{setDevicePopover({visible:false,deviceId:null,position:{x:0,y:0},content:null});};// 设备弹框内容渲染函数\nconst renderDevicePopoverContent=device=>{if(!device)return null;return/*#__PURE__*/_jsx(DevicePopoverContent,{device:device});};// 修改视角切换函数\nconst switchToFollowView=()=>{if(cameraMode!=='follow'){console.log('切换到跟随视角');cameraMode='follow';// 重置相机平滑变量，确保切换视角时不会有突兀的过渡\nlastCameraPosition.current=null;lastCameraTarget.current=null;if(controls){controls.enabled=false;}}};const switchToGlobalView=()=>{if(cameraMode!=='global'){console.log('切换到全局视角');cameraMode='global';// 重置相机平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;if(cameraRef.current&&controls){// 获取当前相机位置和朝向\n// const currentPos = cameraRef.current.position.clone();\ncameraRef.current.position.set(0,500,0);const currentPos=cameraRef.current.position.clone();const currentUp=cameraRef.current.up.clone();// 创建相机位置的补间动画\nnew TWEEN.Tween(currentPos).to({x:0,y:300,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{cameraRef.current.position.copy(currentPos);}).start();// 创建相机上方向的补间动画\nnew TWEEN.Tween(currentUp).to({x:0,y:1,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{cameraRef.current.up.copy(currentUp);}).start();// 获取当前控制器目标点\ncontrols.target.set(0,0,0);const currentTarget=controls.target.clone();// 创建目标点的补间动画\nnew TWEEN.Tween(currentTarget).to({x:0,y:0,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{controls.target.copy(currentTarget);// 确保相机始终朝向目标点\ncameraRef.current.lookAt(controls.target);controls.update();}).start();// 启用控制器\ncontrols.enabled=true;// 重置控制器的一些属性\ncontrols.minDistance=50;controls.maxDistance=500;controls.maxPolarAngle=Math.PI/2.1;controls.minPolarAngle=0;controls.update();// 强制更新相机矩阵\ncameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);console.log('切换到全局视角',{目标相机位置:[0,300,0],目标控制点:[0,0,0],动画已启动:true});}}};// 修改处理路口选择的函数\nconst handleIntersectionChange=value=>{const intersection=intersections.find(i=>i.name===value);if(intersection&&cameraRef.current&&controls){setSelectedIntersection(intersection);// 使用 wgs84ToModel 方法转换经纬度到模型坐标\nconst modelCoords=converter.current.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));console.log('路口坐标转换结果:',{路口名称:intersection.name,经纬度:{longitude:intersection.longitude,latitude:intersection.latitude},模型坐标:modelCoords});// 设置为路口视角模式\ncameraMode='intersection';setViewMode('intersection');// 直接设置相机位置\ncameraRef.current.position.set(modelCoords.x+50,70,-modelCoords.y+50);// 直接设置控制器目标点\ncontrols.target.set(modelCoords.x,0,-modelCoords.y);// 确保相机朝向目标点\ncameraRef.current.lookAt(controls.target);// 更新控制器\ncontrols.enabled=true;controls.update();// 强制更新相机矩阵\ncameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);console.log('相机已直接移动到路口:',{路口名称:intersection.name,相机位置:cameraRef.current.position.toArray(),目标点:controls.target.toArray(),模型坐标:modelCoords});// 如果该路口有红绿灯，自动显示红绿灯弹窗\nif(intersection.hasTrafficLight!==false&&intersection.interId){console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);// 延迟300ms调用以确保场景已更新\nsetTimeout(()=>{// 检查多种可能的ID格式\nlet interId=intersection.interId;// 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\nif(window.showTrafficLightPopup){window.showTrafficLightPopup(interId);console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);}else{console.error('找不到显示红绿灯弹窗的函数');}},300);}else{console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);// 如果有弹窗正在显示，则关闭它\nif(window._setTrafficLightPopover){window._setTrafficLightPopover({visible:false});}}}};// 修改处理MQTT消息的函数\nconst handleMqttMessage=(topic,message)=>{var _payload$data3,_payload$data4,_payload$data5,_payload$data6,_payload$data7,_payload$data8,_payload$data9,_payload$data10,_payload$data11,_payload$data12;try{const payload=JSON.parse(message);// 处理RSM消息\nif(topic===MQTT_CONFIG.rsm){var _payload$data;// console.log('收到RSM消息:', payload);\n// 检查设备时间戳\nconst deviceMac=payload.mac;const messageTimestamp=payload.tm;// 获取该设备的最新时间戳\nconst lastTimestamp=deviceTimestamps.get(deviceMac);// 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\nif(lastTimestamp&&messageTimestamp<lastTimestamp){// console.log('忽略过期的RSM消息:', {\n//   设备MAC: deviceMac,\n//   消息时间戳: messageTimestamp,\n//   最新时间戳: lastTimestamp\n// });\nreturn;}// 更新设备的最新时间戳\ndeviceTimestamps.set(deviceMac,messageTimestamp);// console.log('RSM消息时间戳更新:', {\n//   设备MAC: deviceMac,\n//   时间戳: messageTimestamp,\n//   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n// });\nconst participants=((_payload$data=payload.data)===null||_payload$data===void 0?void 0:_payload$data.participants)||[];const rsuid=payload.data.rsuid;// 分类处理不同类型的参与者\nconst now=Date.now();// 处理所有参与者\nparticipants.forEach(participant=>{// const id = participant.partPtcId;\n// const id =  rsuid + participant.partPtcId;\nconst id=deviceMac+participant.partPtcId;const type=participant.partPtcType;if(type==='3'||type==='2'||type==='1'){// if(type === '3'){\n// if(type === '3'||type === '1'){\n// 解析位置和状态信息\nconst state={longitude:parseFloat(participant.partPosLong),latitude:parseFloat(participant.partPosLat),speed:parseFloat(participant.partSpeed),heading:parseFloat(participant.partHeading)};const modelPos=converter.current.wgs84ToModel(state.longitude,state.latitude);// 根据类型选择对应的预加载模型\nlet preloadedModel;switch(type){case'1':// 机动车\npreloadedModel=preloadedVehicleModel;break;case'2':// 非机动车\npreloadedModel=preloadedCyclistModel;break;case'3':// 行人\npreloadedModel=preloadedPeopleModel;break;default:return;// 跳过未知类型\n}// 获取或创建模型\nlet model=vehicleModels.get(id);if(!model&&preloadedModel){// 创建新模型实例\nconst newModel=type==='3'?SkeletonUtils.clone(preloadedPeopleModel):preloadedModel.clone();// 根据类型调整高度和缩放\nconst height=type==='3'?2.0:1.0;newModel.position.set(modelPos.x,height,-modelPos.y);newModel.rotation.y=Math.PI-state.heading*Math.PI/180;// 如果是行人类型，设置缩放和创建动画\nif(type==='3'){// newModel.scale.set(0.005, 0.005, 0.005);\nnewModel.scale.set(4,4,4);// 使用resourceManager创建并管理动画混合器\nconst mixer=createAnimationMixer(newModel);if(peopleBaseModel&&peopleBaseModel.animations&&peopleBaseModel.animations.length>0){// 只创建一个动作并添加到资源管理器\nconst action=createAction(peopleBaseModel.animations[0],mixer,newModel);action.play();}// 保存到全局Map中(不再使用useRef)\npeopleAnimationMixers.set(id,mixer);}scene.add(newModel);vehicleModels.set(id,{model:newModel,lastUpdate:now,type:type});}else if(model){// 更新现有模型\nmodel.model.position.set(modelPos.x,model.type==='3'?2.0:1.0,-modelPos.y);model.model.rotation.y=Math.PI-state.heading*Math.PI/180;model.lastUpdate=now;model.model.updateMatrix();model.model.updateMatrixWorld(true);}}});// 清理长时间未更新的模型\nconst CLEANUP_THRESHOLD=1000;const currentIds=new Set(participants.map(p=>deviceMac+p.partPtcId));vehicleModels.forEach((modelData,id)=>{if(now-modelData.lastUpdate>CLEANUP_THRESHOLD&&!currentIds.has(id)){// 如果是行人，清理动画混合器\nif(modelData.type==='3'&&peopleAnimationMixers.has(id)){const mixer=peopleAnimationMixers.get(id);// 使用resourceManager清理混合器\nresourceManager.removeMixer(mixer);peopleAnimationMixers.delete(id);}// 从场景移除模型\nscene.remove(modelData.model);vehicleModels.delete(id);}});return;}// 处理BSM消息\nif(topic===MQTT_CONFIG.bsm){// console.log('收到BSM消息:', payload);\nconst bsmData=payload.data;const bsmid=bsmData.bsmId;const newState={longitude:parseFloat(bsmData.partLong),latitude:parseFloat(bsmData.partLat),speed:parseFloat(bsmData.partSpeed),heading:parseFloat(bsmData.partHeading)};// console.log('解析后的车辆状态:', newState);\n// console.log('车辆ID:', bsmid);\n// 通知RealTimeTraffic组件已收到真实BSM消息\nwindow.postMessage({type:'realBsmReceived',source:'CampusModel'},'*');// 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\nwindow.postMessage({type:'bsm',bsmId:bsmid,// 直接传递车辆ID\ndata:{// 同时提供完整的BSM数据\nbsmId:bsmid,partSpeed:bsmData.partSpeed,partLat:bsmData.partLat,partLong:bsmData.partLong,partHeading:bsmData.partHeading}},'*');// 获取模型位置坐标\nconst modelPos=converter.current.wgs84ToModel(newState.longitude,newState.latitude);const initialPosition=new THREE.Vector3(modelPos.x,1.0,-modelPos.y);const initialRotation=Math.PI-newState.heading*Math.PI/180;// 应用平滑滤波 - 使用已有的滤波函数\nconst newPosition=filterPosition(initialPosition,bsmid);const newRotation=filterRotation(initialRotation,bsmid);// 检查该车辆是否已存在于场景中\nlet vehicleObj=vehicleModels.get(bsmid);// 检查是否是主车\nconst isMainVehicle=bsmid===mainVehicleBsmId;if(!vehicleObj&&preloadedVehicleModel){// 创建一个新的车辆模型实例\nconst newVehicleModel=preloadedVehicleModel.clone();// 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n// 这样可以避免车辆突然出现的视觉冲击\nnewVehicleModel.position.set(newPosition.x,-5,newPosition.z);newVehicleModel.rotation.y=newRotation;// 设置BSM车辆为突出的颜色\nnewVehicleModel.traverse(child=>{if(child.isMesh&&child.material){// // 保存原始材质颜色\n// if (!child.userData.originalColor && child.material.color) {\n//   child.userData.originalColor = child.material.color.clone();\n// }\n// // 设置为更鲜艳的颜色\n// if (isMainVehicle) {\n//   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n// } else {\n//   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n// }\n// // 增加材质亮度\n// child.material.emissive = new THREE.Color(0x222222);\n// // 初始设置为半透明\n// child.material.transparent = true;\n// child.material.opacity = 0.6;\n// child.material.needsUpdate = true;\nconst newMaterial=child.material.clone();child.material=newMaterial;// 修改颜色逻辑（与原模型解耦）\nif(isMainVehicle){newMaterial.color.set(0x00BFFF);}else{newMaterial.color.set(0xFF6347);}newMaterial.emissive=new THREE.Color(0x222222);newMaterial.transparent=true;// newMaterial.opacity = 0.6;\nnewMaterial.needsUpdate=true;}});// 创建速度显示标签\nconst speedLabel=createTextSprite(`${Math.round(newState.speed)} km/h`,{backgroundColor:isMainVehicle?{r:0,g:191,b:255,a:0.8}:// 主车使用蓝色背景\n{r:255,g:99,b:71,a:0.8},// 其他车辆使用红色背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文本\nfontSize:10,padding:8});speedLabel.position.set(0,7,0);// 位于车辆顶部上方\nspeedLabel.renderOrder=1000;// 确保在最上层渲染\nspeedLabel.material.opacity=0.6;// 初始半透明\nnewVehicleModel.add(speedLabel);scene.add(newVehicleModel);// 保存车辆引用到车辆模型集合中\nvehicleModels.set(bsmid,{model:newVehicleModel,lastUpdate:Date.now(),type:'1',// 设置为机动车类型\nisMain:isMainVehicle,speedLabel:speedLabel// 保存速度标签引用\n});// console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n// 使用补间动画使车辆从地下逐渐显示出来\nnew TWEEN.Tween(newVehicleModel.position).to({y:0.5},500).easing(TWEEN.Easing.Quadratic.Out).start();// 使用补间动画使车辆从半透明变为完全不透明\nnewVehicleModel.traverse(child=>{if(child.isMesh&&child.material&&child.material.transparent){new TWEEN.Tween({opacity:0.6}).to({opacity:1.0},500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function(){child.material.opacity=this.opacity;child.material.needsUpdate=true;}).start();}});// 为速度标签也添加透明度动画\nnew TWEEN.Tween({opacity:0.6}).to({opacity:1.0},500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function(){speedLabel.material.opacity=this.opacity;speedLabel.material.needsUpdate=true;}).start();// 如果是主车，设置全局引用\nif(isMainVehicle){globalVehicleRef=newVehicleModel;setVehicleState(newState);console.log('设置全局主车引用:',bsmid);}}else if(vehicleObj){// 应用滤波\nconst filteredPosition=filterPosition(newPosition,bsmid);const filteredRotation=filterRotation(newRotation,bsmid);// 更新现有车辆位置和朝向\nvehicleObj.model.position.copy(filteredPosition);vehicleObj.model.rotation.y=filteredRotation;vehicleObj.model.updateMatrix();vehicleObj.model.updateMatrixWorld(true);vehicleObj.lastUpdate=Date.now();vehicleObj.isMain=isMainVehicle;// 更新主车状态\n// 更新速度标签文本\nif(vehicleObj.speedLabel){vehicleObj.speedLabel.material.map.dispose();vehicleObj.model.remove(vehicleObj.speedLabel);}// 创建新的速度标签\nconst speedLabel=createTextSprite(`${Math.round(newState.speed*3.6)} km/h`,{backgroundColor:isMainVehicle?{r:0,g:191,b:255,a:0.8}:// 主车使用蓝色背景\n{r:255,g:99,b:71,a:0.8},// 其他车辆使用红色背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文本\nfontSize:20,padding:8});speedLabel.position.set(0,15,0);// 位于车辆顶部上方\nspeedLabel.renderOrder=1000;// 确保在最上层渲染\nvehicleObj.model.add(speedLabel);vehicleObj.speedLabel=speedLabel;// console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n// 如果是主车，同时更新全局引用和状态（用于相机跟随）\nif(isMainVehicle){globalVehicleRef=vehicleObj.model;setVehicleState(newState);}}// 清理长时间未更新的车辆\nconst now=Date.now();const CLEANUP_THRESHOLD=1000;// 增加到10秒，避免频繁清理造成闪烁\nvehicleModels.forEach((modelData,id)=>{const timeSinceLastUpdate=now-modelData.lastUpdate;// 对于即将超时的车辆，先降低透明度，而不是直接移除\nif(timeSinceLastUpdate>CLEANUP_THRESHOLD*0.7&&timeSinceLastUpdate<=CLEANUP_THRESHOLD){// const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\nconst opacity=1;modelData.model.traverse(child=>{if(child.isMesh&&child.material){// 如果材质还没有透明度设置，先保存初始状态\nif(child.material.transparent===undefined){child.material.originalTransparent=child.material.transparent||false;child.material.originalOpacity=child.material.opacity||1.0;}// 设置透明度\nchild.material.transparent=true;child.material.opacity=opacity;child.material.needsUpdate=true;}});// 如果有速度标签，也调整其透明度\nif(modelData.speedLabel){modelData.speedLabel.material.opacity=opacity;modelData.speedLabel.material.needsUpdate=true;}}// 完全超时，移除车辆\nelse if(timeSinceLastUpdate>CLEANUP_THRESHOLD){// 清理资源\nif(modelData.speedLabel){modelData.speedLabel.material.map.dispose();modelData.speedLabel.material.dispose();modelData.model.remove(modelData.speedLabel);}modelData.model.traverse(child=>{if(child.isMesh){if(child.material){if(Array.isArray(child.material)){child.material.forEach(m=>m.dispose());}else{child.material.dispose();}}if(child.geometry)child.geometry.dispose();}});// 从场景中移除\nscene.remove(modelData.model);vehicleModels.delete(id);// 同时清除该车辆的滤波缓存\nvehicleLastPositions.delete(id);vehicleLastRotations.delete(id);console.log(`移除长时间未更新的车辆: ID ${id}`);}});return;}// SPAT消息处理\nif(topic===MQTT_CONFIG.spat){// console.log('收到SPAT消息:', message);\ntry{const payload=JSON.parse(message);// 检查设备时间戳\nconst deviceMac=payload.mac;const messageTimestamp=payload.tm;// 获取该设备的最新时间戳\nconst lastTimestamp=deviceTimestamps.get(deviceMac);// 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\nif(lastTimestamp&&messageTimestamp<lastTimestamp){// console.log('忽略过期的SPAT消息:', {\n//   设备MAC: deviceMac,\n//   消息时间戳: messageTimestamp,\n//   最新时间戳: lastTimestamp\n// });\nreturn;}// 更新设备时间戳\ndeviceTimestamps.set(deviceMac,messageTimestamp);// 修改：访问data.intersections而不是直接访问intersections\nif(payload.data&&payload.data.intersections&&Array.isArray(payload.data.intersections)){payload.data.intersections.forEach(intersection=>{const interId=intersection.interId;if(!interId){console.error('SPAT消息缺少interId:',intersection);return;}// console.log(`处理路口ID: ${interId} 的SPAT消息`);\n// 创建所有相位的数组 - 存储到trafficLightStates\nif(intersection.phases&&Array.isArray(intersection.phases)){// 构建存储相位信息的数组\nconst phasesInfo=[];intersection.phases.forEach(phase=>{// 修改：使用phaseId而不是id\nif(!phase.phaseId){console.error('相位信息缺少phaseId:',phase);return;}const phaseId=phase.phaseId.toString();// 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\nconst direction=phase.trafficDirec?getDirectionFromCode(phase.trafficDirec):getPhaseDirection(phaseId);// 修改：直接从phase中获取信号灯状态和剩余时间\nconst lightState=phase.trafficLight||'R';// 默认为红灯\nconst remainTime=parseInt(phase.remainTime)||0;// console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n// 构建相位信息对象\nconst phaseInfo={phaseId,direction,trafficLight:lightState,remainTime};// 添加到相位信息数组\nphasesInfo.push(phaseInfo);// 查找红绿灯模型并更新视觉效果\n// 尝试使用字符串ID和数字ID查找\nlet trafficLightKey=String(interId);let trafficLightModel=trafficLightsMap.get(trafficLightKey);if(!trafficLightModel){// 尝试使用数字ID\ntrafficLightKey=parseInt(interId);trafficLightModel=trafficLightsMap.get(trafficLightKey);}if(trafficLightModel){// 更新交通灯视觉效果\nupdateTrafficLightVisual(trafficLightModel,phaseInfo);// 更新弹出窗信息\nif(selectedIntersection&&selectedIntersection.interId===interId){setTrafficLightPopover(prev=>({...prev,visible:true,phaseId,direction,state:lightState,remainTime}));}}else{// console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n}});// 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\nlet modelKey=null;// 尝试字符串ID\nconst strId=String(interId);if(trafficLightsMap.has(strId)){modelKey=strId;}else{// 尝试数字ID\nconst numId=parseInt(interId);if(trafficLightsMap.has(numId)){modelKey=numId;}}if(modelKey!==null){// 使用正确的ID类型存储状态信息\ntrafficLightStates.set(modelKey,{updateTime:Date.now(),phases:phasesInfo});console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);// 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\nif(window.currentPopoverIdRef&&(window.currentPopoverIdRef.current===modelKey||window.currentPopoverIdRef.current===String(modelKey)||window.currentPopoverIdRef.current===parseInt(modelKey))){console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);// 强制更新弹窗ID为当前数据的正确ID类型\nwindow.currentPopoverIdRef.current=modelKey;// 如果没有更新定时器则创建一个\nif(window.trafficLightUpdateTimerRef&&!window.trafficLightUpdateTimerRef.current){console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);setTimeout(()=>{window.showTrafficLightPopup(modelKey);},100);}}}else{// 如果找不到模型，仍使用原始ID存储\ntrafficLightStates.set(interId,{updateTime:Date.now(),phases:phasesInfo});// console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n}}else{console.error('SPAT消息缺少相位信息:',intersection);}});}else{console.error('SPAT消息格式错误，缺少data.intersections数组:',payload);}}catch(error){console.error('解析SPAT消息出错:',error,message);}return;}// 处理 RSI 消息\nif(topic===MQTT_CONFIG.rsi&&payload.type==='RSI'){// console.log('收到RSI消息:', payload);\n// 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\nwindow.postMessage({type:'RSI',data:payload.data,mac:payload.mac,tm:payload.tm},'*');const rsiData=payload.data;const rsuId=rsiData.rsuId;const events=rsiData.rtes||[];events.forEach(event=>{var _payload$data2;const eventId=event.rteId;const eventType=event.eventType;const description=event.description;const startTime=event.startTime;const endTime=event.endTime;// 将基准点经纬度转换为模型坐标\nconst modelPos=converter.current.wgs84ToModel(parseFloat(rsiData.posLong),parseFloat(rsiData.posLat));// 根据事件类型显示不同的提示或标记\nlet warningText='';let warningColor='';switch(eventType){case'401':// 道路抛洒物\nwarningText='道路抛洒物';warningColor='#ff4d4f';break;case'404':// 道路障碍物\nwarningText='道路障碍物';warningColor='#faad14';break;case'405':// 行人通过马路\nwarningText='行人通过马路';warningColor='#1890ff';break;case'904':// 逆行车辆\nwarningText='逆行车辆';warningColor='#f5222d';break;case'910':// 违停车辆\nwarningText='违停车辆';warningColor='#722ed1';break;case'1002':// 道路施工\nwarningText='道路施工';warningColor='#fa8c16';break;case'901':// 车辆超速\nwarningText='车辆超速';warningColor='#eb2f96';break;default:warningText=description||'未知事件';warningColor='#8c8c8c';}// 显示警告标记\nshowWarningMarker(modelPos,warningText,warningColor,eventType,{rsuId:((_payload$data2=payload.data)===null||_payload$data2===void 0?void 0:_payload$data2.rsuId)||'UNKNOWN',eventId:eventId,description:description});// console.log('RSI事件处理:', {\n//   事件ID: eventId,\n//   事件类型: eventType,\n//   事件说明: description,\n//   开始时间: startTime,\n//   结束时间: endTime,\n//   位置: modelPos\n// });\n});return;}// 处理场景事件消息\nif(topic===MQTT_CONFIG.scene&&payload.type==='SCENE'){// console.log('收到场景事件消息:', payload);\nconst sceneData=payload.data;const sceneId=sceneData.sceneId;const sceneType=sceneData.sceneType;const sceneDesc=sceneData.sceneDesc;const position={latitude:parseFloat(sceneData.partLat),longitude:parseFloat(sceneData.partLong)};// 将经纬度转换为模型坐标\nconst modelPos=converter.current.wgs84ToModel(position.longitude,position.latitude);// 根据场景类型显示不同的提示或标记\nswitch(sceneType){case'2':// 交叉路口碰撞预警\nshowWarningMarker(modelPos,'交叉路口碰撞预警','#ff4d4f','2',{rsuId:(_payload$data3=payload.data)===null||_payload$data3===void 0?void 0:_payload$data3.rsuId,sceneData});break;case'9-5':// 道路危险状况预警（施工）\nshowWarningMarker(modelPos,'道路施工','#faad14','1002',{rsuId:(_payload$data4=payload.data)===null||_payload$data4===void 0?void 0:_payload$data4.rsuId,sceneData});break;case'9-6':// 前方有障碍物\nshowWarningMarker(modelPos,'前方障碍物','#ff7a45','404',{rsuId:(_payload$data5=payload.data)===null||_payload$data5===void 0?void 0:_payload$data5.rsuId,sceneData});break;case'10':// 限速提醒\nconst speedLimit=sceneData.eventData1;// 限速值\nshowWarningMarker(modelPos,`限速${speedLimit}km/h`,'#1890ff','10',{rsuId:(_payload$data6=payload.data)===null||_payload$data6===void 0?void 0:_payload$data6.rsuId,sceneData});break;case'12':// 交通参与者碰撞预警\nshowWarningMarker(modelPos,'碰撞预警','#f5222d','12',{rsuId:(_payload$data7=payload.data)===null||_payload$data7===void 0?void 0:_payload$data7.rsuId,sceneData});break;case'13':// 绿波车速引导\nshowWarningMarker(modelPos,'绿波引导','#52c41a','13',{rsuId:(_payload$data8=payload.data)===null||_payload$data8===void 0?void 0:_payload$data8.rsuId,sceneData});break;case'21-8':// 禁止鸣笛\nshowWarningMarker(modelPos,'禁止鸣笛','#722ed1','21-8',{rsuId:(_payload$data9=payload.data)===null||_payload$data9===void 0?void 0:_payload$data9.rsuId,sceneData});break;case'34':// 逆行车辆提醒\nshowWarningMarker(modelPos,'逆行警告','#eb2f96','904',{rsuId:(_payload$data10=payload.data)===null||_payload$data10===void 0?void 0:_payload$data10.rsuId,sceneData});break;case'33':// 违章占道车辆预警\nshowWarningMarker(modelPos,'违章占道','#fa8c16','910',{rsuId:(_payload$data11=payload.data)===null||_payload$data11===void 0?void 0:_payload$data11.rsuId,sceneData});break;case'999':// 信号灯优先\nconst priorityType=sceneData.eventData1;// 优先类型\nconst duration=sceneData.eventData2;// 优先时长\nshowWarningMarker(modelPos,`信号优先-${getPriorityTypeText(priorityType)}${duration}秒`,'#13c2c2','999',{rsuId:(_payload$data12=payload.data)===null||_payload$data12===void 0?void 0:_payload$data12.rsuId,sceneData});break;}return;}// 如果不是RSM或BSM消息，则记录为其他类型\n// console.log('未知类型消息:', {\n//   topic,\n//   type: payload.type,\n//   data: payload\n// });\n}catch(error){console.error('处理MQTT消息失败:',error);console.error('原始消息内容:',message);}};// 修改初始化MQTT连接函数\nconst initMqttClient=()=>{console.log('正在连接MQTT服务器...');const wsUrl=`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;console.log('尝试连接WebSocket:',wsUrl);// 创建WebSocket连接，不指定协议\nconst ws=new WebSocket(wsUrl);ws.onopen=()=>{console.log('WebSocket连接成功');};ws.onmessage=event=>{try{const message=JSON.parse(event.data);// 处理连接确认消息\nif(message.type==='connect'){console.log('收到连接确认:',message);return;}// 处理心跳消息\nif(message.type==='ping'){return;}// 处理MQTT消息\nif(message.type==='message'&&message.topic&&message.payload){// 检查消息ID是否已处理过\nif(message.messageId){if(processedMessageIds.has(message.messageId)){// console.log('跳过重复消息:', message.messageId);\nreturn;}// 添加到已处理集合\nprocessedMessageIds.add(message.messageId);// 限制缓存大小，防止内存泄漏\nif(processedMessageIds.size>1000){// 转换为数组，删除最早的100个元素\nconst idsArray=Array.from(processedMessageIds);for(let i=0;i<100;i++){processedMessageIds.delete(idsArray[i]);}}}// 直接将消息传递给handleMqttMessage处理\nhandleMqttMessage(message.topic,JSON.stringify(message.payload));}}catch(error){console.error('处理WebSocket消息失败:',error);}};ws.onerror=error=>{console.error('WebSocket错误:',error);};ws.onclose=()=>{console.log('WebSocket连接关闭');// 5秒后尝试重新连接\nsetTimeout(initMqttClient,5000);};// 保存WebSocket引用\nmqttClientRef.current=ws;};useEffect(()=>{if(!containerRef.current)return;// 预加载所有模型\npreloadModels();// 创建场景\nscene=new THREE.Scene();// 使用全局scene变量\n// 创建相机\nconst camera=new THREE.PerspectiveCamera(60,window.innerWidth/window.innerHeight,0.1,2000);// camera.position.set(0, 300, 0); // 初始为全局视角\ncamera.position.set(0,100,0);// 初始为全局视角\ncamera.lookAt(0,0,0);cameraRef.current=camera;// 创建渲染器\nconst renderer=new THREE.WebGLRenderer({antialias:true});renderer.setSize(window.innerWidth,window.innerHeight);renderer.setClearColor(0x000000);renderer.setPixelRatio(window.devicePixelRatio);containerRef.current.appendChild(renderer.domElement);// 修改光照设置\n// 添加环境光和平行光\nconst ambientLight=new THREE.AmbientLight(0xffffff,0.8);// 增加环境光强度从0.5到0.8\nscene.add(ambientLight);// 添加多个平行光源，从不同角度照亮车辆\nconst directionalLight1=new THREE.DirectionalLight(0xffffff,1.0);// 增加强度从0.8到1.0\ndirectionalLight1.position.set(10,10,10);scene.add(directionalLight1);// 添加第二个平行光源，从另一个角度照亮\nconst directionalLight2=new THREE.DirectionalLight(0xffffff,0.8);directionalLight2.position.set(-10,8,-10);scene.add(directionalLight2);// 添加一个聚光灯，专门照亮车辆\nconst spotLight=new THREE.SpotLight(0xffffff,1.0);spotLight.position.set(0,50,0);spotLight.angle=Math.PI/4;spotLight.penumbra=0.1;spotLight.decay=2;spotLight.distance=200;scene.add(spotLight);// 创建控制器\ncontrols=new OrbitControls(camera,renderer.domElement);controls.enableDamping=true;controls.dampingFactor=0.05;controls.screenSpacePanning=false;controls.minDistance=50;controls.maxDistance=500;controls.maxPolarAngle=Math.PI/2.1;controls.minPolarAngle=0;controls.target.set(0,0,0);controls.update();// 打印相机和控制器引用\nconsole.log('初始化完成',{camera:!!camera,controls:!!controls,cameraRef:!!cameraRef.current});// 修改加载车辆模型的函数\nconst loadVehicleModel=()=>{return new Promise((resolve,reject)=>{const vehicleLoader=new GLTFLoader();vehicleLoader.load(`${BASE_URL}/changli2/vehicle.glb`,gltf=>{const vehicleModel=gltf.scene;// 创建一个新的Group作为根容器\nconst vehicleContainer=new THREE.Group();// 调整模型材质\nvehicleModel.traverse(child=>{if(child.isMesh){// 检查并调整材质\nif(child.material){// 创建新的标准材质\nconst newMaterial=new THREE.MeshStandardMaterial({color:0xffffff,// 白色\nmetalness:0.2,// 降低金属感\nroughness:0.1,// 降低粗糙度\nenvMapIntensity:1.0// 环境贴图强度\n});// 保留原始贴图\nif(child.material.map){newMaterial.map=child.material.map;}// 应用新材质\nchild.material=newMaterial;console.log('已调整车辆材质:',child.name);}}});// 遍历模型的所有子对象，确保它们都被正确添加到容器中\nwhile(vehicleModel.children.length>0){const child=vehicleModel.children[0];vehicleContainer.add(child);}// 确保容器直接添加到场景根节点\nscene.add(vehicleContainer);// 保存容器的引用\nglobalVehicleRef=vehicleContainer;console.log('车辆模型加载成功，使用容器包装');setIsVehicleLoaded(true);resolve(vehicleContainer);},xhr=>{console.log(`车辆模型加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},reject);});};// 修改初始化流程\nconst initializeScene=async()=>{try{// 1. 加载车辆模型\n// const vehicleContainer = await loadVehicleModel();\n// 2. 初始化MQTT客户端\ninitMqttClient();// 3. 设置初始位置\n// if (vehicleContainer) {\n//   const initialState = {\n//     longitude: 113.0022348,\n//     latitude: 28.0698301,\n//     heading: 0\n//   };\n//   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n//   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n//   vehicleContainer.position.set(0, 1.0, 0);\n//   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n//   vehicleContainer.updateMatrix();\n//   vehicleContainer.updateMatrixWorld(true);\n//   currentPosition = vehicleContainer.position.clone();\n// }\n}catch(error){console.error('初始化场景失败:',error);}};// 添加重试逻辑的加载函数\nconst loadModelWithRetry=function(url){let maxRetries=arguments.length>1&&arguments[1]!==undefined?arguments[1]:3;return new Promise((resolve,reject)=>{const attemptLoad=retriesLeft=>{console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);const loader=new GLTFLoader();loader.load(url,gltf=>{console.log(`模型加载成功: ${url}`);resolve(gltf);},xhr=>{console.log(`加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},error=>{console.error(`加载失败: ${url}`,error);if(retriesLeft>0){console.log(`将在 1 秒后重试...`);setTimeout(()=>attemptLoad(retriesLeft-1),1000);}else{reject(error);}});};attemptLoad(maxRetries);});};// 使用重试逻辑加载模型\nconst loader=new GLTFLoader();loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`,async gltf=>{try{const model=gltf.scene;model.scale.set(1,1,1);model.position.set(0,0,0);// 检查scene是否初始化\nif(scene){scene.add(model);// 在校园模型加载完成后初始化场景\nawait initializeScene();}else{console.error('无法添加模型：场景未初始化');}}catch(error){console.error('处理模型时出错:',error);}},xhr=>{console.log(`加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},error=>{console.error('模型加载错误:',error);console.error('错误详情:',{错误类型:error.type,错误消息:error.message,加载URL:`${BASE_URL}/changli2/CLG_ALL_1025.glb`,完整URL:`${BASE_URL}/changli2/CLG_ALL_1025.glb`});});// 修改动画循环\nconst animate=()=>{animationFrameRef.current=requestAnimationFrame(animate);// 更新 TWEEN 动画\nTWEEN.update();// 更新行人动画 - 只更新存在的混合器\nconst deltaTime=clock.getDelta();// 使用Map而不是ref.current\nif(peopleAnimationMixers.size>0){peopleAnimationMixers.forEach(mixer=>{mixer.update(deltaTime);});}if(cameraMode==='follow'&&globalVehicleRef){// 在跟随模式下禁用控制器\ncontrols.enabled=false;// 获取车辆当前位置\nconst vehiclePos=globalVehicleRef.position.clone();// 获取车辆旋转角度\nconst vehicleRotation=globalVehicleRef.rotation.y;// 计算相机偏移量\n// 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\nconst adjustedRotation=-(vehicleRotation-Math.PI)+Math.PI/2*3;// 增加距离到 50 单位\nconst cameraOffset=new THREE.Vector3(-50*Math.cos(adjustedRotation),200,-50*Math.sin(adjustedRotation));// 计算目标相机位置和观察点\nconst targetCameraPosition=vehiclePos.clone().add(cameraOffset);const targetLookAt=vehiclePos.clone();// 初始化上一帧数据（如果是首次）\nif(!lastCameraPosition.current){lastCameraPosition.current=targetCameraPosition.clone();}if(!lastCameraTarget.current){lastCameraTarget.current=targetLookAt.clone();}// 应用平滑处理 - 使用lerp进行线性插值\nlastCameraPosition.current.lerp(targetCameraPosition,1-cameraSmoothing);lastCameraTarget.current.lerp(targetLookAt,1-cameraSmoothing);// 设置相机位置为平滑后的位置\ncamera.position.copy(lastCameraPosition.current);// 重置相机方向\ncamera.up.set(0,1,0);// 设置相机观察点为平滑后的目标\ncamera.lookAt(lastCameraTarget.current);// 强制更新相机矩阵\ncamera.updateProjectionMatrix();camera.updateMatrix();camera.updateMatrixWorld(true);// 禁用控制器\ncontrols.enabled=false;// 确保控制器不会覆盖相机设置\ncontrols.target.copy(lastCameraTarget.current);controls.update();console.log('相机设置:',{车辆位置:vehiclePos.toArray(),相机位置:camera.position.toArray(),相机目标:lastCameraTarget.current.toArray(),相机朝向:camera.getWorldDirection(new THREE.Vector3()).toArray()});}else if(cameraMode==='global'){// 在全局模式或切换模式时重置平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;// 在全局模式下启用控制器\ncontrols.enabled=true;// 确保相机的up向量保持正确\ncamera.up.set(0,1,0);// 如果相机位置偏离太多，重置到默认位置\nif(Math.abs(camera.position.y)<50){camera.position.set(0,300,0);controls.target.set(0,0,0);camera.lookAt(controls.target);controls.update();}//         // 强制更新相机矩阵\n// camera.updateProjectionMatrix();\ncamera.updateMatrix();camera.updateMatrixWorld(true);}else if(cameraMode==='intersection'){// 在路口视角模式下也重置平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;// 路口视角模式\ncontrols.update();}if(controls)controls.update();if(scene&&camera){renderer.render(scene,camera);}};animate();// 处理窗口大小变化\nconst handleResize=()=>{camera.aspect=window.innerWidth/window.innerHeight;camera.updateProjectionMatrix();renderer.setSize(window.innerWidth,window.innerHeight);};window.addEventListener('resize',handleResize);// 添加事件清理定时器\nconst eventCleanupInterval=setInterval(()=>{removeInactiveEvents();},30000);// 每30秒清理一次非活跃事件\n// 添加全局函数用于手动切换视角\nwindow.setGlobalView=()=>{if(cameraRef.current){cameraRef.current.position.set(0,300,0);cameraRef.current.lookAt(0,0,0);cameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);if(controls){controls.target.set(0,0,0);controls.enabled=true;controls.update();}cameraMode='global';console.log('手动切换到全局视角');return true;}return false;};// // 添加全局测试函数用于验证事件去重功能\n// window.test3DEventDeduplication = () => {\n//   console.log('🧪 开始3D场景事件去重测试');\n//   // 测试位置\n//   const testPosition = { x: 100, y: 100 };\n//   // 测试1：创建新事件\n//   console.log('测试1：创建新的违停车辆事件');\n//   showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n//   // 测试2：相同位置的重复事件（应该被去重）\n//   setTimeout(() => {\n//     console.log('测试2：相同位置的重复事件（应该被去重）');\n//     showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n//   }, 1000);\n//   // 测试3：稍微不同位置的事件（距离在阈值内，应该被去重）\n//   setTimeout(() => {\n//     console.log('测试3：稍微不同位置的事件（应该被去重）');\n//     showWarningMarker({ x: 105, y: 105 }, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n//   }, 2000);\n//   // 测试4：不同类型的事件（应该创建新事件）\n//   setTimeout(() => {\n//     console.log('测试4：不同类型的事件（应该创建新事件）');\n//     showWarningMarker(testPosition, '逆行车辆', '#eb2f96', '904', { rsuId: 'TEST_RSU' });\n//   }, 3000);\n//   // 测试5：查看缓存状态\n//   setTimeout(() => {\n//     console.log('📊 3D场景事件缓存状态:', {\n//       缓存事件数: eventListCache.length,\n//       事件ID计数器: eventIdCounter,\n//       场景标记数: eventMarkers.size,\n//       事件列表: eventListCache.map(e => ({\n//         ID: e.eventId,\n//         类型: e.eventType,\n//         更新次数: e.updateCount,\n//         位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`\n//       }))\n//     });\n//   }, 4000);\n// };\n// 添加手动清理3D事件的全局函数\nwindow.cleanup3DEvents=()=>{console.log('🧹 手动清理3D场景事件');removeInactiveEvents();};// 修改清理函数\nreturn()=>{console.log('开始清理组件...');// 1. 停止渲染循环\nif(animationFrameRef.current){cancelAnimationFrame(animationFrameRef.current);animationFrameRef.current=null;}// 2. 清理事件清理定时器\nif(eventCleanupInterval){clearInterval(eventCleanupInterval);}// 3. 清理所有事件标记\neventMarkers.forEach(marker=>{if(scene&&marker){scene.remove(marker);}});eventMarkers.clear();eventListCache.length=0;// 4. 停止所有 TWEEN 动画\nTWEEN.removeAll();// 3. 清理所有动画混合器\npeopleAnimationMixers.forEach(mixer=>{resourceManager.removeMixer(mixer);});peopleAnimationMixers.clear();// 4. 清理所有其他资源\nresourceManager.cleanup();// 5. 清理场景中的所有对象\nif(scene){const objectsToRemove=[];scene.traverse(object=>{if(object.isMesh){if(object.geometry){object.geometry.dispose();}if(object.material){if(Array.isArray(object.material)){object.material.forEach(material=>{if(material.map)material.map.dispose();material.dispose();});}else{if(object.material.map)object.material.map.dispose();object.material.dispose();}}if(object!==scene){objectsToRemove.push(object);}}});// 从场景中移除对象\nobjectsToRemove.forEach(obj=>{if(obj.parent){obj.parent.remove(obj);}});scene.clear();}// 6. 清理渲染器\nif(renderer){renderer.setAnimationLoop(null);if(containerRef.current&&renderer.domElement&&renderer.domElement.parentNode===containerRef.current){containerRef.current.removeChild(renderer.domElement);}renderer.dispose();renderer.forceContextLoss();}// 7. 清理其他资源\nif(controls){controls.dispose();}// 8. 清理数据结构\nvehicleLastPositions.clear();vehicleLastRotations.clear();deviceTimestamps.clear();vehicleModels.clear();trafficLightsMap.clear();trafficLightStates.clear();console.log('组件清理完成');};},[]);// 在组件挂载时获取主车信息和添加事件监听\nuseEffect(()=>{// 初始获取主车信息\nfetchMainVehicleBsmId();// 添加自定义事件监听，用于接收主车变更通知\nconst handleMainVehicleChange=()=>{console.log('接收到主车变更通知，重新获取主车信息');fetchMainVehicleBsmId();};// 监听主车变更事件\nwindow.addEventListener('mainVehicleChanged',handleMainVehicleChange);// 定时刷新主车信息（每分钟一次）\nconst intervalId=setInterval(()=>{fetchMainVehicleBsmId();},60000);// 组件卸载时清理事件监听和定时器\nreturn()=>{window.removeEventListener('mainVehicleChanged',handleMainVehicleChange);clearInterval(intervalId);};},[]);// 添加一个useEffect钩子在场景初始化完成后创建红绿灯\nuseEffect(()=>{// 在场景加载后初始化红绿灯\nif(scene&&converter.current&&intersections&&intersections.length>0){// 延迟一秒创建红绿灯，确保模型已加载\nconst timer=setTimeout(()=>{if(scene&&converter.current&&intersections&&intersections.length>0){// 再次检查，以防延迟期间组件卸载\ncreateTrafficLights(converter.current,intersections);}},2000);return()=>clearTimeout(timer);}else{console.log('场景、坐标转换器或路口数据未准备好，暂不创建红绿灯');}},[scene,intersections]);// 添加点击事件处理\nuseEffect(()=>{if(containerRef.current){// 定义点击处理函数\nconst handleClick=event=>{if(scene&&cameraRef.current){handleMouseClick(event,containerRef.current,scene,cameraRef.current);}};// 添加点击事件监听\ncontainerRef.current.addEventListener('click',handleClick);// 记录到控制台\nconsole.log('已添加点击事件监听器到容器',!!containerRef.current);// 清理函数\nreturn()=>{if(containerRef.current){containerRef.current.removeEventListener('click',handleClick);console.log('已移除点击事件监听器');}};}},[scene,cameraRef.current]);// 初始化场景 - 简化为空函数，避免引用错误\nconst initScene=useCallback(()=>{console.log('initScene函数已禁用');// 原始实现已移除，避免canvasRef未定义的错误\n},[containerRef,setCurrentRSU,trafficLightsMap]);// 创建简单交通灯模型\nconst createSimpleTrafficLight=()=>{const geometry=new THREE.BoxGeometry(4,15,4);const material=new THREE.MeshBasicMaterial({color:0x333333});const trafficLightModel=new THREE.Mesh(geometry,material);// 添加基座\nconst baseGeometry=new THREE.CylinderGeometry(2,2,2,32);const baseMaterial=new THREE.MeshBasicMaterial({color:0x666666});const baseModel=new THREE.Mesh(baseGeometry,baseMaterial);baseModel.position.set(0,-8.5,0);trafficLightModel.add(baseModel);return trafficLightModel;};// 添加额外的点击检测辅助对象\nconst addClickHelpers=()=>{if(!scene)return;// 为每个红绿灯添加一个透明的大型碰撞体，便于点击\ntrafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){// 创建一个较大的碰撞检测几何体\nconst helperGeometry=new THREE.SphereGeometry(3,3,3);const helperMaterial=new THREE.MeshBasicMaterial({color:0xff00ff,//\ntransparent:false,opacity:0.1,// 几乎透明\ndepthWrite:false});const helperMesh=new THREE.Mesh(helperGeometry,helperMaterial);helperMesh.position.set(0,0,0);// 放在红绿灯位置\n// 标记为click helper\nhelperMesh.userData={type:'trafficLight',interId:interId,name:lightObj.intersection.name,isClickHelper:true};// 添加到红绿灯模型\nlightObj.model.add(helperMesh);console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);}});};// 在创建红绿灯之后调用\nuseEffect(()=>{// 等待红绿灯创建完成后添加点击辅助对象\nconst timer=setTimeout(()=>{if(trafficLightsMap.size>0){console.log('添加红绿灯点击辅助对象');// addClickHelpers();\n}},5500);// 延迟略长于debugTrafficLights\nreturn()=>clearTimeout(timer);},[]);// // 在组件加载完毕后调用调试函数\n// useEffect(() => {\n//   // 延迟5秒调用调试函数，确保所有模型都已加载\n//   const timer = setTimeout(() => {\n//     console.log('调用场景调试函数');\n//     if (window.debugScene) {\n//       window.debugScene(); // 使用全局函数\n//     } else {\n//       console.error('debugScene函数未定义');\n//     }\n//   }, 5000);\n//   return () => clearTimeout(timer);\n// }, []);\n// 在useEffect中添加定时器清理逻辑\nuseEffect(()=>{return()=>{// 组件卸载时清理定时器\nif(trafficLightUpdateTimerRef.current){clearInterval(trafficLightUpdateTimerRef.current);trafficLightUpdateTimerRef.current=null;console.log('已清理红绿灯状态更新定时器');}};},[]);// 空依赖数组确保只在组件挂载和卸载时运行\n// 添加关闭弹窗时清理定时器的逻辑\nuseEffect(()=>{if(!trafficLightPopover.visible&&trafficLightUpdateTimerRef.current){clearInterval(trafficLightUpdateTimerRef.current);trafficLightUpdateTimerRef.current=null;currentPopoverIdRef.current=null;console.log('弹窗关闭，已清理红绿灯状态更新定时器');}},[trafficLightPopover.visible]);// 添加自动选择第一个路口的逻辑\nuseEffect(()=>{// 确保路口数据已加载\nif(intersections&&intersections.length>0){// 确保只在组件初次渲染并且未选择路口时执行\nif(!selectedIntersection){// 查找第一个带有红绿灯的路口\nconst firstTrafficLightIntersection=intersections.find(intersection=>intersection.hasTrafficLight!==false&&intersection.interId);// 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\nconst targetIntersection=firstTrafficLightIntersection||intersections[0];console.log('自动选择路口:',targetIntersection.name,'具有红绿灯:',firstTrafficLightIntersection?'是':'否');// 延迟执行，确保场景和相机已初始化\nconst timer=setTimeout(()=>{handleIntersectionChange(targetIntersection.name);},2000);return()=>clearTimeout(timer);}}},[intersections,selectedIntersection]);// 新增：在场景初始化后渲染所有路口entrance的设备图标\nconst renderEntranceDeviceIcons=converterInstance=>{if(!scene||typeof scene.traverse!=='function'||!converterInstance){console.warn('设备图标渲染条件不满足:',{scene:!!scene,sceneTraverse:scene&&typeof scene.traverse==='function',converter:!!converterInstance});return;}if(!devicesData.devices||devicesData.devices.length===0){console.warn('设备数据未加载，跳过设备图标渲染');return;// 设备数据未加载时不渲染\n}if(!intersections||intersections.length===0){console.warn('路口数据未加载，跳过设备图标渲染');return;}console.log('开始渲染设备图标:',{设备总数:devicesData.devices.length,路口总数:intersections.length});try{// 清理之前的设备图标\nconst existingIcons=scene.children.filter(obj=>obj.userData&&obj.userData.isEntranceDeviceIcons);existingIcons.forEach(obj=>scene.remove(obj));console.log('清理了',existingIcons.length,'个旧的设备图标');let totalRenderedDevices=0;intersections.forEach(intersection=>{if(!intersection.entrances||!Array.isArray(intersection.entrances))return;intersection.entrances.forEach(entrance=>{if(!entrance.longitude||!entrance.latitude||isNaN(parseFloat(entrance.longitude))||isNaN(parseFloat(entrance.latitude))){return;}const devices=devicesData.devices.filter(d=>d.location===intersection.name&&d.entrance===entrance.name);if(devices.length===0)return;// 经纬度转模型坐标\nconsole.log('渲染路口',intersection.name,'入口',entrance.name,'设备数量',devices.length);const modelPos=converterInstance.wgs84ToModel(parseFloat(entrance.longitude),parseFloat(entrance.latitude));// 创建一个组用于存放所有图标\nconst group=new THREE.Group();group.position.set(modelPos.x,10,-modelPos.y);// 上方10米\ngroup.userData={isEntranceDeviceIcons:true};// 图标排成一排，居中\nconst iconSize=32;// px\nconst iconSpacing=8;// px\nconst totalWidth=devices.length*iconSize+(devices.length-1)*iconSpacing;// 以三维单位为准，假设1单位=1米，24px约等于0.6米\nconst size3D=4.0;// 图标尺寸加大\nconst spacing3D=0.5;// 图标间距加大\nconst startX=-((devices.length-1)*(size3D+spacing3D))/2;devices.forEach((device,idx)=>{// 创建一个平面用于显示SVG图标\nconst textureLoader=new THREE.TextureLoader();const iconPath=`${BASE_URL}/images/${device.type}.svg`;// 图标材质\nconst iconMaterial=new THREE.MeshBasicMaterial({map:textureLoader.load(iconPath),transparent:true,opacity:1// 图标完全不透明\n});// 图标背景板材质\nconst bgMaterial=new THREE.MeshBasicMaterial({color:0x000000,transparent:true,opacity:0.7// 半透明黑色\n});// 背景板尺寸略大于图标\nconst bgWidth=size3D*1.25;const bgHeight=size3D*1.25;// 背景板几何体（圆角矩形）\n// 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\nconst bgGeometry=new THREE.PlaneGeometry(bgWidth,bgHeight);const bgMesh=new THREE.Mesh(bgGeometry,bgMaterial);// 图标几何体\nconst iconGeometry=new THREE.PlaneGeometry(size3D,size3D);const iconMesh=new THREE.Mesh(iconGeometry,iconMaterial);// 图标略微前移，避免Z轴重叠闪烁\niconMesh.position.set(0,0,0.01);// 创建一个组，包含背景和图标\nconst iconGroup=new THREE.Group();iconGroup.add(bgMesh);iconGroup.add(iconMesh);// 整体平移到正确位置\niconGroup.position.set(startX+idx*(size3D+spacing3D),0,0);// 不再固定旋转角度，后续在动画循环中让其始终面向相机\niconGroup.renderOrder=999;// 提高渲染优先级\niconGroup.userData={deviceId:device.id,deviceType:device.type,entrance:entrance.name,isEntranceDeviceIcon:true};group.add(iconGroup);});// 新增：添加白色半透明光柱，指向设备图标组\n// 光柱高度等于图标组的y坐标（即10），底部在地面y=0，顶部在图标组中心\nconst pillarHeight=group.position.y;// 10\nconst pillarGeometry=new THREE.CylinderGeometry(0.2,0.2,pillarHeight,16);const pillarMaterial=new THREE.MeshBasicMaterial({color:0xffffff,transparent:true,opacity:0.7});const pillar=new THREE.Mesh(pillarGeometry,pillarMaterial);// 设置光柱中心在y=0~y=10之间，底部正好在地面\npillar.position.set(0,-pillarHeight/2,0);// pillar.position.set(0, -pillarHeight, 0);\n// 可选：添加标记，便于后续查找或清理\npillar.userData={isEntranceDevicePillar:true};group.add(pillar);scene.add(group);});});}catch(e){console.error('renderEntranceDeviceIcons error',e);return;}};// 在场景初始化后调用渲染设备图标\nuseEffect(()=>{if(scene&&typeof scene.traverse==='function'&&converter.current&&devicesData.devices){var _devicesData$devices;console.log('触发设备图标渲染:',{scene:!!scene,converter:!!converter.current,devicesCount:((_devicesData$devices=devicesData.devices)===null||_devicesData$devices===void 0?void 0:_devicesData$devices.length)||0,intersectionsCount:(intersections===null||intersections===void 0?void 0:intersections.length)||0});// 延迟渲染，确保所有资源都已准备好\nconst timer=setTimeout(()=>{renderEntranceDeviceIcons(converter.current);},500);// 延迟500ms\nreturn()=>clearTimeout(timer);}},[scene,converter.current,devicesData.devices,intersections]);// 添加intersections依赖\n// 添加额外的重试机制，确保设备图标能够正确渲染\nuseEffect(()=>{if(scene&&converter.current&&devicesData.devices&&intersections){// 延迟3秒后检查是否有设备图标，如果没有则重新渲染\nconst retryTimer=setTimeout(()=>{const existingIcons=scene.children.filter(obj=>obj.userData&&obj.userData.isEntranceDeviceIcons);if(existingIcons.length===0){console.log('检测到设备图标缺失，执行重试渲染');renderEntranceDeviceIcons(converter.current);}else{console.log('设备图标渲染正常，共',existingIcons.length,'个图标组');}},3000);// 3秒后检查\nreturn()=>clearTimeout(retryTimer);}},[scene,converter.current,devicesData.devices,intersections]);// 新增：每帧让所有设备图标组和事件图标组始终面向相机（billboard效果）\nuseEffect(()=>{if(!scene||!cameraRef.current)return;const animateBillboard=()=>{// 遍历所有图标组，让其正对相机\nscene.children.forEach(obj=>{// 设备图标组\nif(obj.userData&&obj.userData.isEntranceDeviceIcons){obj.lookAt(cameraRef.current.position.x,obj.position.y,cameraRef.current.position.z);}// 事件图标组\nif(obj.userData&&obj.userData.type==='eventMarker'){obj.lookAt(cameraRef.current.position.x,obj.position.y,cameraRef.current.position.z);}});requestAnimationFrame(animateBillboard);};animateBillboard();},[scene]);// 修改点击处理函数\nconst handleMouseClick=(event,container,sceneInstance,cameraInstance)=>{if(!container||!sceneInstance||!cameraInstance)return;const rect=container.getBoundingClientRect();const mouseX=(event.clientX-rect.left)/container.clientWidth*2-1;const mouseY=-((event.clientY-rect.top)/container.clientHeight)*2+1;const raycaster=new THREE.Raycaster();raycaster.params.Points.threshold=1;raycaster.params.Line.threshold=1;const mouseVector=new THREE.Vector2(mouseX,mouseY);raycaster.setFromCamera(mouseVector,cameraInstance);const intersects=raycaster.intersectObjects(sceneInstance.children,true);if(intersects.length>0){for(let i=0;i<intersects.length;i++){const obj=intersects[i].object;if(obj.parent&&obj.parent.userData&&obj.parent.userData.isEntranceDeviceIcon){const deviceId=obj.parent.userData.deviceId;console.log('devicesDatahandleMouseClick',devicesData);const device=devicesData.devices.find(d=>d.id===deviceId);if(device){const x=event.clientX;const y=event.clientY;setDevicePopover({visible:true,deviceId,position:{x,y},content:renderDevicePopoverContent(device)});return;// 命中设备图标后直接返回\n}}}}// ...原有红绿灯弹框逻辑保持不变...\n// ... existing code ...\n};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{style:labelStyle,children:\"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{style:intersectionSelectStyle,placeholder:\"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",onChange:handleIntersectionChange,onSelect:handleIntersectionChange// 添加onSelect事件，确保每次选择都触发\n,options:intersections.map(intersection=>({value:intersection.name,label:intersection.name})),size:\"large\",bordered:true,dropdownStyle:{zIndex:1002,maxHeight:'300px'},value:selectedIntersection?selectedIntersection.name:undefined}),/*#__PURE__*/_jsx(\"div\",{ref:containerRef,style:{width:'100%',height:'100%'}}),trafficLightPopover.visible&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',left:`${trafficLightPopover.position.x}px`,top:`${trafficLightPopover.position.y}px`,transform:'translate(-50%, -100%)',zIndex:1003,backgroundColor:'rgba(0, 0, 0, 0.85)',color:'white',borderRadius:'4px',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.3)',padding:'0',maxWidth:'240px',// 缩小最大宽度\nfontSize:'12px'// 缩小字体\n},children:[trafficLightPopover.content,/*#__PURE__*/_jsx(\"button\",{style:{position:'absolute',top:'0px',right:'0px',background:'none',border:'none',color:'white',fontSize:'12px',cursor:'pointer',padding:'2px 6px'},onClick:()=>handleClosePopover(setTrafficLightPopover),children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:buttonContainerStyle,children:[/*#__PURE__*/_jsx(\"button\",{style:{...buttonStyle,backgroundColor:viewMode==='follow'?'#1890ff':'rgba(255, 255, 255, 0.9)',color:viewMode==='follow'?'white':'black'},onClick:switchToFollowView,children:\"\\u8DDF\\u968F\\u89C6\\u89D2\"}),/*#__PURE__*/_jsx(\"button\",{style:{...buttonStyle,backgroundColor:viewMode==='global'?'#1890ff':'rgba(255, 255, 255, 0.9)',color:viewMode==='global'?'white':'black'},onClick:switchToGlobalView,children:\"\\u5168\\u5C40\\u89C6\\u89D2\"})]}),devicePopover.visible&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',left:`${devicePopover.position.x}px`,top:`${devicePopover.position.y}px`,transform:'translate(-50%, -100%)',zIndex:1100,backgroundColor:'rgba(0, 0, 0, 0.92)',color:'white',borderRadius:'6px',boxShadow:'0 2px 12px rgba(0,0,0,0.35)',padding:0,minWidth:320,maxWidth:350,fontSize:13},children:[devicePopover.content,/*#__PURE__*/_jsx(\"button\",{style:{position:'absolute',top:'0px',right:'0px',background:'none',border:'none',color:'white',fontSize:'16px',cursor:'pointer',padding:'2px 10px',zIndex:1200},onClick:handleCloseDevicePopover,children:\"\\xD7\"})]})]});};// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text){let parameters=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const params={fontFace:parameters.fontFace||'Arial',fontSize:parameters.fontSize||12,// 从24px调小到16px\nfontWeight:parameters.fontWeight||'bold',borderThickness:parameters.borderThickness||4,borderColor:parameters.borderColor||{r:0,g:0,b:0,a:1.0},backgroundColor:parameters.backgroundColor||{r:255,g:255,b:255,a:0.8},textColor:parameters.textColor||{r:0,g:0,b:0,a:1.0},padding:parameters.padding||5};// 创建画布\nconst canvas=document.createElement('canvas');const context=canvas.getContext('2d');// 设置字体\n// context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n// 测量文本宽度\nconst textWidth=context.measureText(text).width;// 设置画布尺寸，考虑边框和填充\nconst width=textWidth+4*params.padding+4*params.borderThickness;const height=params.fontSize+2*params.padding+2*params.borderThickness;canvas.width=width;canvas.height=height;// 重新设置字体，因为改变画布尺寸会重置上下文\ncontext.font=`${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;context.textBaseline='middle';// // 只有在有边框或背景时才绘制背景和边框\n// if (params.borderThickness > 0 || params.backgroundColor.a > 0) {\n//   // 绘制背景和边框（圆角矩形）\n//   const radius = 8;\n//   context.beginPath();\n//   context.moveTo(params.borderThickness + radius, params.borderThickness);\n//   context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n//   context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n//   context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n//   context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n//   context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n//   context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n//   context.lineTo(params.borderThickness, params.borderThickness + radius);\n//   context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n//   context.closePath();\n//   // 设置背景填充（如果有背景）\n//   if (params.backgroundColor.a > 0) {\n//     context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n//     context.fill();\n//   }\n//   // 设置边框颜色（如果有边框）\n//   if (params.borderThickness > 0) {\n//     context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n//     context.lineWidth = params.borderThickness;\n//     context.stroke();\n//   }\n// }\n// 设置文字颜色\ncontext.fillStyle=`rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;context.textAlign='center';// 绘制文本\ncontext.fillText(text,width/2,height/2);// 创建纹理\nconst texture=new THREE.CanvasTexture(canvas);texture.minFilter=THREE.LinearFilter;texture.needsUpdate=true;// 创建精灵材质\nconst spriteMaterial=new THREE.SpriteMaterial({map:texture,transparent:true});// 创建精灵\nconst sprite=new THREE.Sprite(spriteMaterial);// sprite.scale.set(10, 5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\nsprite.scale.set(7,3.5,1);// 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\nsprite.material.depthTest=false;// 确保始终可见\n// 保存文本信息到userData，便于后续更新\nsprite.userData={text:text,params:params};return sprite;}// 在文件底部添加这个全局函数\nwindow.forceGlobalView=()=>{try{// 获取当前场景中的相机\nconst camera=document.querySelector('canvas').parentElement.__THREE_camera;if(camera){// 保存旧位置\nconst oldPos=camera.position.clone();// 设置新位置\ncamera.position.set(0,300,0);camera.up.set(0,1,0);camera.lookAt(0,0,0);// 更新矩阵\ncamera.updateMatrix();camera.updateMatrixWorld(true);// 更新控制器\nconst controls=document.querySelector('canvas').parentElement.__THREE_controls;if(controls){controls.target.set(0,0,0);controls.update();}console.log('强制设置全局视角成功',{旧位置:oldPos.toArray(),新位置:camera.position.toArray()});return true;}return false;}catch(e){console.error('强制设置全局视角失败',e);return false;}};// 修改车辆模型预加载函数\nconst preloadModels=async()=>{try{console.log('开始预加载所有模型...');const loader=new GLTFLoader();// 并行加载所有模型\ntry{const[trafficLightGltf,vehicleGltf,cyclistGltf,peopleGltf]=await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);// 处理机动车模型\nconsole.log('加载车辆模型...');preloadedVehicleModel=vehicleGltf.scene;preloadedVehicleModel.traverse(child=>{if(child.isMesh){const newMaterial=new THREE.MeshStandardMaterial({color:0xff0000,// 0xff0000, //红色 0xffffff,白色\nmetalness:0.2,roughness:0.1,envMapIntensity:1.0});// 保留原始贴图\nif(child.material.map){newMaterial.map=child.material.map;}child.materia=newMaterial;}});console.log('加载非机动车模型...');// 处理非机动车模型\npreloadedCyclistModel=cyclistGltf.scene;// 设置非机动车模型的缩放\npreloadedCyclistModel.scale.set(2,2,2);// 保持原始材质\npreloadedCyclistModel.traverse(child=>{if(child.isMesh&&child.material){// 只调整材质属性，保持原始颜色\nchild.material.metalness=0.1;child.material.roughness=0.8;child.material.envMapIntensity=1.0;}});console.log('加载行人模型...');// 处理行人模型\npreloadedPeopleModel=peopleGltf.scene;// 设置行人模型的缩放\n// preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n// 保持原始材质\npreloadedPeopleModel.traverse(child=>{if(child.isMesh&&child.material){// 只调整材质属性，保持原始颜色\nchild.material.metalness=0.1;child.material.roughness=0.8;child.material.envMapIntensity=1.0;}if(child.isMesh){child.castShadow=true;}});// 保存行人动画数据\nconsole.log('找到行人动画sss:',peopleGltf.animations.length,'个');if(peopleGltf.animations&&peopleGltf.animations.length>0){console.log('找到行人动画:',peopleGltf.animations.length,'个');peopleBaseModel=peopleGltf;}else{console.warn('行人模型没有包含动画数据');}console.log('加载红绿灯模型...');// 处理红绿灯模型\npreloadedTrafficLightModel=trafficLightGltf.scene;console.log('红绿灯模型：',preloadedTrafficLightModel);// 设置红绿灯模型的缩放\npreloadedTrafficLightModel.scale.set(6,6,6);// 保持原始材质\npreloadedTrafficLightModel.traverse(child=>{if(child.isMesh&&child.material){// 设置材质属性\nchild.material.metalness=0.2;child.material.roughness=0.3;child.material.envMapIntensity=1.2;}});console.log('所有模型预加载成功');}catch(error){console.error('加载特定模型失败，尝试单独加载:',error);// 如果整个Promise.all失败，尝试单独加载关键模型\ntry{if(!preloadedVehicleModel){const vehicleGltf=await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);preloadedVehicleModel=vehicleGltf.scene;}// // 尝试单独加载红绿灯模型\n// if (!preloadedTrafficLightModel) {\n//   console.log('正在单独加载红绿灯模型...');\n//   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n//   preloadedTrafficLightModel = trafficLightGltf.scene;\n//   preloadedTrafficLightModel.scale.set(3, 3, 3);\n//   console.log('红绿灯模型加载成功');\n// }\n}catch(err){console.error('单独加载模型也失败:',err);}}}catch(error){console.error('模型预加载失败:',error);}};// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText=type=>{const types={'1':'信号灯保持','2':'绿灯延长','3':'红灯截断','4':'相位插入','5':'相位插入','6':'优先未处理'};return types[type]||'未知类型';};// 添加辅助函数：显示警告标记\n// 添加计算两点之间距离的函数\nconst calculateDistance=(x1,y1,x2,y2)=>{const c=Math.sqrt((x1-x2)*(x1-x2)+(y1-y2)*(y1-y2));return c;};// 获取事件类型的阈值配置\nconst getEventThresholds=eventType=>{switch(eventType){case'910':// 违停车辆\nreturn{timeThreshold:300000,distanceThreshold:10};// 5分钟, 20米\ncase'904':// 逆行车辆\nreturn{timeThreshold:10000,distanceThreshold:20};// 10秒, 20米\ncase'901':// 车辆超速\nreturn{timeThreshold:30000,distanceThreshold:20};// 30秒, 50米\ncase'401':// 道路抛洒物\ncase'404':// 道路障碍物\ncase'1002':// 道路施工\nreturn{timeThreshold:600000,distanceThreshold:10};// 10分钟, 30米\ncase'405':// 行人通过马路\nreturn{timeThreshold:10000,distanceThreshold:10};// 10秒, 10米\ndefault:return{timeThreshold:5000,distanceThreshold:5};// 5秒, 5米\n}};// 优化后的事件重复检查逻辑\nconst checkDuplicateEvent=(eventType,eventKey,currentTime,currentPos)=>{const{timeThreshold,distanceThreshold}=getEventThresholds(eventType);// 遍历事件列表缓存中的所有事件\nfor(let i=0;i<eventListCache.length;i++){const cachedEvent=eventListCache[i];// 检查事件类型是否相同\nif(cachedEvent.eventType!==eventType){continue;}// 计算时间差\nconst timeDiff=currentTime-cachedEvent.lastUpdateTime;// 检查时间差是否在阈值内\nif(timeDiff>timeThreshold){continue;}// 计算距离\nconst distance=calculateDistance(currentPos.x,currentPos.y,cachedEvent.position.x,cachedEvent.position.y);// 检查距离是否在阈值内\nif(distance<=distanceThreshold){// 找到匹配的事件，更新信息\ncachedEvent.eventKey=eventKey;cachedEvent.lastUpdateTime=currentTime;cachedEvent.position={...currentPos};cachedEvent.updateCount=(cachedEvent.updateCount||1)+1;console.log(`🔄 3D场景检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);return{isDuplicate:true,eventId:cachedEvent.eventId,matchedEvent:cachedEvent};}}// 没有找到匹配的事件，创建新事件\nconst newEventId=`3D_EVT_${eventIdCounter.toString().padStart(6,'0')}`;eventIdCounter++;const newEvent={eventId:newEventId,eventType:eventType,eventKey:eventKey,firstDetectedTime:currentTime,lastUpdateTime:currentTime,position:{...currentPos},updateCount:1};// 添加到事件列表缓存\neventListCache.push(newEvent);return{isDuplicate:false,eventId:newEventId,newEvent:newEvent};};// 删除2s内没有更新的事件\nconst removeInactiveEvents=()=>{const currentTime=Date.now();const inactiveThreshold=2000;// 2s\nconst initialCount=eventListCache.length;const removedEvents=[];eventListCache=eventListCache.filter(event=>{const timeSinceLastUpdate=currentTime-event.lastUpdateTime;if(timeSinceLastUpdate>inactiveThreshold){removedEvents.push({id:event.eventId,type:event.eventType,inactiveTime:(timeSinceLastUpdate/1000).toFixed(1)});// 从场景中移除对应的标记\nconst marker=eventMarkers.get(event.eventId);if(marker&&scene){scene.remove(marker);eventMarkers.delete(event.eventId);}return false;// 删除该事件\n}return true;// 保留该事件\n});const removedCount=initialCount-eventListCache.length;if(removedCount>0){console.log(`🗑️ 3D场景删除了 ${removedCount} 个1s内未更新的事件:`);removedEvents.forEach(event=>{console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);});console.log(`📊 3D场景当前缓存事件数: ${eventListCache.length}`);}};// 根据事件类型获取背景颜色\nconst getEventBackgroundColor=eventType=>{switch(eventType){case'910':// 违停车辆\nreturn 0xff4d4f;// 红色背景 - 严重违规\ncase'904':// 逆行车辆\nreturn 0xf5222d;// 深红色背景 - 危险行为\ncase'901':// 车辆超速\nreturn 0xfa8c16;// 橙色背景 - 警告\ncase'401':// 道路抛洒物\nreturn 0xfaad14;// 黄色背景 - 注意\ncase'404':// 道路障碍物\nreturn 0xff7a45;// 橙红色背景 - 阻碍\ncase'1002':// 道路施工\nreturn 0x1890ff;// 蓝色背景 - 信息\ncase'405':// 行人通过马路\nreturn 0x52c41a;// 绿色背景 - 正常\ncase'2':// 交叉路口碰撞预警\nreturn 0xff0000;// 纯红色背景 - 紧急\ncase'12':// 交通参与者碰撞预警\nreturn 0xeb2f96;// 粉红色背景 - 预警\ncase'13':// 绿波车速引导\nreturn 0x13c2c2;// 青色背景 - 引导\ncase'999':// 信号灯优先\nreturn 0x722ed1;// 紫色背景 - 优先\ndefault:return 0xffa500;// 默认橙黄色背景\n}};// 获取事件类型的中文名称\nconst getEventTypeName=eventType=>{switch(eventType){case'401':return'抛洒物';case'404':return'障碍物';case'405':return'行人';case'904':return'逆行';case'910':return'违停';case'1002':return'施工';case'901':return'超速';case'2':return'碰撞';case'12':return'碰撞';case'13':return'绿波';case'999':return'优先';default:return`事件${eventType}`;}};// 创建事件图标标记\nconst createEventMarker=(eventType,position,eventId)=>{if(!scene){console.warn('无法创建事件标记：场景不存在或已卸载');return null;}try{// 创建一个组来包含背景和图标\nconst markerGroup=new THREE.Group();// 1. 创建统一的背景平面（包含图标和文字区域，类似截图中的设计）\nconst backgroundGeometry=new THREE.PlaneGeometry(6,8);// 适当增加宽度和高度以容纳图标和文字\nconst backgroundColor=getEventBackgroundColor(eventType);// 根据事件类型获取背景色\nconst backgroundMaterial=new THREE.MeshBasicMaterial({color:backgroundColor,// 使用事件类型对应的背景颜色\ntransparent:true,opacity:0.9,// 提高不透明度，使颜色更加鲜明\nside:THREE.DoubleSide});const backgroundMesh=new THREE.Mesh(backgroundGeometry,backgroundMaterial);backgroundMesh.position.set(0,-1,-0.01);// 向下偏移以居中整个标记，稍微向后作为背景\nbackgroundMesh.renderOrder=998;// 背景渲染优先级\nmarkerGroup.add(backgroundMesh);// 2. 创建图标平面（位于背景上方区域）\nconst textureLoader=new THREE.TextureLoader();const iconPath=`${BASE_URL}/images/${eventType}.svg`;// 事件图标路径\nconst iconMaterial=new THREE.MeshBasicMaterial({map:textureLoader.load(iconPath,// 加载成功回调\ntexture=>{console.log(`事件图标加载成功: 事件${eventType}.svg`);},// 加载进度回调\nundefined,// 加载失败回调\nerror=>{console.warn(`事件图标加载失败: 事件${eventType}.svg，使用默认图标`);// 可以在这里设置默认图标\n}),transparent:true,opacity:1,// 完全不透明\nside:THREE.DoubleSide});// 创建图标几何体（参考截图中的比例）\nconst iconGeometry=new THREE.PlaneGeometry(5,5);// 宽度5，高度4，类似截图中的车辆图标比例\nconst iconMesh=new THREE.Mesh(iconGeometry,iconMaterial);iconMesh.position.set(0,0.3,0.01);// 图标位于背景上半部分\niconMesh.renderOrder=999;// 图标渲染优先级\nmarkerGroup.add(iconMesh);// 3. 创建文字标签（显示在图标正下方，参考截图布局）\nconst eventTypeName=getEventTypeName(eventType);const textLabel=createTextSprite(eventTypeName,{backgroundColor:{r:0,g:0,b:0,a:0.0},// 完全透明背景，与图标共用背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文字\nfontSize:14,// 增大字体以提高可读性\npadding:0,// 无填充\nborderThickness:0,// 无边框\nfontWeight:'bold'// 加粗字体，类似截图效果\n});textLabel.position.set(0,-3.5,0.02);// 位于图标正下方，紧贴背景下半部分\ntextLabel.renderOrder=1000;// 确保在最上层渲染\ntextLabel.scale.set(6,3,1);// 调整文字标签的缩放以适应背景\nmarkerGroup.add(textLabel);// 设置组的位置，高度为15米\nmarkerGroup.position.set(position.x,15,-position.y);// 让整个组始终面向相机\n// markerGroup.lookAt(0, 15, 0);\n// 创建时直接朝向相机，避免角度跳跃\n// if (cameraRef.current) {\n//   markerGroup.lookAt(cameraRef.current.position.x, markerGroup.position.y, cameraRef.current.position.z);\n// } else {\n//   markerGroup.lookAt(0, markerGroup.position.y, 0);\n// }\n// 设置渲染优先级，与设备图标保持一致\nmarkerGroup.renderOrder=999;// 添加用户数据到组\nmarkerGroup.userData={type:'eventMarker',eventId:eventId,eventType:eventType};// 添加到场景\nscene.add(markerGroup);// 存储标记引用\neventMarkers.set(eventId,markerGroup);console.log(`📍 创建事件标记 ${eventType} (${eventTypeName}) (ID: ${eventId})，位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)})，背景色: #${backgroundColor.toString(16)}`);return markerGroup;}catch(error){console.error('创建事件标记时出错:',error);return null;}};// 更新事件标记位置\nconst updateEventMarkerPosition=(eventId,newPosition)=>{const markerGroup=eventMarkers.get(eventId);if(markerGroup&&scene){markerGroup.position.set(newPosition.x,15,-newPosition.y);// 重新设置朝向相机\n// markerGroup.lookAt(0, 15, 0);\nconsole.log(`📍 更新事件标记位置 (ID: ${eventId})，新位置: (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)})`);}};const showWarningMarker=function(position,text,color){let eventType=arguments.length>3&&arguments[3]!==undefined?arguments[3]:'999';let eventData=arguments.length>4&&arguments[4]!==undefined?arguments[4]:{};// 添加安全检查 - 如果场景不存在，则直接返回\nif(!scene){console.warn('无法显示警告标记：场景不存在或已卸载');return;}try{// 生成事件Key用于去重\nconst currentTime=Date.now();const eventKey=`${eventData.rsuId||'UNKNOWN'}_${eventType}_${position.x.toFixed(0)}_${position.y.toFixed(0)}`;// 转换模型坐标为经纬度（用于距离计算）\n// const converter = new CoordinateConverter();\n// const wgs84Pos = converter.modelToWgs84(position.x, position.y);\n// 检查事件去重\nconst duplicateResult=checkDuplicateEvent(eventType,eventKey,currentTime,position);const isDuplicate=duplicateResult.isDuplicate;console.log(`🔍 3D场景事件去重检查 - 类型: ${eventType}, EventKey: ${eventKey}, 位置: (${position.x}, ${position.y}), 结果: ${isDuplicate?'重复':'新事件'}`);if(isDuplicate){// 重复事件，更新现有标记位置\nupdateEventMarkerPosition(duplicateResult.eventId,position);}else{// 新事件，创建新的标记\ncreateEventMarker(eventType,position,duplicateResult.eventId);}}catch(error){console.error('显示警告标记时出错:',error);}};// 添加创建红绿灯模型的函数\nconst createTrafficLights=(converterInstance,intersections)=>{if(!scene){console.error('无法创建红绿灯：场景未初始化');return;}if(!converterInstance){console.error('无法创建红绿灯：坐标转换器未初始化');return;}// 检查红绿灯模型是否已加载\nif(!preloadedTrafficLightModel){console.error('红绿灯模型未加载，尝试重新加载...');// 尝试重新加载红绿灯模型\nconst loader=new GLTFLoader();loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`).then(trafficLightGltf=>{preloadedTrafficLightModel=trafficLightGltf.scene;preloadedTrafficLightModel.scale.set(6,6,6);preloadedTrafficLightModel.traverse(child=>{if(child.isMesh&&child.material){child.material.metalness=0.2;child.material.roughness=0.3;child.material.envMapIntensity=1.2;}});console.log('红绿灯模型重新加载成功，开始创建红绿灯...');// 重新调用创建函数\ncreateTrafficLights(converterInstance,intersections);}).catch(error=>{console.error('红绿灯模型重新加载失败:',error);// 如果加载失败，使用简单的替代物体\ncreateFallbackTrafficLights(converterInstance,intersections);});return;}// 先清除现有的红绿灯\ntrafficLightsMap.forEach(lightObj=>{if(scene&&lightObj.model){scene.remove(lightObj.model);}});trafficLightsMap.clear();// 为每个路口创建红绿灯模型\nintersections.forEach(intersection=>{if(intersection.hasTrafficLight===false){console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);return;}if(intersection.latitude&&intersection.longitude&&intersection.interId){const modelPos=converterInstance.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);try{// 确保模型存在且可以克隆\nif(!preloadedTrafficLightModel||!preloadedTrafficLightModel.clone){throw new Error('红绿灯模型无效或无法克隆');}// 创建红绿灯模型\nconst trafficLightModel=preloadedTrafficLightModel.clone();// 给模型一个名称便于调试\ntrafficLightModel.name=`交通灯-${intersection.name}`;// 设置位置，离地面高度为15米，提高可见性\ntrafficLightModel.position.set(modelPos.x,15,-modelPos.y);// 放大红绿灯模型尺寸，使其更容易被点击\ntrafficLightModel.scale.set(10,10,10);// 确保渲染顺序高，避免被其他对象遮挡\ntrafficLightModel.renderOrder=100;// 设置材质属性\ntrafficLightModel.traverse(child=>{if(child.isMesh){child.material.transparent=false;child.material.opacity=1.0;child.material.side=THREE.DoubleSide;child.material.depthWrite=true;child.material.depthTest=true;child.material.needsUpdate=true;child.renderOrder=100;}});// 添加交互所需的信息\ntrafficLightModel.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};// 将红绿灯添加到场景中\n// scene.add(trafficLightModel);\n// ========== 新增：在红绿灯地面添加指北针图标 =============\n// 创建一个平面用于显示指北针SVG图标\nconst compassTextureLoader=new THREE.TextureLoader();const compassIconPath=`${BASE_URL}/images/compass.svg`;// public目录下的路径\nconst compassMaterial=new THREE.MeshBasicMaterial({map:compassTextureLoader.load(compassIconPath),transparent:true,opacity:1});// 平面几何体，5x5米\nconst compassGeometry=new THREE.PlaneGeometry(5,5);const compassMesh=new THREE.Mesh(compassGeometry,compassMaterial);// 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\ncompassMesh.position.set(modelPos.x+20,1.5,-(modelPos.y+20));// 指北针朝上，旋转x轴-90度\ncompassMesh.rotation.x=-Math.PI/2;// 渲染优先级高，避免被地面遮挡\ncompassMesh.renderOrder=101;// 可选：添加userData标记\ncompassMesh.userData={type:'compassIcon',interId:intersection.interId,name:intersection.name};// 添加到场景\nscene.add(compassMesh);// ========== 指北针图标添加结束 =============\n// 存储红绿灯引用\ntrafficLightsMap.set(intersection.interId,{model:trafficLightModel,intersection:intersection,position:modelPos});console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);}catch(error){console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`,error);// 如果克隆失败，创建一个简单的替代物体\n// createSimpleTrafficLight(intersection, modelPos, converterInstance);\n}}});// 在控制台输出所有红绿灯的信息\nconsole.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);trafficLightsMap.forEach((lightObj,interId)=>{console.log(`- ID ${interId}: ${lightObj.intersection.name}`);});};// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights=(converterInstance,intersections)=>{intersections.forEach(intersection=>{// 跳过没有红绿灯的路口\nif(intersection.hasTrafficLight===false){return;}if(intersection.latitude&&intersection.longitude&&intersection.interId){// 转换经纬度到模型坐标\nconst modelPos=converterInstance.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));createSimpleTrafficLight(intersection,modelPos,converterInstance);}});};// 创建简单的替代红绿灯\nconst createSimpleTrafficLight=(intersection,modelPos,converterInstance)=>{// 创建一个简单的几何体作为红绿灯 - 增大尺寸\nconst geometry=new THREE.BoxGeometry(10,30,10);const material=new THREE.MeshBasicMaterial({color:0x333333,transparent:false,opacity:1.0});const trafficLightModel=new THREE.Mesh(geometry,material);// 给模型一个名称便于调试\ntrafficLightModel.name=`简易交通灯-${intersection.name}`;// 设置位置 - 提高高度以增加可见性\ntrafficLightModel.position.set(modelPos.x,15,-modelPos.y);// 设置渲染顺序\ntrafficLightModel.renderOrder=100;// 添加交互所需的信息\ntrafficLightModel.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};// 添加一个专门用于点击的大型碰撞体\nconst colliderGeometry=new THREE.SphereGeometry(3,12,12);const colliderMaterial=new THREE.MeshBasicMaterial({color:0xff00ff,transparent:true,opacity:0.0,// 完全透明\ndepthWrite:false});const collider=new THREE.Mesh(colliderGeometry,colliderMaterial);collider.name=`简易交通灯碰撞体-${intersection.name}`;collider.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name,isCollider:true};trafficLightModel.add(collider);// 将红绿灯添加到场景中\nscene.add(trafficLightModel);// 存储红绿灯引用\ntrafficLightsMap.set(intersection.interId,{model:trafficLightModel,intersection:intersection,position:modelPos});// 添加一个顶部灯光标识，使其更容易被看到\nconst lightGeometry=new THREE.SphereGeometry(5,16,16);const lightMaterial=new THREE.MeshBasicMaterial({color:0xFF0000});const lightMesh=new THREE.Mesh(lightGeometry,lightMaterial);lightMesh.position.set(0,15,0);// 给灯光相同的userData\nlightMesh.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};trafficLightModel.add(lightMesh);console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);};// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection=phaseId=>{switch(phaseId){case'1':return'北进口左转';case'2':return'北进口直行';case'3':return'北进口右转';case'5':return'东进口左转';case'6':return'东进口直行';case'7':return'东进口右转';case'9':return'南进口左转';case'10':return'南进口直行';case'11':return'南进口右转';case'13':return'西进口左转';case'14':return'西进口直行';case'15':return'西进口右转';default:return`相位${phaseId}`;}};// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode=dirCode=>{switch(dirCode){case'N':return'北向南';case'S':return'南向北';case'E':return'东向西';case'W':return'西向东';case'NE':return'东北向西南';case'NW':return'西北向东南';case'SE':return'东南向西北';case'SW':return'西南向东北';default:return`方向${dirCode}`;}};// 关闭弹出窗口的处理函数\nconst handleClosePopover=setPopoverState=>{// 清理定时器\nif(window.trafficLightUpdateTimerRef&&window.trafficLightUpdateTimerRef.current){clearInterval(window.trafficLightUpdateTimerRef.current);window.trafficLightUpdateTimerRef.current=null;console.log('已清理红绿灯状态更新定时器');}// 清理当前弹窗ID\nif(window.currentPopoverIdRef){window.currentPopoverIdRef.current=null;}// 更新弹窗状态为不可见\nsetPopoverState(prev=>({...prev,visible:false,content:null,// 清空内容\nphases:[]// 清空相位信息\n}));console.log('弹窗已关闭，所有相关资源已清理');};// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick=interId=>{try{var _document$querySelect,_document$querySelect2;// 检查是否有该ID的红绿灯\nconst trafficLight=trafficLightsMap.get(interId||'1');if(!trafficLight){console.error('未找到指定ID的红绿灯:',interId);// 输出所有可用ID\nconsole.log('可用的红绿灯ID:');trafficLightsMap.forEach((light,id)=>{console.log(`- ${id}: ${light.intersection.name}`);});return false;}// 获取红绿灯模型\nconst lightModel=trafficLight.model;// 模拟点击事件\nconst stateInfo=trafficLightStates.get(interId);const intersection=trafficLight.intersection;// 创建弹出窗口内容\nlet content;if(stateInfo&&stateInfo.phases){content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'220px',maxHeight:'300px',overflowY:'auto'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'6px',fontSize:'14px',borderBottom:'1px solid #eee',paddingBottom:'4px'},children:[intersection.name,\" (ID: \",interId,\")\"]}),/*#__PURE__*/_jsx(\"div\",{children:stateInfo.phases.map((phase,index)=>{let lightColor;switch(phase.trafficLight){case'G':lightColor='#00ff00';break;case'Y':lightColor='#ffff00';break;case'R':default:lightColor='#ff0000';break;}return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'6px',backgroundColor:'rgba(255,255,255,0.1)',padding:'4px',borderRadius:'4px',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold'},children:getPhaseDirection(phase.phaseId)}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u706F\\u8272: \"}),/*#__PURE__*/_jsx(\"span\",{style:{color:lightColor,fontWeight:'bold',backgroundColor:'rgba(0,0,0,0.3)',padding:'0 3px',borderRadius:'2px'},children:phase.trafficLight==='R'?'红灯':phase.trafficLight==='Y'?'黄灯':'绿灯'})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u5012\\u8BA1\\u65F6: \"}),/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'bold'},children:[phase.remainTime,\" \\u79D2\"]})]})]},index);})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'6px',fontSize:'10px',color:'#888'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}else{content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',maxWidth:'200px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:'8px'},children:intersection.name}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u8DEF\\u53E3ID: \",interId]}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"})]});}// 获取弹窗位置 - 使用中心位置\nconst centerX=window.innerWidth/2-500;const centerY=window.innerHeight/2-500;// 获取全局的setTrafficLightPopover函数\nconst setPopoverState=(_document$querySelect=document.querySelector('#root'))===null||_document$querySelect===void 0?void 0:(_document$querySelect2=_document$querySelect.__REACT_INSTANCE)===null||_document$querySelect2===void 0?void 0:_document$querySelect2.setTrafficLightPopover;if(setPopoverState){// 直接调用React组件的状态更新函数\nsetPopoverState({visible:true,interId:interId,position:{x:centerX,y:centerY},content:content,phases:(stateInfo===null||stateInfo===void 0?void 0:stateInfo.phases)||[]});console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);return true;}else{// 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\nconst popover=document.createElement('div');popover.style.position='absolute';popover.style.left=`${centerX}px`;popover.style.top=`${centerY}px`;popover.style.transform='translate(-50%, -100%)';popover.style.zIndex='9999';popover.style.backgroundColor='rgba(0, 0, 0, 0.85)';popover.style.color='white';popover.style.borderRadius='4px';popover.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.3)';popover.style.padding='8px';popover.style.maxWidth='240px';popover.style.fontSize='12px';popover.innerHTML=`\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo?'红绿灯状态已加载':'当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;document.body.appendChild(popover);// 添加关闭按钮点击事件\nconst closeButton=popover.querySelector('button');if(closeButton){closeButton.addEventListener('click',()=>{document.body.removeChild(popover);});}console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);return true;}}catch(error){console.error('测试红绿灯点击失败:',error);return false;}};// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights=()=>{console.log('红绿灯列表:');if(!trafficLightsMap||trafficLightsMap.size===0){console.log('当前没有红绿灯对象');return[];}const list=[];trafficLightsMap.forEach((light,id)=>{console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);list.push({id,name:light.intersection.name,position:light.position});});return list;};// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup=interId=>{try{// 确保interId为字符串类型\ninterId=String(interId);console.log('调用showTrafficLightPopup函数, 参数ID:',interId,'类型:',typeof interId);console.log('当前trafficLightsMap大小:',trafficLightsMap.size);// 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\nlet trafficLight=trafficLightsMap.get(interId);if(!trafficLight){// 尝试转换为数字查找\nconst numericId=parseInt(interId);trafficLight=trafficLightsMap.get(numericId);if(trafficLight){console.log(`使用数字ID ${numericId} 找到了红绿灯`);interId=numericId;// 更新interId为找到的正确类型\n}}if(!trafficLight){console.error('未找到指定ID的红绿灯:',interId);return false;}const stateInfo=trafficLightStates.get(interId);const intersection=trafficLight.intersection;// 判断是否有相位数据\nconst hasPhaseData=stateInfo&&stateInfo.phases&&stateInfo.phases.length>0;let content;// 指北针样式\nconst compassStyle={position:'absolute',top:'5px',right:'25px',width:'30px',height:'30px',display:'flex',justifyContent:'center',alignItems:'center',borderRadius:'50%',background:'rgba(0,0,0,0.1)',zIndex:10};// 指北针组件\nconst CompassIcon=()=>/*#__PURE__*/_jsx(\"div\",{style:compassStyle,children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',alignItems:'center',transform:'rotate(0deg)'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#ff5252',fontSize:'14px',fontWeight:'bold',lineHeight:'14px'},children:\"N\"}),/*#__PURE__*/_jsx(\"span\",{style:{width:0,height:0,borderLeft:'6px solid transparent',borderRight:'6px solid transparent',borderBottom:'10px solid #ff5252',marginTop:'-2px'}})]})});if(hasPhaseData){// 相位ID与方向/方式的映射\nconst phaseMap={'1':{dir:'N',type:'left'},'2':{dir:'N',type:'straight'},'3':{dir:'N',type:'right'},'5':{dir:'E',type:'left'},'6':{dir:'E',type:'straight'},'7':{dir:'E',type:'right'},'9':{dir:'S',type:'left'},'10':{dir:'S',type:'straight'},'11':{dir:'S',type:'right'},'13':{dir:'W',type:'left'},'14':{dir:'W',type:'straight'},'15':{dir:'W',type:'right'}};const typeOrder=['left','straight','right'];const colorMap={G:'#00ff00',Y:'#ffff00',R:'#ff0000'};const dirData={N:{},E:{},S:{},W:{}};stateInfo.phases.forEach(phase=>{const map=phaseMap[phase.phaseId];if(map){dirData[map.dir][map.type]={color:colorMap[phase.trafficLight]||'#888',remainTime:phase.remainTime};}});content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'260px',background:'rgba(0,0,0,0.05)',position:'relative'},children:[/*#__PURE__*/_jsx(CompassIcon,{}),/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'8px',fontSize:'15px',textAlign:'center'},children:[intersection.name,\"\\u706F\\u6001\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateRows:'60px 60px 60px',gridTemplateColumns:'60px 60px 60px',justifyContent:'center',alignItems:'center',background:'rgba(255,255,255,0.05)',borderRadius:'8px',margin:'0 auto',position:'relative'},children:[/*#__PURE__*/_jsx(\"div\",{style:{gridRow:1,gridColumn:2,textAlign:'center',display:'flex',justifyContent:'center',width:'100%'},children:typeOrder.map((type,index)=>{// 反转左转和右转在数组中的顺序\nlet displayIndex=index;if(index===0)displayIndex=2;else if(index===2)displayIndex=0;const currentType=typeOrder[displayIndex];// 计算与南边对齐的样式\nconst marginStyle={};if(currentType==='left'){// 左转箭头 (右侧显示)\nmarginStyle.marginRight='0px';}else if(currentType==='straight'){// 直行箭头 (中间显示)\nmarginStyle.marginLeft='10px';marginStyle.marginRight='10px';}else if(currentType==='right'){// 右转箭头 (左侧显示)\nmarginStyle.marginLeft='0px';}return dirData.N[currentType]&&/*#__PURE__*/// <div key={currentType} style={{\n//   display: 'flex',\n//   flexDirection: 'column',\n//   alignItems: 'center',\n//   ...marginStyle\n// }}>\n_jsxs(\"div\",{style:{marginRight:currentType==='left'?0:'10px',display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.N[currentType].color,fontWeight:'bold',marginBottom:'3px'},children:dirData.N[currentType].remainTime}),/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.N[currentType].color,fontSize:'20px',lineHeight:'20px'},children:currentType==='left'?'\\u{1F87A}':currentType==='straight'?'\\u{1F87B}':'\\u{1F878}'})]},currentType);})}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:3,gridColumn:2,textAlign:'center',display:'flex',justifyContent:'center',width:'100%'},children:typeOrder.map(type=>dirData.S[type]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginRight:type==='right'?0:'10px',display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.S[type].color,fontSize:'20px',lineHeight:'20px'},children:type==='left'?'\\u{1F878}':type==='straight'?'\\u{1F879}':'\\u{1F87A}'}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.S[type].color,fontWeight:'bold',marginTop:'3px'},children:dirData.S[type].remainTime})]},type))}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:2,gridColumn:3,textAlign:'center'},children:typeOrder.map((type,index)=>{// 反转左转和右转在数组中的顺序\nlet displayIndex=index;if(index===0)displayIndex=2;else if(index===2)displayIndex=0;const currentType=typeOrder[displayIndex];return dirData.E[currentType]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'8px',display:'flex',alignItems:'center',justifyContent:'flex-start'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.E[currentType].color,fontSize:'20px',lineHeight:'20px'},children:currentType==='left'?'\\u{1F87B}':currentType==='straight'?'\\u{1F878}':'\\u{1F879}'}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.E[currentType].color,fontWeight:'bold',marginLeft:'5px'},children:dirData.E[currentType].remainTime})]},currentType);})}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:2,gridColumn:1,textAlign:'center'},children:typeOrder.map(type=>dirData.W[type]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'8px',display:'flex',alignItems:'center',justifyContent:'flex-end'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.W[type].color,fontWeight:'bold',marginRight:'5px'},children:dirData.W[type].remainTime}),/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.W[type].color,fontSize:'20px',lineHeight:'20px'},children:type==='left'?'\\u{1F879}':type==='straight'?'\\u{1F87A}':'\\u{1F87B}'})]},type))})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'8px',fontSize:'11px',color:'#888',textAlign:'center'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}else{// 没有相位数据时显示的内容\ncontent=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'15px',width:'260px',background:'rgba(0,0,0,0.05)',position:'relative'},children:[/*#__PURE__*/_jsx(CompassIcon,{}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:'10px',fontSize:'15px',textAlign:'center'},children:intersection.name}),/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'20px 0',color:'#ff9800',fontSize:'14px',fontWeight:'bold',background:'rgba(255,255,255,0.1)',borderRadius:'8px',marginBottom:'10px'},children:\"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#888',textAlign:'center'},children:[\"\\u8DEF\\u53E3ID: \",interId]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'8px',fontSize:'11px',color:'#888',textAlign:'center'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}// 设置弹窗位置在左上角区域\nconst x=445;const y=250;// 更新当前显示的红绿灯ID引用\nif(window.currentPopoverIdRef){window.currentPopoverIdRef.current=interId;}// 取消之前的更新定时器（如果存在）\nif(window.trafficLightUpdateTimerRef&&window.trafficLightUpdateTimerRef.current){clearInterval(window.trafficLightUpdateTimerRef.current);window.trafficLightUpdateTimerRef.current=null;}// 更新弹窗状态\nif(window._setTrafficLightPopover){window._setTrafficLightPopover({visible:true,interId:interId,position:{x,y},content:content,phases:(stateInfo===null||stateInfo===void 0?void 0:stateInfo.phases)||[]});// 设置定时更新\nif(window.trafficLightUpdateTimerRef){window.trafficLightUpdateTimerRef.current=setInterval(()=>{window.showTrafficLightPopup(interId);},1000);}return true;}else{console.error('无法找到setTrafficLightPopover函数');return false;}}catch(error){console.error('显示红绿灯弹窗失败:',error);return false;}};// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject=object=>{let current=object;// 如果对象本身就有红绿灯数据，直接返回\nif(current&&current.userData&&current.userData.type==='trafficLight'){console.log('直接找到红绿灯对象:',current.name||'无名称');return current;}// 向上查找父对象，直到找到红绿灯或到达顶层\nwhile(current&&current.parent){current=current.parent;if(current.userData&&current.userData.type==='trafficLight'){console.log('从父对象找到红绿灯:',current.name||'无名称');return current;}}return null;};// 添加调试工具：强制进行点击测试\nwindow.testClickDetection=(x,y)=>{try{console.log('执行强制点击测试 @ 位置:',x,y);// 找到渲染器的DOM元素\nconst canvas=document.querySelector('canvas');if(!canvas){console.error('找不到THREE.js的canvas元素');return false;}// 确保scene和camera已定义\nif(!scene||!cameraRef.current){console.error('scene或camera未定义');return false;}// 如果没有传入坐标，使用屏幕中心点\nif(x===undefined||y===undefined){x=window.innerWidth/2;y=window.innerHeight/2;}// 计算归一化设备坐标 (-1 到 +1)\nconst rect=canvas.getBoundingClientRect();const mouseX=(x-rect.left)/canvas.clientWidth*2-1;const mouseY=-((y-rect.top)/canvas.clientHeight)*2+1;console.log('归一化坐标:',mouseX,mouseY);// 创建一个射线\nconst raycaster=new THREE.Raycaster();raycaster.params.Points.threshold=5;raycaster.params.Line.threshold=5;const mouseVector=new THREE.Vector2(mouseX,mouseY);raycaster.setFromCamera(mouseVector,cameraRef.current);// 收集所有红绿灯对象\nconst trafficLightObjects=[];trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){trafficLightObjects.push(lightObj.model);console.log(`添加红绿灯 ${interId} 到检测列表`);}});// 直接对红绿灯对象进行碰撞检测\nconsole.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);const tlIntersects=raycaster.intersectObjects(trafficLightObjects,true);if(tlIntersects.length>0){console.log('成功点击到红绿灯对象!');tlIntersects.forEach((intersect,i)=>{console.log(`结果 ${i}:`,intersect.object.name||'无名称','距离:',intersect.distance,'position:',intersect.object.position.toArray(),'userData:',intersect.object.userData);// 尝试获取红绿灯ID\nconst obj=getTrafficLightFromObject(intersect.object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){console.log('找到红绿灯ID:',obj.userData.interId);}});return true;}// 对整个场景进行碰撞检测\nconsole.log('对整个场景进行碰撞检测...');const sceneIntersects=raycaster.intersectObjects(scene.children,true);console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);sceneIntersects.forEach((intersect,i)=>{const obj=intersect.object;console.log(`场景物体 ${i}:`,obj.name||'无名称','类型:',obj.type,'位置:',obj.position.toArray(),'距离:',intersect.distance,'userData:',obj.userData);});// 测试红绿灯的可见性\nconsole.log('检查红绿灯的可见性...');let visibleCount=0;trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){var _lightObj$intersectio;// 检查红绿灯模型是否可见\nlet isVisible=lightObj.model.visible;let frustumVisible=true;// 获取世界位置\nconst worldPos=new THREE.Vector3();lightObj.model.getWorldPosition(worldPos);// 计算到摄像机的距离\nconst distanceToCamera=worldPos.distanceTo(cameraRef.current.position);// 检查是否在视锥体内\nconst screenPos=worldPos.clone().project(cameraRef.current);if(Math.abs(screenPos.x)>1||Math.abs(screenPos.y)>1||screenPos.z<-1||screenPos.z>1){frustumVisible=false;}if(isVisible){visibleCount++;}console.log(`红绿灯 ${interId}:`,{名称:((_lightObj$intersectio=lightObj.intersection)===null||_lightObj$intersectio===void 0?void 0:_lightObj$intersectio.name)||'未知',可见性:isVisible,在视锥体内:frustumVisible,世界位置:worldPos.toArray(),屏幕位置:[screenPos.x,screenPos.y,screenPos.z],与摄像机距离:distanceToCamera});}});console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);// 如果未检测到任何交叉点\nreturn sceneIntersects.length>0;}catch(error){console.error('点击测试失败:',error);return false;}};// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual=(trafficLight,phaseInfo)=>{var _trafficLight$interse,_trafficLight$interse2,_trafficLight$interse3;if(!trafficLight||!trafficLight.model||!phaseInfo){return;}// 移除旧的灯光模型(如果存在)\nconst lightsToRemove=[];trafficLight.model.traverse(child=>{if(child.userData&&child.userData.isLight){lightsToRemove.push(child);}});lightsToRemove.forEach(light=>{trafficLight.model.remove(light);});// 根据状态获取颜色\nlet lightColor;switch(phaseInfo.trafficLight){case'G':lightColor=0x00FF00;// 绿色\nbreak;case'Y':lightColor=0xFFFF00;// 黄色\nbreak;case'R':default:lightColor=0xFF0000;// 红色\nbreak;}// 创建一个球体作为灯光\nconst lightGeometry=new THREE.SphereGeometry(3,16,16);const lightMaterial=new THREE.MeshBasicMaterial({color:lightColor,emissive:lightColor,emissiveIntensity:1});const lightMesh=new THREE.Mesh(lightGeometry,lightMaterial);lightMesh.position.set(0,12,0);// 放在交通灯顶部\nlightMesh.userData={isLight:true,type:'trafficLight',interId:(_trafficLight$interse=trafficLight.intersection)===null||_trafficLight$interse===void 0?void 0:_trafficLight$interse.interId,phaseId:phaseInfo.phaseId,direction:phaseInfo.direction,remainTime:phaseInfo.remainTime};// 添加光源使灯光更明显\nconst light=new THREE.PointLight(lightColor,1,50);light.position.set(0,12,0);light.userData={isLight:true};// 将灯光添加到交通灯模型\ntrafficLight.model.add(lightMesh);trafficLight.model.add(light);console.log(`更新路口 ${((_trafficLight$interse2=trafficLight.intersection)===null||_trafficLight$interse2===void 0?void 0:_trafficLight$interse2.name)||((_trafficLight$interse3=trafficLight.intersection)===null||_trafficLight$interse3===void 0?void 0:_trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);};export default CampusModel;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "axios", "VideoPlayer", "DevicePopoverContent", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "mixersToCleanup", "Set", "bonesMap", "resourceManager", "mixers", "bones", "actions", "models", "addMixer", "mixer", "model", "add", "traverse", "object", "isBone", "uuid", "addAction", "action", "removeMixer", "for<PERSON>ach", "stop", "delete", "stopAllAction", "root", "getRoot", "animations", "length", "uncacheRoot", "uncacheAction", "uncacheClip", "clips", "_actions", "map", "a", "_clip", "filter", "Boolean", "clip", "e", "cleanup", "clear", "bone", "parent", "remove", "matrix", "identity", "matrixWorld", "<PERSON><PERSON><PERSON>", "geometry", "dispose", "material", "createAnimationMixer", "AnimationMixer", "createAction", "clipAction", "processedMessageIds", "eventListCache", "eventIdCounter", "eventMarkers", "CampusModel", "_ref", "className", "onCurrentRSUChange", "selectedRSUs", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "devicesData", "setDevicesData", "devices", "loadDevicesData", "devicesArray", "apiUrl", "success", "intersections", "setIntersections", "fetchIntersections", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "devicePopover", "setDevicePopover", "deviceId", "handleCloseDevicePopover", "renderDevicePopoverContent", "device", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "hasTrafficLight", "setTimeout", "showTrafficLightPopup", "handleMqttMessage", "message", "_payload$data3", "_payload$data4", "_payload$data5", "_payload$data6", "_payload$data7", "_payload$data8", "_payload$data9", "_payload$data10", "_payload$data11", "_payload$data12", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "newModel", "height", "rotation", "scale", "play", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "p", "modelData", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "child", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "rsiData", "rsuId", "events", "rtes", "event", "_payload$data2", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "messageId", "size", "idsArray", "from", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "arguments", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "eventCleanupInterval", "setInterval", "removeInactiveEvents", "setGlobalView", "cleanup3DEvents", "cancelAnimationFrame", "clearInterval", "marker", "removeAll", "objectsToRemove", "obj", "setAnimationLoop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "forceContextLoss", "handleMainVehicleChange", "intervalId", "removeEventListener", "timer", "createTrafficLights", "clearTimeout", "handleClick", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "lightObj", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "firstTrafficLightIntersection", "targetIntersection", "renderEntranceDeviceIcons", "converterInstance", "warn", "sceneTraverse", "设备总数", "路口总数", "existingIcons", "isEntranceDeviceIcons", "totalRenderedDevices", "entrances", "entrance", "isNaN", "d", "group", "iconSize", "iconSpacing", "totalWidth", "size3D", "spacing3D", "startX", "idx", "textureLoader", "TextureLoader", "iconPath", "iconMaterial", "bgMaterial", "bgWidth", "bgHeight", "bgGeometry", "PlaneGeometry", "bg<PERSON><PERSON>", "iconGeometry", "<PERSON><PERSON><PERSON>", "iconGroup", "deviceType", "isEntranceDeviceIcon", "pillarHeight", "pillarGeometry", "pillarMaterial", "pillar", "isEntranceDevicePillar", "_devicesData$devices", "devicesCount", "intersectionsCount", "retryTimer", "animateBillboard", "container", "sceneInstance", "cameraInstance", "rect", "getBoundingClientRect", "mouseX", "clientX", "clientWidth", "mouseY", "clientY", "clientHeight", "raycaster", "Raycaster", "params", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "intersects", "intersectObjects", "style", "placeholder", "onChange", "onSelect", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "min<PERSON><PERSON><PERSON>", "text", "parameters", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "textWidth", "measureText", "font", "textBaseline", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "<PERSON><PERSON><PERSON><PERSON>", "err", "types", "calculateDistance", "x1", "y1", "x2", "y2", "c", "sqrt", "getEventThresholds", "timeT<PERSON><PERSON>old", "distanceThreshold", "checkDuplicateEvent", "eventKey", "currentTime", "cachedEvent", "timeDiff", "lastUpdateTime", "updateCount", "isDuplicate", "matchedEvent", "newEventId", "padStart", "newEvent", "firstDetectedTime", "inactiveThreshold", "initialCount", "removedEvents", "inactiveTime", "removedCount", "getEventBackgroundColor", "getEventTypeName", "createEventMarker", "markerGroup", "backgroundGeometry", "backgroundMaterial", "side", "DoubleSide", "<PERSON><PERSON><PERSON>", "eventTypeName", "textLabel", "updateEventMarkerPosition", "eventData", "duplicateResult", "then", "catch", "createFallbackTrafficLights", "Error", "compassTextureLoader", "compassIconPath", "compassMaterial", "compassGeometry", "compassMesh", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "setPopoverState", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "index", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "numericId", "hasPhaseData", "compassStyle", "alignItems", "CompassIcon", "flexDirection", "borderLeft", "borderRight", "phaseMap", "dir", "typeOrder", "colorMap", "G", "Y", "R", "dirD<PERSON>", "N", "E", "S", "W", "gridTemplateRows", "gridTemplateColumns", "margin", "gridRow", "gridColumn", "displayIndex", "currentType", "marginStyle", "marginRight", "marginLeft", "getTrafficLightFromObject", "testClickDetection", "trafficLightObjects", "tlIntersects", "intersect", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "worldPos", "getWorldPosition", "distanceToCamera", "screenPos", "project", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\n\n\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport axios from 'axios'; // 新增 axios 导入\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\nimport DevicePopoverContent from './DevicePopoverContent';\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n\n\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n\n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n\n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n\n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n\n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n\n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n\n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n\n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n\n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n\n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n\n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n\n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n\n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n\n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = (model) => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\n\n// 添加事件去重相关的全局变量\nlet eventListCache = []; // 事件列表缓存，存储所有事件的完整信息\nlet eventIdCounter = 1; // 事件ID计数器\nlet eventMarkers = new Map(); // 存储事件标记的映射，key为eventId，value为THREE对象\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n\n  // 设备数据 state\n  const [devicesData, setDevicesData] = useState({ devices: [] });\n\n  // 动态加载设备数据\n  const loadDevicesData = async () => {\n    let devicesArray = [];\n    try {\n      // const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const apiUrl = BASE_URL;\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        devicesArray = response.data.data;\n      }\n\n      console.log('devicesArrayapiUrl', apiUrl);\n      console.log('devicesArray', devicesArray);\n    } catch (error) {\n      try {\n        const response = await fetch('/src/data/devices.json');\n        const json = await response.json();\n        devicesArray = json.devices || [];\n      } catch (e) {\n        console.error('设备数据加载失败', e);\n      }\n    }\n    setDevicesData({ devices: devicesArray });\n  };\n  useEffect(() => { loadDevicesData(); }, []);\n\n // 路口数据 state\n  const [intersections, setIntersections] = useState([]);\n  // 动态加载路口数据\n  useEffect(() => {\n    const fetchIntersections = async () => {\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/intersections`);\n        if (response.data && response.data.success) {\n          setIntersections(response.data.data || []);\n        }\n      } catch (error) {\n        console.error('获取路口信息失败:', error);\n      }\n    };\n    fetchIntersections();\n  }, []);\n\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',  // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 设备信息弹框状态\n  const [devicePopover, setDevicePopover] = useState({\n    visible: false,\n    deviceId: null,\n    position: { x: 0, y: 0 },\n    content: null\n  });\n\n  // 设备弹框关闭函数\n  const handleCloseDevicePopover = () => {\n    setDevicePopover({ visible: false, deviceId: null, position: { x: 0, y: 0 }, content: null });\n  };\n\n  // 设备弹框内容渲染函数\n  const renderDevicePopoverContent = (device) => {\n    if (!device) return null;\n    return <DevicePopoverContent device={device} />;\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersections.find(i => i.name === value);\n\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n\n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n\n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n\n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n\n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n\n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n\n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1': // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2': // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3': // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return; // 跳过未知类型\n            }\n\n            // 获取或创建模型\n          let model = vehicleModels.get(id);\n\n          if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n              // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n\n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n\n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n\n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n\n              scene.add(newModel);\n\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n              type: type\n              });\n          } else if (model) {\n              // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n\n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ?\n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 10,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n\n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ?\n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n\n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n\n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n\n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n\n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ?\n                    getDirectionFromCode(phase.trafficDirec) :\n                    getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n\n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef &&\n                     (window.currentPopoverIdRef.current === modelKey ||\n                      window.currentPopoverIdRef.current === String(modelKey) ||\n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n\n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor, eventType, {\n            rsuId: payload.data?.rsuId || 'UNKNOWN',\n            eventId: eventId,\n            description: description\n          });\n\n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f', '2', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14', '1002', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45', '404', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff', '10', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d', '12', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a', '13', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1', '21-8', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96', '904', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16', '910', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2', '999', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n        }\n\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n\n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n\n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n\n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/vehicle.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n\n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n\n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n\n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n\n                  // 应用新材质\n                  child.material = newMaterial;\n\n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n\n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n\n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n\n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n\n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n\n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n\n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n\n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach((mixer) => {\n          mixer.update(deltaTime);\n        });\n      }\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加事件清理定时器\n    const eventCleanupInterval = setInterval(() => {\n      removeInactiveEvents();\n    }, 30000); // 每30秒清理一次非活跃事件\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // // 添加全局测试函数用于验证事件去重功能\n    // window.test3DEventDeduplication = () => {\n    //   console.log('🧪 开始3D场景事件去重测试');\n\n    //   // 测试位置\n    //   const testPosition = { x: 100, y: 100 };\n\n    //   // 测试1：创建新事件\n    //   console.log('测试1：创建新的违停车辆事件');\n    //   showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n\n    //   // 测试2：相同位置的重复事件（应该被去重）\n    //   setTimeout(() => {\n    //     console.log('测试2：相同位置的重复事件（应该被去重）');\n    //     showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n    //   }, 1000);\n\n    //   // 测试3：稍微不同位置的事件（距离在阈值内，应该被去重）\n    //   setTimeout(() => {\n    //     console.log('测试3：稍微不同位置的事件（应该被去重）');\n    //     showWarningMarker({ x: 105, y: 105 }, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n    //   }, 2000);\n\n    //   // 测试4：不同类型的事件（应该创建新事件）\n    //   setTimeout(() => {\n    //     console.log('测试4：不同类型的事件（应该创建新事件）');\n    //     showWarningMarker(testPosition, '逆行车辆', '#eb2f96', '904', { rsuId: 'TEST_RSU' });\n    //   }, 3000);\n\n    //   // 测试5：查看缓存状态\n    //   setTimeout(() => {\n    //     console.log('📊 3D场景事件缓存状态:', {\n    //       缓存事件数: eventListCache.length,\n    //       事件ID计数器: eventIdCounter,\n    //       场景标记数: eventMarkers.size,\n    //       事件列表: eventListCache.map(e => ({\n    //         ID: e.eventId,\n    //         类型: e.eventType,\n    //         更新次数: e.updateCount,\n    //         位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`\n    //       }))\n    //     });\n    //   }, 4000);\n    // };\n\n    // 添加手动清理3D事件的全局函数\n    window.cleanup3DEvents = () => {\n      console.log('🧹 手动清理3D场景事件');\n      removeInactiveEvents();\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理事件清理定时器\n      if (eventCleanupInterval) {\n        clearInterval(eventCleanupInterval);\n      }\n\n      // 3. 清理所有事件标记\n      eventMarkers.forEach((marker) => {\n        if (scene && marker) {\n          scene.remove(marker);\n        }\n      });\n      eventMarkers.clear();\n      eventListCache.length = 0;\n\n      // 4. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse((object) => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n\n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n\n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current && intersections && intersections.length > 0) {\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current && intersections && intersections.length > 0) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current, intersections);\n        }\n      }, 2000);\n\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景、坐标转换器或路口数据未准备好，暂不创建红绿灯');\n    }\n  }, [scene, intersections]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersections && intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersections.find(\n          intersection => intersection.hasTrafficLight !== false && intersection.interId\n        );\n\n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersections[0];\n\n        console.log('自动选择路口:', targetIntersection.name,\n                    '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersections, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = (converterInstance) => {\n    if (!scene || typeof scene.traverse !== 'function' || !converterInstance) {\n      console.warn('设备图标渲染条件不满足:', {\n        scene: !!scene,\n        sceneTraverse: scene && typeof scene.traverse === 'function',\n        converter: !!converterInstance\n      });\n      return;\n    }\n\n    if (!devicesData.devices || devicesData.devices.length === 0) {\n      console.warn('设备数据未加载，跳过设备图标渲染');\n      return; // 设备数据未加载时不渲染\n    }\n\n    if (!intersections || intersections.length === 0) {\n      console.warn('路口数据未加载，跳过设备图标渲染');\n      return;\n    }\n\n    console.log('开始渲染设备图标:', {\n      设备总数: devicesData.devices.length,\n      路口总数: intersections.length\n    });\n\n    try {\n      // 清理之前的设备图标\n      const existingIcons = scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons);\n      existingIcons.forEach(obj => scene.remove(obj));\n      console.log('清理了', existingIcons.length, '个旧的设备图标');\n\n      let totalRenderedDevices = 0;\n\n      intersections.forEach(intersection => {\n        if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n        intersection.entrances.forEach((entrance) => {\n          if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n            return;\n          }\n\n          const devices = devicesData.devices.filter(\n            d => d.location === intersection.name && d.entrance === entrance.name\n          );\n          if (devices.length === 0) return;\n\n          // 经纬度转模型坐标\n          console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n          const modelPos = converterInstance.wgs84ToModel(\n            parseFloat(entrance.longitude),\n            parseFloat(entrance.latitude)\n          );\n          // 创建一个组用于存放所有图标\n          const group = new THREE.Group();\n          group.position.set(modelPos.x, 10, -modelPos.y); // 上方10米\n          group.userData = { isEntranceDeviceIcons: true };\n          // 图标排成一排，居中\n          const iconSize = 32; // px\n          const iconSpacing = 8; // px\n          const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n          // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n          const size3D = 4.0; // 图标尺寸加大\n          const spacing3D = 0.5; // 图标间距加大\n          const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n          devices.forEach((device, idx) => {\n            // 创建一个平面用于显示SVG图标\n            const textureLoader = new THREE.TextureLoader();\n            const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n            // 图标材质\n            const iconMaterial = new THREE.MeshBasicMaterial({\n              map: textureLoader.load(iconPath),\n              transparent: true,\n              opacity: 1 // 图标完全不透明\n            });\n            // 图标背景板材质\n            const bgMaterial = new THREE.MeshBasicMaterial({\n              color: 0x000000,\n              transparent: true,\n              opacity: 0.7 // 半透明黑色\n            });\n            // 背景板尺寸略大于图标\n            const bgWidth = size3D * 1.25;\n            const bgHeight = size3D * 1.25;\n            // 背景板几何体（圆角矩形）\n            // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n            const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n            // 图标几何体\n            const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n            const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n            // 图标略微前移，避免Z轴重叠闪烁\n            iconMesh.position.set(0, 0, 0.01);\n            // 创建一个组，包含背景和图标\n            const iconGroup = new THREE.Group();\n            iconGroup.add(bgMesh);\n            iconGroup.add(iconMesh);\n            // 整体平移到正确位置\n            iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n            // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n            iconGroup.renderOrder = 999; // 提高渲染优先级\n            iconGroup.userData = {\n              deviceId: device.id,\n              deviceType: device.type,\n              entrance: entrance.name,\n              isEntranceDeviceIcon: true\n            };\n            group.add(iconGroup);\n          });\n          // 新增：添加白色半透明光柱，指向设备图标组\n          // 光柱高度等于图标组的y坐标（即10），底部在地面y=0，顶部在图标组中心\n          const pillarHeight =  group.position.y; // 10\n          const pillarGeometry = new THREE.CylinderGeometry(0.2, 0.2, pillarHeight, 16);\n          const pillarMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.7 });\n          const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);\n          // 设置光柱中心在y=0~y=10之间，底部正好在地面\n          pillar.position.set(0, -pillarHeight / 2, 0);\n          // pillar.position.set(0, -pillarHeight, 0);\n          // 可选：添加标记，便于后续查找或清理\n          pillar.userData = { isEntranceDevicePillar: true };\n          group.add(pillar);\n          scene.add(group);\n        });\n      });\n\n    } catch (e) {\n      console.error('renderEntranceDeviceIcons error', e);\n      return;\n    }\n  };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current && devicesData.devices) {\n      console.log('触发设备图标渲染:', {\n        scene: !!scene,\n        converter: !!converter.current,\n        devicesCount: devicesData.devices?.length || 0,\n        intersectionsCount: intersections?.length || 0\n      });\n\n      // 延迟渲染，确保所有资源都已准备好\n      const timer = setTimeout(() => {\n        renderEntranceDeviceIcons(converter.current);\n      }, 500); // 延迟500ms\n\n      return () => clearTimeout(timer);\n    }\n  }, [scene, converter.current, devicesData.devices, intersections]); // 添加intersections依赖\n\n  // 添加额外的重试机制，确保设备图标能够正确渲染\n  useEffect(() => {\n    if (scene && converter.current && devicesData.devices && intersections) {\n      // 延迟3秒后检查是否有设备图标，如果没有则重新渲染\n      const retryTimer = setTimeout(() => {\n        const existingIcons = scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons);\n        if (existingIcons.length === 0) {\n          console.log('检测到设备图标缺失，执行重试渲染');\n          renderEntranceDeviceIcons(converter.current);\n        } else {\n          console.log('设备图标渲染正常，共', existingIcons.length, '个图标组');\n        }\n      }, 3000); // 3秒后检查\n\n      return () => clearTimeout(retryTimer);\n    }\n  }, [scene, converter.current, devicesData.devices, intersections]);\n\n  // 新增：每帧让所有设备图标组和事件图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有图标组，让其正对相机\n      scene.children.forEach(obj => {\n        // 设备图标组\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n        // 事件图标组\n        if (obj.userData && obj.userData.type === 'eventMarker') {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n\n  // 修改点击处理函数\n  const handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n    if (!container || !sceneInstance || !cameraInstance) return;\n    const rect = container.getBoundingClientRect();\n    const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n    const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 1;\n    raycaster.params.Line.threshold = 1;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraInstance);\n    const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n    if (intersects.length > 0) {\n      for (let i = 0; i < intersects.length; i++) {\n        const obj = intersects[i].object;\n        if (obj.parent && obj.parent.userData && obj.parent.userData.isEntranceDeviceIcon) {\n          const deviceId = obj.parent.userData.deviceId;\n          console.log('devicesDatahandleMouseClick', devicesData);\n          const device = devicesData.devices.find(d => d.id === deviceId);\n          if (device) {\n            const x = event.clientX;\n            const y = event.clientY;\n            setDevicePopover({\n              visible: true,\n              deviceId,\n              position: { x, y },\n              content: renderDevicePopoverContent(device)\n            });\n            return; // 命中设备图标后直接返回\n          }\n        }\n      }\n    }\n    // ...原有红绿灯弹框逻辑保持不变...\n    // ... existing code ...\n  };\n\n\n\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        onSelect={handleIntersectionChange} // 添加onSelect事件，确保每次选择都触发\n        options={intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{\n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n\n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div\n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button\n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n\n      <div style={buttonContainerStyle}>\n        <button\n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button\n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n      {devicePopover.visible && (\n        <div\n          style={{\n            position: 'absolute',\n            left: `${devicePopover.position.x}px`,\n            top: `${devicePopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1100,\n            backgroundColor: 'rgba(0, 0, 0, 0.92)',\n            color: 'white',\n            borderRadius: '6px',\n            boxShadow: '0 2px 12px rgba(0,0,0,0.35)',\n            padding: 0,\n            minWidth: 320,\n            maxWidth: 350,\n            fontSize: 13\n          }}\n        >\n          {devicePopover.content}\n          <button\n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '16px',\n              cursor: 'pointer',\n              padding: '2px 10px',\n              zIndex: 1200\n            }}\n            onClick={handleCloseDevicePopover}\n          >×</button>\n        </div>\n      )}\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 4 * params.padding + 4 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // // 只有在有边框或背景时才绘制背景和边框\n  // if (params.borderThickness > 0 || params.backgroundColor.a > 0) {\n  //   // 绘制背景和边框（圆角矩形）\n  //   const radius = 8;\n  //   context.beginPath();\n  //   context.moveTo(params.borderThickness + radius, params.borderThickness);\n  //   context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  //   context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  //   context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  //   context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  //   context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  //   context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  //   context.lineTo(params.borderThickness, params.borderThickness + radius);\n  //   context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  //   context.closePath();\n\n  //   // 设置背景填充（如果有背景）\n  //   if (params.backgroundColor.a > 0) {\n  //     context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  //     context.fill();\n  //   }\n\n  //   // 设置边框颜色（如果有边框）\n  //   if (params.borderThickness > 0) {\n  //     context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  //     context.lineWidth = params.borderThickness;\n  //     context.stroke();\n  //   }\n  // }\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  // sprite.scale.set(10, 5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n\n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n\n    ]);\n\n\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n\n      }\n      if (child.isMesh){\n        child.castShadow = true;\n      }\n    });\n\n\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n\n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\n// 添加计算两点之间距离的函数\nconst calculateDistance = (x1, y1, x2, y2) => {\n\n  const c = Math.sqrt((x1-x2)*(x1-x2)+(y1-y2)*(y1-y2));\n  return  c;\n};\n\n// 获取事件类型的阈值配置\nconst getEventThresholds = (eventType) => {\n  switch(eventType) {\n    case '910': // 违停车辆\n      return { timeThreshold: 300000, distanceThreshold: 10 }; // 5分钟, 20米\n    case '904': // 逆行车辆\n      return { timeThreshold: 10000, distanceThreshold: 20 }; // 10秒, 20米\n    case '901': // 车辆超速\n      return { timeThreshold: 30000, distanceThreshold: 20 }; // 30秒, 50米\n    case '401': // 道路抛洒物\n    case '404': // 道路障碍物\n    case '1002': // 道路施工\n      return { timeThreshold: 600000, distanceThreshold: 10 }; // 10分钟, 30米\n    case '405': // 行人通过马路\n      return { timeThreshold: 10000, distanceThreshold: 10 }; // 10秒, 10米\n    default:\n      return { timeThreshold: 5000, distanceThreshold: 5 }; // 5秒, 5米\n  }\n};\n\n// 优化后的事件重复检查逻辑\nconst checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {\n  const { timeThreshold, distanceThreshold } = getEventThresholds(eventType);\n\n  // 遍历事件列表缓存中的所有事件\n  for (let i = 0; i < eventListCache.length; i++) {\n    const cachedEvent = eventListCache[i];\n\n    // 检查事件类型是否相同\n    if (cachedEvent.eventType !== eventType) {\n      continue;\n    }\n\n    // 计算时间差\n    const timeDiff = currentTime - cachedEvent.lastUpdateTime;\n\n    // 检查时间差是否在阈值内\n    if (timeDiff > timeThreshold) {\n      continue;\n    }\n\n    // 计算距离\n    const distance = calculateDistance(\n      currentPos.x, currentPos.y,\n      cachedEvent.position.x, cachedEvent.position.y\n    );\n\n    // 检查距离是否在阈值内\n    if (distance <= distanceThreshold) {\n      // 找到匹配的事件，更新信息\n      cachedEvent.eventKey = eventKey;\n      cachedEvent.lastUpdateTime = currentTime;\n      cachedEvent.position = { ...currentPos };\n      cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;\n\n      console.log(`🔄 3D场景检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);\n\n      return {\n        isDuplicate: true,\n        eventId: cachedEvent.eventId,\n        matchedEvent: cachedEvent\n      };\n    }\n  }\n\n  // 没有找到匹配的事件，创建新事件\n  const newEventId = `3D_EVT_${eventIdCounter.toString().padStart(6, '0')}`;\n  eventIdCounter++;\n\n  const newEvent = {\n    eventId: newEventId,\n    eventType: eventType,\n    eventKey: eventKey,\n    firstDetectedTime: currentTime,\n    lastUpdateTime: currentTime,\n    position: { ...currentPos },\n    updateCount: 1\n  };\n\n  // 添加到事件列表缓存\n  eventListCache.push(newEvent);\n\n\n\n  return {\n    isDuplicate: false,\n    eventId: newEventId,\n    newEvent: newEvent\n  };\n};\n\n// 删除2s内没有更新的事件\nconst removeInactiveEvents = () => {\n  const currentTime = Date.now();\n  const inactiveThreshold = 2000; // 2s\n\n  const initialCount = eventListCache.length;\n  const removedEvents = [];\n\n  eventListCache = eventListCache.filter(event => {\n    const timeSinceLastUpdate = currentTime - event.lastUpdateTime;\n    if (timeSinceLastUpdate > inactiveThreshold) {\n      removedEvents.push({\n        id: event.eventId,\n        type: event.eventType,\n        inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)\n      });\n\n      // 从场景中移除对应的标记\n      const marker = eventMarkers.get(event.eventId);\n      if (marker && scene) {\n        scene.remove(marker);\n        eventMarkers.delete(event.eventId);\n      }\n\n      return false; // 删除该事件\n    }\n    return true; // 保留该事件\n  });\n\n  const removedCount = initialCount - eventListCache.length;\n  if (removedCount > 0) {\n    console.log(`🗑️ 3D场景删除了 ${removedCount} 个1s内未更新的事件:`);\n    removedEvents.forEach(event => {\n      console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);\n    });\n    console.log(`📊 3D场景当前缓存事件数: ${eventListCache.length}`);\n  }\n};\n\n// 根据事件类型获取背景颜色\nconst getEventBackgroundColor = (eventType) => {\n  switch(eventType) {\n    case '910': // 违停车辆\n      return 0xff4d4f; // 红色背景 - 严重违规\n    case '904': // 逆行车辆\n      return 0xf5222d; // 深红色背景 - 危险行为\n    case '901': // 车辆超速\n      return 0xfa8c16; // 橙色背景 - 警告\n    case '401': // 道路抛洒物\n      return 0xfaad14; // 黄色背景 - 注意\n    case '404': // 道路障碍物\n      return 0xff7a45; // 橙红色背景 - 阻碍\n    case '1002': // 道路施工\n      return 0x1890ff; // 蓝色背景 - 信息\n    case '405': // 行人通过马路\n      return 0x52c41a; // 绿色背景 - 正常\n    case '2': // 交叉路口碰撞预警\n      return 0xff0000; // 纯红色背景 - 紧急\n    case '12': // 交通参与者碰撞预警\n      return 0xeb2f96; // 粉红色背景 - 预警\n    case '13': // 绿波车速引导\n      return 0x13c2c2; // 青色背景 - 引导\n    case '999': // 信号灯优先\n      return 0x722ed1; // 紫色背景 - 优先\n    default:\n      return 0xffa500; // 默认橙黄色背景\n  }\n};\n\n// 获取事件类型的中文名称\nconst getEventTypeName = (eventType) => {\n  switch(eventType) {\n    case '401': return '抛洒物';\n    case '404': return '障碍物';\n    case '405': return '行人';\n    case '904': return '逆行';\n    case '910': return '违停';\n    case '1002': return '施工';\n    case '901': return '超速';\n    case '2': return '碰撞';\n    case '12': return '碰撞';\n    case '13': return '绿波';\n    case '999': return '优先';\n    default: return `事件${eventType}`;\n  }\n};\n\n// 创建事件图标标记\nconst createEventMarker = (eventType, position, eventId) => {\n  if (!scene) {\n    console.warn('无法创建事件标记：场景不存在或已卸载');\n    return null;\n  }\n\n  try {\n    // 创建一个组来包含背景和图标\n    const markerGroup = new THREE.Group();\n\n    // 1. 创建统一的背景平面（包含图标和文字区域，类似截图中的设计）\n    const backgroundGeometry = new THREE.PlaneGeometry(6, 8); // 适当增加宽度和高度以容纳图标和文字\n    const backgroundColor = getEventBackgroundColor(eventType); // 根据事件类型获取背景色\n    const backgroundMaterial = new THREE.MeshBasicMaterial({\n      color: backgroundColor, // 使用事件类型对应的背景颜色\n      transparent: true,\n      opacity: 0.9, // 提高不透明度，使颜色更加鲜明\n      side: THREE.DoubleSide\n    });\n    const backgroundMesh = new THREE.Mesh(backgroundGeometry, backgroundMaterial);\n    backgroundMesh.position.set(0, -1, -0.01); // 向下偏移以居中整个标记，稍微向后作为背景\n    backgroundMesh.renderOrder = 998; // 背景渲染优先级\n    markerGroup.add(backgroundMesh);\n\n    // 2. 创建图标平面（位于背景上方区域）\n    const textureLoader = new THREE.TextureLoader();\n    const iconPath = `${BASE_URL}/images/${eventType}.svg`; // 事件图标路径\n\n    const iconMaterial = new THREE.MeshBasicMaterial({\n      map: textureLoader.load(iconPath,\n        // 加载成功回调\n        (texture) => {\n          console.log(`事件图标加载成功: 事件${eventType}.svg`);\n        },\n        // 加载进度回调\n        undefined,\n        // 加载失败回调\n        (error) => {\n          console.warn(`事件图标加载失败: 事件${eventType}.svg，使用默认图标`);\n          // 可以在这里设置默认图标\n        }\n      ),\n      transparent: true,\n      opacity: 1, // 完全不透明\n      side: THREE.DoubleSide\n    });\n\n    // 创建图标几何体（参考截图中的比例）\n    const iconGeometry = new THREE.PlaneGeometry(5, 5); // 宽度5，高度4，类似截图中的车辆图标比例\n    const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n    iconMesh.position.set(0, 0.3, 0.01); // 图标位于背景上半部分\n    iconMesh.renderOrder = 999; // 图标渲染优先级\n    markerGroup.add(iconMesh);\n\n    // 3. 创建文字标签（显示在图标正下方，参考截图布局）\n    const eventTypeName = getEventTypeName(eventType);\n    const textLabel = createTextSprite(eventTypeName, {\n      backgroundColor: { r: 0, g: 0, b: 0, a: 0.0 }, // 完全透明背景，与图标共用背景\n      textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文字\n      fontSize: 14, // 增大字体以提高可读性\n      padding: 0, // 无填充\n      borderThickness: 0, // 无边框\n      fontWeight: 'bold' // 加粗字体，类似截图效果\n    });\n    textLabel.position.set(0, -3.5, 0.02); // 位于图标正下方，紧贴背景下半部分\n    textLabel.renderOrder = 1000; // 确保在最上层渲染\n    textLabel.scale.set(6, 3, 1); // 调整文字标签的缩放以适应背景\n    markerGroup.add(textLabel);\n\n    // 设置组的位置，高度为15米\n    markerGroup.position.set(position.x, 15, -position.y);\n\n    // 让整个组始终面向相机\n    // markerGroup.lookAt(0, 15, 0);\n// 创建时直接朝向相机，避免角度跳跃\n    // if (cameraRef.current) {\n    //   markerGroup.lookAt(cameraRef.current.position.x, markerGroup.position.y, cameraRef.current.position.z);\n    // } else {\n    //   markerGroup.lookAt(0, markerGroup.position.y, 0);\n    // }\n    // 设置渲染优先级，与设备图标保持一致\n    markerGroup.renderOrder = 999;\n\n    // 添加用户数据到组\n    markerGroup.userData = {\n      type: 'eventMarker',\n      eventId: eventId,\n      eventType: eventType\n    };\n\n    // 添加到场景\n    scene.add(markerGroup);\n\n    // 存储标记引用\n    eventMarkers.set(eventId, markerGroup);\n\n    console.log(`📍 创建事件标记 ${eventType} (${eventTypeName}) (ID: ${eventId})，位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)})，背景色: #${backgroundColor.toString(16)}`);\n\n    return markerGroup;\n  } catch (error) {\n    console.error('创建事件标记时出错:', error);\n    return null;\n  }\n};\n\n// 更新事件标记位置\nconst updateEventMarkerPosition = (eventId, newPosition) => {\n  const markerGroup = eventMarkers.get(eventId);\n  if (markerGroup && scene) {\n    markerGroup.position.set(newPosition.x, 15, -newPosition.y);\n    // 重新设置朝向相机\n    // markerGroup.lookAt(0, 15, 0);\n    console.log(`📍 更新事件标记位置 (ID: ${eventId})，新位置: (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)})`);\n  }\n};\n\nconst showWarningMarker = (position, text, color, eventType = '999', eventData = {}) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n\n  try {\n    // 生成事件Key用于去重\n    const currentTime = Date.now();\n    const eventKey = `${eventData.rsuId || 'UNKNOWN'}_${eventType}_${position.x.toFixed(0)}_${position.y.toFixed(0)}`;\n\n    // 转换模型坐标为经纬度（用于距离计算）\n    // const converter = new CoordinateConverter();\n    // const wgs84Pos = converter.modelToWgs84(position.x, position.y);\n\n    // 检查事件去重\n    const duplicateResult = checkDuplicateEvent(\n      eventType,\n      eventKey,\n      currentTime,\n      position\n    );\n\n    const isDuplicate = duplicateResult.isDuplicate;\n\n    console.log(`🔍 3D场景事件去重检查 - 类型: ${eventType}, EventKey: ${eventKey}, 位置: (${position.x}, ${position.y}), 结果: ${isDuplicate ? '重复' : '新事件'}`);\n\n    if (isDuplicate) {\n      // 重复事件，更新现有标记位置\n      updateEventMarkerPosition(duplicateResult.eventId, position);\n    } else {\n      // 新事件，创建新的标记\n      createEventMarker(eventType, position, duplicateResult.eventId);\n    }\n\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance, intersections) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n\n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n      .then(trafficLightGltf => {\n        preloadedTrafficLightModel = trafficLightGltf.scene;\n        preloadedTrafficLightModel.scale.set(6, 6, 6);\n        preloadedTrafficLightModel.traverse((child) => {\n          if (child.isMesh && child.material) {\n            child.material.metalness = 0.2;\n            child.material.roughness = 0.3;\n            child.material.envMapIntensity = 1.2;\n          }\n        });\n        console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n        // 重新调用创建函数\n        createTrafficLights(converterInstance, intersections);\n      })\n      .catch(error => {\n        console.error('红绿灯模型重新加载失败:', error);\n        // 如果加载失败，使用简单的替代物体\n        createFallbackTrafficLights(converterInstance, intersections);\n      });\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n\n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 将红绿灯添加到场景中\n        // scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = `${BASE_URL}/images/compass.svg`; // public目录下的路径\n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n          transparent: true,\n          opacity: 1\n        });\n        // 平面几何体，5x5米\n        const compassGeometry = new THREE.PlaneGeometry(5, 5);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x+20, 1.5, -(modelPos.y+20));\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        // createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance, intersections) => {\n  intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  trafficLightModel.add(lightMesh);\n\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false,\n    content: null, // 清空内容\n    phases: []     // 清空相位信息\n  }));\n\n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n\n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n\n              return (\n                <div key={index} style={{\n                  marginBottom: '6px',\n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{\n                      color: lightColor,\n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 -500;\n    const centerY = window.innerHeight / 2 -500;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n\n  return list;\n};\n\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n\n    let content;\n\n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n\n    // 指北针组件\n    const CompassIcon = () => (\n      <div style={compassStyle}>\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        }}>\n          <span style={{\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          }}>N</span>\n          <span style={{\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }}></span>\n        </div>\n      </div>\n    );\n\n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },\n        '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },\n        '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },\n        '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }\n      };\n\n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };\n      const dirData = { N: {}, E: {}, S: {}, W: {} };\n\n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n\n      content = (\n        <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>\n          <div style={{\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          }}>\n            {/* 北 - 左转、直行、右转箭头水平对齐，倒计时在箭头上面 */}\n            {/* <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%', paddingLeft: '5px' }}> */}\n            <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n\n                const currentType = typeOrder[displayIndex];\n\n                // 计算与南边对齐的样式\n                const marginStyle = {};\n                if (currentType === 'left') { // 左转箭头 (右侧显示)\n                  marginStyle.marginRight = '0px';\n                } else if (currentType === 'straight') { // 直行箭头 (中间显示)\n                  marginStyle.marginLeft = '10px';\n                  marginStyle.marginRight = '10px';\n                } else if (currentType === 'right') { // 右转箭头 (左侧显示)\n                  marginStyle.marginLeft = '0px';\n                }\n\n                return dirData.N[currentType] && (\n                  // <div key={currentType} style={{\n                  //   display: 'flex',\n                  //   flexDirection: 'column',\n                  //   alignItems: 'center',\n                  //   ...marginStyle\n                  // }}>\n                  <div key={currentType} style={{marginRight: currentType === 'left' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                    <div style={{fontSize:'14px', color: dirData.N[currentType].color, fontWeight:'bold', marginBottom: '3px'}}>{dirData.N[currentType].remainTime}</div>\n                    <span style={{color: dirData.N[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n\n            {/* 南 - 左转、直行、右转箭头水平对齐，倒计时在箭头下面 */}\n            <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n              {typeOrder.map(type => dirData.S[type] && (\n                <div key={type} style={{marginRight: type === 'right' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                  <span style={{color: dirData.S[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'}\n                  </span>\n                  <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold', marginTop: '3px'}}>{dirData.S[type].remainTime}</div>\n                </div>\n              ))}\n            </div>\n\n            {/* 东 - 倒计时显示在箭头右边 */}\n\n            <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>\n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n\n                const currentType = typeOrder[displayIndex];\n\n                return dirData.E[currentType] && (\n                  <div key={currentType} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>\n                    <span style={{color: dirData.E[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'}\n                    </span>\n                    <div style={{\n                      fontSize:'14px',\n                      color: dirData.E[currentType].color,\n                      fontWeight:'bold',\n                      marginLeft: '5px'\n                    }}>{dirData.E[currentType].remainTime}</div>\n                  </div>\n                );\n              })}\n            </div>\n\n            {/* 西 - 倒计时显示在箭头左边 */}\n            <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>\n              {typeOrder.map(type => dirData.W[type] && (\n                <div key={type} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>\n                  <div style={{\n                    fontSize:'14px',\n                    color: dirData.W[type].color,\n                    fontWeight:'bold',\n                    marginRight: '5px'\n                  }}>{dirData.W[type].remainTime}</div>\n                  <span style={{color: dirData.W[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      // 没有相位数据时显示的内容\n      content = (\n        <div style={{ padding: '15px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '10px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}</div>\n          <div style={{\n            textAlign: 'center',\n            padding: '20px 0',\n            color: '#ff9800',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          }}>\n            当前无信号灯状态信息\n          </div>\n          <div style={{ fontSize: '12px', color: '#888', textAlign: 'center' }}>\n            路口ID: {interId}\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    }\n\n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: { x, y },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n\n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n\n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称',\n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称',\n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n\n        if (isVisible) {\n          visibleCount++;\n        }\n\n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n\n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel;\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,GAAK,CAAAC,KAAK,KAAM,OAAO,CAC9B,OAASC,UAAU,KAAQ,uCAAuC,CAClE,OAASC,aAAa,KAAQ,2CAA2C,CACzE,OAASC,mBAAmB,KAAQ,8BAA8B,CAClE,MAAO,GAAK,CAAAC,KAAK,KAAM,mBAAmB,CAC1C,MAAO,GAAK,CAAAC,aAAa,KAAM,2CAA2C,CAG1E,MAAO,CAAAC,IAAI,KAAM,MAAM,CACvB,OAASC,MAAM,CAAEC,OAAO,KAAQ,MAAM,CACtC,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAE;AAC3B,MAAO,CAAAC,WAAW,KAAM,eAAe,CAAE;AACzC,MAAO,CAAAC,oBAAoB,KAAM,wBAAwB,CACzD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,GAAI,CAAAC,gBAAgB,CAAG,IAAI,CAC3B,GAAI,CAAAC,gBAAgB,CAAG,EAAE,CACzB,GAAI,CAAAC,iBAAiB,CAAG,CAAC,CACzB,GAAI,CAAAC,oBAAoB,CAAG,IAAI,CAC/B,GAAI,CAAAC,cAAc,CAAG,IAAI,CAAG;AAC5B,GAAI,CAAAC,eAAe,CAAG,IAAI,CAAE;AAC5B,GAAI,CAAAC,QAAQ,CAAG,KAAK,CAAO;AAC3B,GAAI,CAAAC,UAAU,CAAG,QAAQ,CAAE;AAC3B,GAAI,CAAAC,QAAQ,CAAG,IAAI,CAAE;AAErB;AACA,GAAI,CAAAC,qBAAqB,CAAG,IAAI,CAChC,GAAI,CAAAC,qBAAqB,CAAG,IAAI,CAAG;AACnC,GAAI,CAAAC,oBAAoB,CAAG,IAAI,CAAI;AACnC,GAAI,CAAAC,0BAA0B,CAAG,IAAI,CAAE;AACvC,GAAI,CAAAC,KAAK,CAAG,IAAI,CAAE;AAElB,GAAI,CAAAC,eAAe,CAAE,IAAI,CAAE;AAC3B,GAAI,CAAAC,QAAQ,CAAE,IAAI,CAElB;AACA,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,KAAM,CAAAC,KAAK,CAAG,IAAI,CAAE;AAEpB;AACA,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE;AACxC,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CAAE;AAKxC;AACA,KAAM,CAAAE,WAAW,CAAG,CAClBC,MAAM,CAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAChCC,IAAI,CAAE,IAAI,CACV;AACEC,GAAG,CAAE,2BAA2B,CAChCC,GAAG,CAAE,2BAA2B,CAChChB,KAAK,CAAE,6BAA6B,CACtCiB,GAAG,CAAE,2BAA2B,CAAG;AACnCC,IAAI,CAAE,4BAA8B;AACtC,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC,CAE1D;AACA,KAAM,CAAAG,aAAa,CAAG,GAAI,CAAAlB,GAAG,CAAC,CAAC,CAAE;AAEjC;AACA,GAAI,CAAAmB,gBAAgB,CAAG,GAAI,CAAAnB,GAAG,CAAC,CAAC,CAAE;AAElC;AACA,GAAI,CAAAoB,gBAAgB,CAAG,IAAI,CAE3B;AACA,GAAI,CAAAC,gBAAgB,CAAG,GAAI,CAAArB,GAAG,CAAC,CAAC,CAAE;AAClC,GAAI,CAAAsB,kBAAkB,CAAG,GAAI,CAAAtB,GAAG,CAAC,CAAC,CAAE;AAEpC,KAAM,CAAAuB,KAAK,CAAG,GAAI,CAAA7D,KAAK,CAAC8D,KAAK,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAC,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC,CAC7D,KAAM,CAAAgB,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,EAAIA,IAAI,CAACE,QAAQ,EAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,CAAE,CACzD,KAAM,CAAAG,WAAW,CAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,aAAa,GAAK,IAAI,CAAC,CACrE,GAAIH,WAAW,EAAIA,WAAW,CAACI,KAAK,CAAE,CACpCjB,gBAAgB,CAAGa,WAAW,CAACI,KAAK,CACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEG,gBAAgB,CAAC,CAC7C,MAAO,CAAAA,gBAAgB,CACzB,CACF,CACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAChCG,gBAAgB,CAAG,OAAO,CAAE;AAC5B,MAAO,CAAAA,gBAAgB,CACzB,CAAE,MAAOkB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjClB,gBAAgB,CAAG,OAAO,CAAE;AAC5B,MAAO,CAAAA,gBAAgB,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAmB,aAAa,CAAGA,CAACC,QAAQ,CAAEC,SAAS,CAAEC,KAAK,GAAK,CACpD,GAAID,SAAS,GAAK,IAAI,CAAE,MAAO,CAAAD,QAAQ,CACvC,MAAO,CAAAE,KAAK,CAAGF,QAAQ,CAAG,CAAC,CAAC,CAAGE,KAAK,EAAID,SAAS,CACnD,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAACC,MAAM,CAAEC,SAAS,GAAK,CAC5C;AACA,GAAI,CAACA,SAAS,CAAE,CAChB,GAAI,CAACjD,YAAY,CAAE,CACjBA,YAAY,CAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC,CAC7B,MAAO,CAAAF,MAAM,CACf,CAEA,KAAM,CAAAG,SAAS,CAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,CAAEpD,YAAY,CAACoD,CAAC,CAAElD,KAAK,CAAC,CAChE,KAAM,CAAAmD,SAAS,CAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,CAAEtD,YAAY,CAACsD,CAAC,CAAEpD,KAAK,CAAC,CAChE,KAAM,CAAAqD,SAAS,CAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,CAAExD,YAAY,CAACwD,CAAC,CAAEtD,KAAK,CAAC,CAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,CAAEE,SAAS,CAAEE,SAAS,CAAC,CACjD,MAAO,CAAAvD,YAAY,CAACkD,KAAK,CAAC,CAAC,CAC3B,CAEA;AACA,GAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,CAAE,CACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAF,MAAM,CACf,CAEA,KAAM,CAAAW,OAAO,CAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC,CAEnD;AACA,KAAM,CAAAY,QAAQ,CAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC,CAC3C,KAAM,CAAAe,sBAAsB,CAAG,EAAE,CAAE;AAEnC,GAAIF,QAAQ,CAAGE,sBAAsB,CAAE,CACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAF,MAAM,CACf,CAEA;AACA,KAAM,CAAAG,SAAS,CAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,CAAEO,OAAO,CAACP,CAAC,CAAElD,KAAK,CAAC,CAC3D,KAAM,CAAAmD,SAAS,CAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,CAAEK,OAAO,CAACL,CAAC,CAAEpD,KAAK,CAAC,CAC3D,KAAM,CAAAqD,SAAS,CAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,CAAEG,OAAO,CAACH,CAAC,CAAEtD,KAAK,CAAC,CAE3D,KAAM,CAAA+D,WAAW,CAAG,GAAI,CAAAnG,KAAK,CAACoG,OAAO,CAACf,SAAS,CAAEE,SAAS,CAAEE,SAAS,CAAC,CACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC,CAExD,MAAO,CAAAe,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAACC,WAAW,CAAEnB,SAAS,GAAK,CACjD;AACA,GAAI,CAACA,SAAS,CAAE,CAChB,GAAIhD,YAAY,GAAK,IAAI,CAAE,CACzBA,YAAY,CAAGmE,WAAW,CAC1B,MAAO,CAAAA,WAAW,CACpB,CAEA;AACA,GAAI,CAAAC,IAAI,CAAGD,WAAW,CAAGnE,YAAY,CACrC,GAAIoE,IAAI,CAAGC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CACvC,GAAIF,IAAI,CAAG,CAACC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAExC,KAAM,CAAAC,gBAAgB,CAAG7B,aAAa,CAAC1C,YAAY,CAAGoE,IAAI,CAAEpE,YAAY,CAAEC,KAAK,CAAC,CAChFD,YAAY,CAAGuE,gBAAgB,CAC7B,MAAO,CAAAA,gBAAgB,CACzB,CAEA;AACA,GAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,CAAE,CACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEmB,WAAW,CAAC,CAChD,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAK,OAAO,CAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC,CAEnD;AACA,GAAI,CAAAoB,IAAI,CAAGD,WAAW,CAAGK,OAAO,CAChC,GAAIJ,IAAI,CAAGC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CACvC,GAAIF,IAAI,CAAG,CAACC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAExC;AACA,KAAM,CAAAG,mBAAmB,CAAGJ,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE;AACzC,GAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,CAAGK,mBAAmB,CAAE,CACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,CAAG,GAAG,CAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEmB,WAAW,CAAC,CAChD,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAI,gBAAgB,CAAG7B,aAAa,CAAC8B,OAAO,CAAGJ,IAAI,CAAEI,OAAO,CAAEvE,KAAK,CAAC,CACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEuB,gBAAgB,CAAC,CAErD,MAAO,CAAAA,gBAAgB,CACzB,CAAC,CAED;AACA;AACA;AACA,KAAM,CAAAI,6BAA6B,CAAG,CAAC,CAAE;AACzC;AAEA;AACA,KAAM,CAAAC,eAAe,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACjC,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAA3E,GAAG,CAAC,CAAC,CAE1B;AACA,KAAM,CAAA4E,eAAe,CAAG,CACtBC,MAAM,CAAE,GAAI,CAAAH,GAAG,CAAC,CAAC,CACjBI,KAAK,CAAE,GAAI,CAAA9E,GAAG,CAAC,CAAC,CAChB+E,OAAO,CAAE,GAAI,CAAA/E,GAAG,CAAC,CAAC,CAClBgF,MAAM,CAAE,GAAI,CAAAN,GAAG,CAAC,CAAC,CAEjBO,QAAQA,CAACC,KAAK,CAAEC,KAAK,CAAE,CACrB,IAAI,CAACN,MAAM,CAACO,GAAG,CAACF,KAAK,CAAC,CACtB,GAAIC,KAAK,CAAE,CACT,IAAI,CAACH,MAAM,CAACI,GAAG,CAACD,KAAK,CAAC,CACtB;AACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,EAAI,CACvB,GAAIA,MAAM,CAACC,MAAM,CAAE,CACjB,IAAI,CAACT,KAAK,CAACzB,GAAG,CAACiC,MAAM,CAACE,IAAI,CAAEF,MAAM,CAAC,CACrC,CACF,CAAC,CAAC,CACJ,CACA,MAAO,CAAAJ,KAAK,CACd,CAAC,CAEDO,SAASA,CAACC,MAAM,CAAER,KAAK,CAAE,CACvB,GAAI,CAAC,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,CAAE,CAC5B,IAAI,CAACH,OAAO,CAAC1B,GAAG,CAAC6B,KAAK,CAAE,GAAI,CAAAR,GAAG,CAAC,CAAC,CAAC,CACpC,CACA,IAAI,CAACK,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACE,GAAG,CAACM,MAAM,CAAC,CACnC,MAAO,CAAAA,MAAM,CACf,CAAC,CAEDC,WAAWA,CAACT,KAAK,CAAE,CACjB,GAAI,IAAI,CAACL,MAAM,CAACvB,GAAG,CAAC4B,KAAK,CAAC,CAAE,CAC1B,GAAI,CACF;AACA,GAAI,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,CAAE,CAC3B,IAAI,CAACH,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACU,OAAO,CAACF,MAAM,EAAI,CACxC,GAAIA,MAAM,EAAI,MAAO,CAAAA,MAAM,CAACG,IAAI,GAAK,UAAU,CAAE,CAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC,CACf,CACF,CAAC,CAAC,CACF,IAAI,CAACd,OAAO,CAACe,MAAM,CAACZ,KAAK,CAAC,CAC5B,CAEA;AACA,GAAI,MAAO,CAAAA,KAAK,CAACa,aAAa,GAAK,UAAU,CAAE,CAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC,CACvB,CAEA;AACA,KAAM,CAAAC,IAAI,CAAGd,KAAK,CAACe,OAAO,CAAC,CAAC,CAC5B,GAAID,IAAI,CAAE,CACR,IAAI,CAAChB,MAAM,CAACc,MAAM,CAACE,IAAI,CAAC,CACxBA,IAAI,CAACX,QAAQ,CAACC,MAAM,EAAI,CACtB,GAAIA,MAAM,EAAIA,MAAM,CAACC,MAAM,CAAE,CAC3B,IAAI,CAACT,KAAK,CAACgB,MAAM,CAACR,MAAM,CAACE,IAAI,CAAC,CAChC,CACA,GAAIF,MAAM,EAAIA,MAAM,CAACY,UAAU,CAAE,CAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,CAAG,CAAC,CAC9B,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CACF,GAAI,MAAO,CAAAjB,KAAK,CAACkB,WAAW,GAAK,UAAU,CAAE,CAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC,CACzB,CAEA,GAAI,MAAO,CAAAd,KAAK,CAACmB,aAAa,GAAK,UAAU,CAAE,CAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,CAAEL,IAAI,CAAC,CACjC,CAEA;AACA,GAAI,MAAO,CAAAd,KAAK,CAACoB,WAAW,GAAK,UAAU,CAAE,CAC3C;AACA,KAAM,CAAAC,KAAK,CAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,EAAIA,CAAC,EAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,EAAI,CACpB,GAAIA,IAAI,EAAIA,IAAI,CAACtB,IAAI,CAAE,CACrBN,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC,CACzB,CACF,CAAC,CAAC,CACJ,CACF,CAAE,MAAOC,CAAC,CAAE,CACV/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE8F,CAAC,CAAC,CACnC;AACF,CACF,CAEA,IAAI,CAAClC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC,CAC3B,CAAE,MAAO5C,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC;AACA,IAAI,CAACuC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC,CAC3B,CACF,CACF,CAAC,CAED8B,OAAOA,CAAA,CAAG,CACR,GAAI,CACF;AACA,IAAI,CAACjC,OAAO,CAACa,OAAO,CAAC,CAACb,OAAO,CAAEG,KAAK,GAAK,CACvC,GAAI,CACFH,OAAO,CAACa,OAAO,CAACF,MAAM,EAAI,CACxB,GAAIA,MAAM,EAAI,MAAO,CAAAA,MAAM,CAACG,IAAI,GAAK,UAAU,CAAE,CAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC,CACf,CACF,CAAC,CAAC,CACFd,OAAO,CAACkC,KAAK,CAAC,CAAC,CACjB,CAAE,MAAOF,CAAC,CAAE,CACV/F,OAAO,CAACC,GAAG,CAAC,eAAe,CAAE8F,CAAC,CAAC,CACjC,CACF,CAAC,CAAC,CACF,IAAI,CAAChC,OAAO,CAACkC,KAAK,CAAC,CAAC,CAEpB;AACA,IAAI,CAACpC,MAAM,CAACe,OAAO,CAACV,KAAK,EAAI,CAC3B,GAAI,CACF,GAAI,MAAO,CAAAA,KAAK,CAACa,aAAa,GAAK,UAAU,CAAE,CAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC,CACvB,CAEA,KAAM,CAAAC,IAAI,CAAGd,KAAK,CAACe,OAAO,CAAC,CAAC,CAC5B,GAAID,IAAI,CAAE,CACRA,IAAI,CAACX,QAAQ,CAACC,MAAM,EAAI,CACtB,GAAIA,MAAM,EAAIA,MAAM,CAACY,UAAU,CAAE,CAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,CAAG,CAAC,CAC9B,CACF,CAAC,CAAC,CAEF;AACA,GAAI,MAAO,CAAAjB,KAAK,CAACkB,WAAW,GAAK,UAAU,CAAE,CAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC,CACzB,CAEA,GAAI,MAAO,CAAAd,KAAK,CAACmB,aAAa,GAAK,UAAU,CAAE,CAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,CAAEL,IAAI,CAAC,CACjC,CAEA;AACA,GAAI,CACF,GAAId,KAAK,CAACsB,QAAQ,EAAIzE,KAAK,CAACC,OAAO,CAACkD,KAAK,CAACsB,QAAQ,CAAC,CAAE,CACnD,KAAM,CAAAD,KAAK,CAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,EAAIA,CAAC,EAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,EAAI,CACpB,GAAIA,IAAI,EAAIA,IAAI,CAACtB,IAAI,EAAI,MAAO,CAAAN,KAAK,CAACoB,WAAW,GAAK,UAAU,CAAE,CAChEpB,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC,CACzB,CACF,CAAC,CAAC,CACJ,CACF,CAAE,MAAOC,CAAC,CAAE,CACV/F,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAE8F,CAAC,CAAC,CACnC,CACF,CACF,CAAE,MAAOA,CAAC,CAAE,CACV/F,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE8F,CAAC,CAAC,CAClC,CACF,CAAC,CAAC,CACF,IAAI,CAAClC,MAAM,CAACoC,KAAK,CAAC,CAAC,CAEnB;AACA,IAAI,CAACnC,KAAK,CAACc,OAAO,CAACsB,IAAI,EAAI,CACzB,GAAIA,IAAI,CAACC,MAAM,CAAE,CACfD,IAAI,CAACC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAC,CAC1B,CACA,GAAIA,IAAI,CAACG,MAAM,CAAEH,IAAI,CAACG,MAAM,CAACC,QAAQ,CAAC,CAAC,CACvC,GAAIJ,IAAI,CAACK,WAAW,CAAEL,IAAI,CAACK,WAAW,CAACD,QAAQ,CAAC,CAAC,CACnD,CAAC,CAAC,CACF,IAAI,CAACxC,KAAK,CAACmC,KAAK,CAAC,CAAC,CAElB;AACA,IAAI,CAACjC,MAAM,CAACY,OAAO,CAACT,KAAK,EAAI,CAC3B,GAAIA,KAAK,CAACgC,MAAM,CAAE,CAChBhC,KAAK,CAACgC,MAAM,CAACC,MAAM,CAACjC,KAAK,CAAC,CAC5B,CACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,EAAI,CACvB,GAAIA,MAAM,CAACkC,MAAM,CAAE,CACjB,GAAIlC,MAAM,CAACmC,QAAQ,CAAE,CACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAC3B,CACA,GAAIpC,MAAM,CAACqC,QAAQ,CAAE,CACnB,GAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,CAAE,CAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,EAAI,CAClC,GAAIA,QAAQ,CAAClB,GAAG,CAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC,CACpB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,GAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC,CAC3B,CACF,CACF,CACA,GAAIpC,MAAM,CAACY,UAAU,CAAE,CACrBZ,MAAM,CAACY,UAAU,CAACC,MAAM,CAAG,CAAC,CAC9B,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CACF,IAAI,CAACnB,MAAM,CAACiC,KAAK,CAAC,CAAC,CACrB,CAAE,MAAO3E,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC;AACA,IAAI,CAACyC,OAAO,CAACkC,KAAK,CAAC,CAAC,CACpB,IAAI,CAACpC,MAAM,CAACoC,KAAK,CAAC,CAAC,CACnB,IAAI,CAACnC,KAAK,CAACmC,KAAK,CAAC,CAAC,CAClB,IAAI,CAACjC,MAAM,CAACiC,KAAK,CAAC,CAAC,CACrB,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAW,oBAAoB,CAAIzC,KAAK,EAAK,CACtC,KAAM,CAAAD,KAAK,CAAG,GAAI,CAAAxH,KAAK,CAACmK,cAAc,CAAC1C,KAAK,CAAC,CAC7C,MAAO,CAAAP,eAAe,CAACK,QAAQ,CAACC,KAAK,CAAEC,KAAK,CAAC,CAC/C,CAAC,CAED;AACA,KAAM,CAAA2C,YAAY,CAAGA,CAAChB,IAAI,CAAE5B,KAAK,CAAEC,KAAK,GAAK,CAC3C,KAAM,CAAAO,MAAM,CAAGR,KAAK,CAAC6C,UAAU,CAACjB,IAAI,CAAE3B,KAAK,CAAC,CAC5C,MAAO,CAAAP,eAAe,CAACa,SAAS,CAACC,MAAM,CAAER,KAAK,CAAC,CACjD,CAAC,CAED;AACA,KAAM,CAAA8C,mBAAmB,CAAG,GAAI,CAAAtD,GAAG,CAAC,CAAC,CAErC;AACA,GAAI,CAAAuD,cAAc,CAAG,EAAE,CAAE;AACzB,GAAI,CAAAC,cAAc,CAAG,CAAC,CAAE;AACxB,GAAI,CAAAC,YAAY,CAAG,GAAI,CAAAnI,GAAG,CAAC,CAAC,CAAE;AAE9B,KAAM,CAAAoI,WAAW,CAAGC,IAAA,EAAqD,IAApD,CAAEC,SAAS,CAAEC,kBAAkB,CAAEC,YAAa,CAAC,CAAAH,IAAA,CAClE,KAAM,CAAAI,YAAY,CAAGlL,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAmL,UAAU,CAAGnL,MAAM,CAAC,IAAI,CAAC,CAC/B,KAAM,CAAAoL,SAAS,CAAGpL,MAAM,CAAC,GAAI,CAAAM,mBAAmB,CAAC,CAAC,CAAC,CACnD,KAAM,CAAA+K,aAAa,CAAGrL,MAAM,CAAC,EAAE,CAAC,CAChC,KAAM,CAAAsL,eAAe,CAAGtL,MAAM,CAAC,CAAC,CAAC,CACjC,KAAM,CAAAuL,aAAa,CAAGvL,MAAM,CAAC,IAAI,CAAC,CAClC,KAAM,CAAAwL,iBAAiB,CAAGxL,MAAM,CAAC,IAAI,CAAC,CACtC,KAAM,CAAAyL,MAAM,CAAGzL,MAAM,CAAC,IAAI,CAAC,CAE3B;AACA,KAAM,CAAA0L,kBAAkB,CAAG1L,MAAM,CAAC,IAAI,CAAC,CACvC,KAAM,CAAA2L,gBAAgB,CAAG3L,MAAM,CAAC,IAAI,CAAC,CACrC,KAAM,CAAA4L,eAAe,CAAG,IAAI,CAAE;AAE9B;AACA,KAAM,CAAAC,oBAAoB,CAAG7L,MAAM,CAAC8L,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAAC,qBAAqB,CAAG,GAAI,CAAAvJ,GAAG,CAAC,CAAC,CAAE;AACzC,KAAM,CAAAwJ,gBAAgB,CAAGjM,MAAM,CAAC,EAAE,CAAC,CAAE;AAGrC;AACA,KAAM,CAACkM,WAAW,CAAEC,cAAc,CAAC,CAAGlM,QAAQ,CAAC,CAAEmM,OAAO,CAAE,EAAG,CAAC,CAAC,CAE/D;AACA,KAAM,CAAAC,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAAC,YAAY,CAAG,EAAE,CACrB,GAAI,CACF;AACA,KAAM,CAAAC,MAAM,CAAGlJ,QAAQ,CACvB,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAAvD,KAAK,CAACqF,GAAG,CAAC,GAAGsG,MAAM,cAAc,CAAC,CACzD,GAAIpI,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACmI,OAAO,CAAE,CAC1CF,YAAY,CAAGnI,QAAQ,CAACE,IAAI,CAACA,IAAI,CACnC,CAEAZ,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE6I,MAAM,CAAC,CACzC9I,OAAO,CAACC,GAAG,CAAC,cAAc,CAAE4I,YAAY,CAAC,CAC3C,CAAE,MAAOvH,KAAK,CAAE,CACd,GAAI,CACF,KAAM,CAAAZ,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,wBAAwB,CAAC,CACtD,KAAM,CAAAE,IAAI,CAAG,KAAM,CAAAH,QAAQ,CAACG,IAAI,CAAC,CAAC,CAClCgI,YAAY,CAAGhI,IAAI,CAAC8H,OAAO,EAAI,EAAE,CACnC,CAAE,MAAO5C,CAAC,CAAE,CACV/F,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEyE,CAAC,CAAC,CAC9B,CACF,CACA2C,cAAc,CAAC,CAAEC,OAAO,CAAEE,YAAa,CAAC,CAAC,CAC3C,CAAC,CACDvM,SAAS,CAAC,IAAM,CAAEsM,eAAe,CAAC,CAAC,CAAE,CAAC,CAAE,EAAE,CAAC,CAE5C;AACC,KAAM,CAACI,aAAa,CAAEC,gBAAgB,CAAC,CAAGzM,QAAQ,CAAC,EAAE,CAAC,CACtD;AACAF,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4M,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAAJ,MAAM,CAAGjJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAW,QAAQ,CAAG,KAAM,CAAAvD,KAAK,CAACqF,GAAG,CAAC,GAAGsG,MAAM,oBAAoB,CAAC,CAC/D,GAAIpI,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACmI,OAAO,CAAE,CAC1CE,gBAAgB,CAACvI,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAI,EAAE,CAAC,CAC5C,CACF,CAAE,MAAOU,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CACD4H,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,EAAE,CAAC,CAGN;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG5M,QAAQ,CAAC,CAC/C6M,SAAS,CAAE,CAAC,CACZC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,CAAC,CACRC,OAAO,CAAE,CACX,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGlN,QAAQ,CAAC,QAAQ,CAAC,CAElD;AACA,KAAM,CAAAmN,oBAAoB,CAAG,CAC3BC,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7BC,MAAM,CAAE,IAAI,CAAG;AACfC,OAAO,CAAE,MAAM,CACfC,GAAG,CAAE,MACP,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,CAClBC,OAAO,CAAE,UAAU,CACnBC,eAAe,CAAE,0BAA0B,CAC3CC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,2BAA2B,CACtCC,UAAU,CAAE,eACd,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAGrO,MAAM,CAAC,IAAI,CAAC,CAE9B;AACA,KAAM,CAACsO,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtO,QAAQ,CAAC,IAAI,CAAC,CAEtE;AACA,KAAM,CAACuO,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGxO,QAAQ,CAAC,CAC7DyO,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,IAAI,CACbtB,QAAQ,CAAE,CAAE5H,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CACxBiJ,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,EACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,mBAAmB,CAAG9O,MAAM,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAAA+O,0BAA0B,CAAG/O,MAAM,CAAC,IAAI,CAAC,CAE/C;AACA6C,MAAM,CAACmM,uBAAuB,CAAGP,sBAAsB,CAEvD;AACA5L,MAAM,CAACiM,mBAAmB,CAAGA,mBAAmB,CAChDjM,MAAM,CAACkM,0BAA0B,CAAGA,0BAA0B,CAE9D;AACA,KAAM,CAAAE,uBAAuB,CAAG,CAC9B5B,QAAQ,CAAE,OAAO,CACjB6B,GAAG,CAAE,MAAM,CAAG;AACd3B,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7B2B,KAAK,CAAE,OAAO,CAAG;AACjB1B,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,OAAO,CACxBE,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,2BACb,CAAC,CAED;AACA,KAAM,CAAAiB,UAAU,CAAG,CACjB/B,QAAQ,CAAE,OAAO,CACjB6B,GAAG,CAAE,MAAM,CACX3B,IAAI,CAAE,kBAAkB,CAAG;AAC3BC,SAAS,CAAE,mBAAmB,CAC9BK,OAAO,CAAE,OAAO,CAAG;AACnBwB,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,MAAM,CACbpB,QAAQ,CAAE,MAAM,CAChBqB,UAAU,CAAE,MAAM,CAClBC,UAAU,CAAE,2BAA2B,CACvC/B,MAAM,CAAE,IACV,CAAC,CAED;AACA,KAAM,CAAC3J,gBAAgB,CAAC,CAAG7D,QAAQ,CAAC,GAAI,CAAAwC,GAAG,CAAC,CAAC,CAAC,CAE9C;AACA,KAAM,CAACgN,UAAU,CAAEC,aAAa,CAAC,CAAGzP,QAAQ,CAAC,IAAI,CAAC,CAElD;AACA,KAAM,CAAC2D,gBAAgB,CAAE+L,mBAAmB,CAAC,CAAG1P,QAAQ,CAAC,GAAI,CAAAwC,GAAG,CAAC,CAAC,CAAC,CAEnE;AACA,KAAM,CAACmN,WAAW,CAAEC,cAAc,CAAC,CAAG5P,QAAQ,CAAC,CAAE6P,KAAK,CAAE,EAAE,CAAElB,OAAO,CAAE,EAAG,CAAC,CAAC,CAE1E;AACA,KAAM,CAACmB,aAAa,CAAEC,gBAAgB,CAAC,CAAG/P,QAAQ,CAAC,CACjDyO,OAAO,CAAE,KAAK,CACduB,QAAQ,CAAE,IAAI,CACd5C,QAAQ,CAAE,CAAE5H,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CACxBiJ,OAAO,CAAE,IACX,CAAC,CAAC,CAEF;AACA,KAAM,CAAAsB,wBAAwB,CAAGA,CAAA,GAAM,CACrCF,gBAAgB,CAAC,CAAEtB,OAAO,CAAE,KAAK,CAAEuB,QAAQ,CAAE,IAAI,CAAE5C,QAAQ,CAAE,CAAE5H,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAEiJ,OAAO,CAAE,IAAK,CAAC,CAAC,CAC/F,CAAC,CAED;AACA,KAAM,CAAAuB,0BAA0B,CAAIC,MAAM,EAAK,CAC7C,GAAI,CAACA,MAAM,CAAE,MAAO,KAAI,CACxB,mBAAOpP,IAAA,CAACF,oBAAoB,EAACsP,MAAM,CAAEA,MAAO,CAAE,CAAC,CACjD,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIzO,UAAU,GAAK,QAAQ,CAAE,CAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC,CACtB9B,UAAU,CAAG,QAAQ,CAErB;AACA8J,kBAAkB,CAAC4E,OAAO,CAAG,IAAI,CACjC3E,gBAAgB,CAAC2E,OAAO,CAAG,IAAI,CAE/B,GAAIzO,QAAQ,CAAE,CACZA,QAAQ,CAAC0O,OAAO,CAAG,KAAK,CAC1B,CACF,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAI5O,UAAU,GAAK,QAAQ,CAAE,CAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC,CACtB9B,UAAU,CAAG,QAAQ,CAErB;AACA8J,kBAAkB,CAAC4E,OAAO,CAAG,IAAI,CACjC3E,gBAAgB,CAAC2E,OAAO,CAAG,IAAI,CAE/B,GAAIjC,SAAS,CAACiC,OAAO,EAAIzO,QAAQ,CAAE,CACjC;AACA;AACAwM,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACzC,KAAM,CAAA2K,UAAU,CAAGpC,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAAC9H,KAAK,CAAC,CAAC,CACrD,KAAM,CAAAmL,SAAS,CAAGrC,SAAS,CAACiC,OAAO,CAACK,EAAE,CAACpL,KAAK,CAAC,CAAC,CAE9C;AACA,GAAI,CAAAhF,KAAK,CAACqQ,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC,CAAEpL,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,GAAG,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAChCiL,MAAM,CAACvQ,KAAK,CAACwQ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACd7C,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAAC8D,IAAI,CAACV,UAAU,CAAC,CAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC,CAEV;AACA,GAAI,CAAA7Q,KAAK,CAACqQ,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC,CAAEpL,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAC9BiL,MAAM,CAACvQ,KAAK,CAACwQ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACd7C,SAAS,CAACiC,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC,CACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC,CAEV;AACAvP,QAAQ,CAACwP,MAAM,CAACvL,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B,KAAM,CAAAwL,aAAa,CAAGzP,QAAQ,CAACwP,MAAM,CAAC9L,KAAK,CAAC,CAAC,CAE7C;AACA,GAAI,CAAAhF,KAAK,CAACqQ,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC,CAAEpL,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAC9BiL,MAAM,CAACvQ,KAAK,CAACwQ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACdrP,QAAQ,CAACwP,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC,CACnC;AACAjD,SAAS,CAACiC,OAAO,CAACiB,MAAM,CAAC1P,QAAQ,CAACwP,MAAM,CAAC,CACzCxP,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC,CAEV;AACAvP,QAAQ,CAAC0O,OAAO,CAAG,IAAI,CAEvB;AACA1O,QAAQ,CAAC4P,WAAW,CAAG,EAAE,CACzB5P,QAAQ,CAAC6P,WAAW,CAAG,GAAG,CAC1B7P,QAAQ,CAAC8P,aAAa,CAAGhL,IAAI,CAACC,EAAE,CAAG,GAAG,CACtC/E,QAAQ,CAAC+P,aAAa,CAAG,CAAC,CAC1B/P,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CACjB;AACAnD,SAAS,CAACiC,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChCxD,SAAS,CAACiC,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzCrO,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE,CACrBqO,MAAM,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACnBC,KAAK,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAChBC,KAAK,CAAE,IACT,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAIC,KAAK,EAAK,CAC1C,KAAM,CAAAC,YAAY,CAAG3F,aAAa,CAAC9H,IAAI,CAAC0N,CAAC,EAAIA,CAAC,CAACC,IAAI,GAAKH,KAAK,CAAC,CAE9D,GAAIC,YAAY,EAAI/D,SAAS,CAACiC,OAAO,EAAIzO,QAAQ,CAAE,CACjD0M,uBAAuB,CAAC6D,YAAY,CAAC,CAErC;AACA,KAAM,CAAAG,WAAW,CAAGnH,SAAS,CAACkF,OAAO,CAACkC,YAAY,CAChDC,UAAU,CAACL,YAAY,CAACtF,SAAS,CAAC,CAClC2F,UAAU,CAACL,YAAY,CAACrF,QAAQ,CAClC,CAAC,CAEDtJ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE,CACvBgP,IAAI,CAAEN,YAAY,CAACE,IAAI,CACvBK,GAAG,CAAE,CACH7F,SAAS,CAAEsF,YAAY,CAACtF,SAAS,CACjCC,QAAQ,CAAEqF,YAAY,CAACrF,QACzB,CAAC,CACD6F,IAAI,CAAEL,WACR,CAAC,CAAC,CAEF;AACA3Q,UAAU,CAAG,cAAc,CAC3BuL,WAAW,CAAC,cAAc,CAAC,CAE3B;AACAkB,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAACvH,GAAG,CAACyM,WAAW,CAAC9M,CAAC,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC8M,WAAW,CAAC5M,CAAC,CAAC,EAAE,CAAC,CAEvE;AACA9D,QAAQ,CAACwP,MAAM,CAACvL,GAAG,CAACyM,WAAW,CAAC9M,CAAC,CAAE,CAAC,CAAE,CAAC8M,WAAW,CAAC5M,CAAC,CAAC,CAErD;AACA0I,SAAS,CAACiC,OAAO,CAACiB,MAAM,CAAC1P,QAAQ,CAACwP,MAAM,CAAC,CAEzC;AACAxP,QAAQ,CAAC0O,OAAO,CAAG,IAAI,CACvB1O,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CAEjB;AACAnD,SAAS,CAACiC,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChCxD,SAAS,CAACiC,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzCrO,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE,CACzBgP,IAAI,CAAEN,YAAY,CAACE,IAAI,CACvBO,IAAI,CAAExE,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAACyF,OAAO,CAAC,CAAC,CAC1CC,GAAG,CAAElR,QAAQ,CAACwP,MAAM,CAACyB,OAAO,CAAC,CAAC,CAC9BF,IAAI,CAAEL,WACR,CAAC,CAAC,CAEF;AACA,GAAIH,YAAY,CAACY,eAAe,GAAK,KAAK,EAAIZ,YAAY,CAACzD,OAAO,CAAE,CAClElL,OAAO,CAACC,GAAG,CAAC,MAAM0O,YAAY,CAACE,IAAI,iBAAiB,CAAC,CAErD;AACAW,UAAU,CAAC,IAAM,CACf;AACA,GAAI,CAAAtE,OAAO,CAAGyD,YAAY,CAACzD,OAAO,CAElC;AACA,GAAI9L,MAAM,CAACqQ,qBAAqB,CAAE,CAChCrQ,MAAM,CAACqQ,qBAAqB,CAACvE,OAAO,CAAC,CACrClL,OAAO,CAACC,GAAG,CAAC,SAAS0O,YAAY,CAACE,IAAI,SAAS3D,OAAO,YAAY,CAAC,CACrE,CAAC,IAAM,CACLlL,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC,CAChC,CACF,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLtB,OAAO,CAACC,GAAG,CAAC,MAAM0O,YAAY,CAACE,IAAI,sBAAsB,CAAC,CAE1D;AACA,GAAIzP,MAAM,CAACmM,uBAAuB,CAAE,CAClCnM,MAAM,CAACmM,uBAAuB,CAAC,CAC7BN,OAAO,CAAE,KACX,CAAC,CAAC,CACJ,CACF,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAyE,iBAAiB,CAAGA,CAACrD,KAAK,CAAEsD,OAAO,GAAK,KAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,eAAA,CAAAC,eAAA,CAAAC,eAAA,CAC5C,GAAI,CACF,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACb,OAAO,CAAC,CAEnC;AACA,GAAItD,KAAK,GAAKnN,WAAW,CAACO,GAAG,CAAE,KAAAgR,aAAA,CAC7B;AAEA;AACA,KAAM,CAAAC,SAAS,CAAGJ,OAAO,CAACK,GAAG,CAC7B,KAAM,CAAAC,gBAAgB,CAAGN,OAAO,CAACO,EAAE,CAEnC;AACA,KAAM,CAAAC,aAAa,CAAG3Q,gBAAgB,CAACqC,GAAG,CAACkO,SAAS,CAAC,CAErD;AACA,GAAII,aAAa,EAAIF,gBAAgB,CAAGE,aAAa,CAAE,CACrD;AACA;AACA;AACA;AACA;AACA,OACF,CAEA;AACA3Q,gBAAgB,CAACkC,GAAG,CAACqO,SAAS,CAAEE,gBAAgB,CAAC,CAEjD;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAG,YAAY,CAAG,EAAAN,aAAA,CAAAH,OAAO,CAAC1P,IAAI,UAAA6P,aAAA,iBAAZA,aAAA,CAAcM,YAAY,GAAI,EAAE,CACrD,KAAM,CAAAC,KAAK,CAAGV,OAAO,CAAC1P,IAAI,CAACoQ,KAAK,CAEhC;AACA,KAAM,CAAA1I,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAEtB;AACAyI,YAAY,CAACnM,OAAO,CAACqM,WAAW,EAAI,CAClC;AACA;AACA,KAAM,CAAAC,EAAE,CAAIR,SAAS,CAAGO,WAAW,CAACE,SAAS,CAC7C,KAAM,CAAAC,IAAI,CAAGH,WAAW,CAACI,WAAW,CAEpC,GAAGD,IAAI,GAAK,GAAG,EAAEA,IAAI,GAAK,GAAG,EAAEA,IAAI,GAAK,GAAG,CAAC,CAC5C;AACA;AACE;AACA,KAAM,CAAAE,KAAK,CAAG,CACZjI,SAAS,CAAE2F,UAAU,CAACiC,WAAW,CAACM,WAAW,CAAC,CAC9CjI,QAAQ,CAAE0F,UAAU,CAACiC,WAAW,CAACO,UAAU,CAAC,CAC5CjI,KAAK,CAAEyF,UAAU,CAACiC,WAAW,CAACQ,SAAS,CAAC,CACxCjI,OAAO,CAAEwF,UAAU,CAACiC,WAAW,CAACS,WAAW,CAC7C,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGhK,SAAS,CAACkF,OAAO,CAACkC,YAAY,CAACuC,KAAK,CAACjI,SAAS,CAAEiI,KAAK,CAAChI,QAAQ,CAAC,CAEhF;AACA,GAAI,CAAAsI,cAAc,CAClB,OAAQR,IAAI,EACV,IAAK,GAAG,CAAE;AACRQ,cAAc,CAAGvT,qBAAqB,CACtC,MACF,IAAK,GAAG,CAAE;AACRuT,cAAc,CAAGtT,qBAAqB,CACtC,MACF,IAAK,GAAG,CAAE;AACRsT,cAAc,CAAGrT,oBAAoB,CACrC,MACF,QACE,OAAQ;AACZ,CAEA;AACF,GAAI,CAAA4F,KAAK,CAAGjE,aAAa,CAACsC,GAAG,CAAC0O,EAAE,CAAC,CAEjC,GAAI,CAAC/M,KAAK,EAAIyN,cAAc,CAAE,CAC1B;AACA,KAAM,CAAAC,QAAQ,CAAGT,IAAI,GAAK,GAAG,CAAGrU,aAAa,CAAC+E,KAAK,CAACvD,oBAAoB,CAAC,CAAGqT,cAAc,CAAC9P,KAAK,CAAC,CAAC,CAClG;AACA,KAAM,CAAAgQ,MAAM,CAAGV,IAAI,GAAK,GAAG,CAAG,GAAG,CAAG,GAAG,CACvCS,QAAQ,CAACjI,QAAQ,CAACvH,GAAG,CAACsP,QAAQ,CAAC3P,CAAC,CAAE8P,MAAM,CAAE,CAACH,QAAQ,CAACzP,CAAC,CAAC,CACtD2P,QAAQ,CAACE,QAAQ,CAAC7P,CAAC,CAAGgB,IAAI,CAACC,EAAE,CAAGmO,KAAK,CAAC9H,OAAO,CAAGtG,IAAI,CAACC,EAAE,CAAG,GAAG,CAE7D;AACA,GAAIiO,IAAI,GAAK,GAAG,CAAE,CAClB;AACES,QAAQ,CAACG,KAAK,CAAC3P,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAE3B;AACA,KAAM,CAAA6B,KAAK,CAAG0C,oBAAoB,CAACiL,QAAQ,CAAC,CAE5C,GAAInT,eAAe,EAAIA,eAAe,CAACwG,UAAU,EAAIxG,eAAe,CAACwG,UAAU,CAACC,MAAM,CAAG,CAAC,CAAE,CAC1F;AACA,KAAM,CAAAT,MAAM,CAAGoC,YAAY,CAACpI,eAAe,CAACwG,UAAU,CAAC,CAAC,CAAC,CAAEhB,KAAK,CAAE2N,QAAQ,CAAC,CAC3EnN,MAAM,CAACuN,IAAI,CAAC,CAAC,CACf,CAEA;AACA1J,qBAAqB,CAAClG,GAAG,CAAC6O,EAAE,CAAEhN,KAAK,CAAC,CACtC,CAEAzF,KAAK,CAAC2F,GAAG,CAACyN,QAAQ,CAAC,CAEnB3R,aAAa,CAACmC,GAAG,CAAC6O,EAAE,CAAE,CACpB/M,KAAK,CAAE0N,QAAQ,CACfK,UAAU,CAAE5J,GAAG,CACjB8I,IAAI,CAAEA,IACN,CAAC,CAAC,CACN,CAAC,IAAM,IAAIjN,KAAK,CAAE,CACd;AACFA,KAAK,CAACA,KAAK,CAACyF,QAAQ,CAACvH,GAAG,CAACsP,QAAQ,CAAC3P,CAAC,CAAEmC,KAAK,CAACiN,IAAI,GAAK,GAAG,CAAG,GAAG,CAAG,GAAG,CAAE,CAACO,QAAQ,CAACzP,CAAC,CAAC,CACjFiC,KAAK,CAACA,KAAK,CAAC4N,QAAQ,CAAC7P,CAAC,CAAGgB,IAAI,CAACC,EAAE,CAAGmO,KAAK,CAAC9H,OAAO,CAAGtG,IAAI,CAACC,EAAE,CAAG,GAAG,CAChEgB,KAAK,CAAC+N,UAAU,CAAG5J,GAAG,CACtBnE,KAAK,CAACA,KAAK,CAACiK,YAAY,CAAC,CAAC,CAC1BjK,KAAK,CAACA,KAAK,CAACkK,iBAAiB,CAAC,IAAI,CAAC,CACnC,CACF,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAA8D,iBAAiB,CAAG,IAAI,CAC9B,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAA1O,GAAG,CAACqN,YAAY,CAACtL,GAAG,CAAC4M,CAAC,EAAI3B,SAAS,CAAG2B,CAAC,CAAClB,SAAS,CAAC,CAAC,CAE1EjR,aAAa,CAAC0E,OAAO,CAAC,CAAC0N,SAAS,CAAEpB,EAAE,GAAK,CACvC,GAAI5I,GAAG,CAAGgK,SAAS,CAACJ,UAAU,CAAGC,iBAAiB,EAAI,CAACC,UAAU,CAAC9P,GAAG,CAAC4O,EAAE,CAAC,CAAE,CACzE;AACA,GAAIoB,SAAS,CAAClB,IAAI,GAAK,GAAG,EAAI7I,qBAAqB,CAACjG,GAAG,CAAC4O,EAAE,CAAC,CAAE,CAC3D,KAAM,CAAAhN,KAAK,CAAGqE,qBAAqB,CAAC/F,GAAG,CAAC0O,EAAE,CAAC,CAC3C;AACAtN,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC,CAClCqE,qBAAqB,CAACzD,MAAM,CAACoM,EAAE,CAAC,CAClC,CAEA;AACAzS,KAAK,CAAC2H,MAAM,CAACkM,SAAS,CAACnO,KAAK,CAAC,CAC7BjE,aAAa,CAAC4E,MAAM,CAACoM,EAAE,CAAC,CAC1B,CACF,CAAC,CAAC,CACF,OACF,CAEA;AACA,GAAI7E,KAAK,GAAKnN,WAAW,CAACM,GAAG,CAAE,CAC7B;AAEA,KAAM,CAAA+S,OAAO,CAAGjC,OAAO,CAAC1P,IAAI,CAC5B,KAAM,CAAA4R,KAAK,CAAGD,OAAO,CAAClR,KAAK,CAC3B,KAAM,CAAAoR,QAAQ,CAAG,CACfpJ,SAAS,CAAE2F,UAAU,CAACuD,OAAO,CAACG,QAAQ,CAAC,CACvCpJ,QAAQ,CAAE0F,UAAU,CAACuD,OAAO,CAACI,OAAO,CAAC,CACrCpJ,KAAK,CAAEyF,UAAU,CAACuD,OAAO,CAACd,SAAS,CAAC,CACpCjI,OAAO,CAAEwF,UAAU,CAACuD,OAAO,CAACb,WAAW,CACzC,CAAC,CAED;AACA;AAEA;AACAtS,MAAM,CAACwT,WAAW,CAAC,CACjBxB,IAAI,CAAE,iBAAiB,CACvByB,MAAM,CAAE,aACV,CAAC,CAAE,GAAG,CAAC,CAEP;AACAzT,MAAM,CAACwT,WAAW,CAAC,CACjBxB,IAAI,CAAE,KAAK,CACX/P,KAAK,CAAEmR,KAAK,CAAE;AACd5R,IAAI,CAAE,CAAQ;AACZS,KAAK,CAAEmR,KAAK,CACZf,SAAS,CAAEc,OAAO,CAACd,SAAS,CAC5BkB,OAAO,CAAEJ,OAAO,CAACI,OAAO,CACxBD,QAAQ,CAAEH,OAAO,CAACG,QAAQ,CAC1BhB,WAAW,CAAEa,OAAO,CAACb,WACvB,CACF,CAAC,CAAE,GAAG,CAAC,CAEP;AACA,KAAM,CAAAC,QAAQ,CAAGhK,SAAS,CAACkF,OAAO,CAACkC,YAAY,CAAC0D,QAAQ,CAACpJ,SAAS,CAAEoJ,QAAQ,CAACnJ,QAAQ,CAAC,CACtF,KAAM,CAAAwJ,eAAe,CAAG,GAAI,CAAApW,KAAK,CAACoG,OAAO,CAAC6O,QAAQ,CAAC3P,CAAC,CAAE,GAAG,CAAE,CAAC2P,QAAQ,CAACzP,CAAC,CAAC,CACvE,KAAM,CAAA6Q,eAAe,CAAG7P,IAAI,CAACC,EAAE,CAAGsP,QAAQ,CAACjJ,OAAO,CAAGtG,IAAI,CAACC,EAAE,CAAG,GAAG,CAElE;AACA,KAAM,CAAA6P,WAAW,CAAGrR,cAAc,CAACmR,eAAe,CAAEN,KAAK,CAAC,CAC1D,KAAM,CAAAxP,WAAW,CAAGD,cAAc,CAACgQ,eAAe,CAAEP,KAAK,CAAC,CAE1D;AACA,GAAI,CAAAS,UAAU,CAAG/S,aAAa,CAACsC,GAAG,CAACgQ,KAAK,CAAC,CAEzC;AACA,KAAM,CAAApR,aAAa,CAAGoR,KAAK,GAAKpS,gBAAgB,CAEhD,GAAI,CAAC6S,UAAU,EAAI5U,qBAAqB,CAAE,CACxC;AACA,KAAM,CAAA6U,eAAe,CAAG7U,qBAAqB,CAACyD,KAAK,CAAC,CAAC,CAErD;AACA;AACAoR,eAAe,CAACtJ,QAAQ,CAACvH,GAAG,CAAC2Q,WAAW,CAAChR,CAAC,CAAE,CAAC,CAAC,CAAEgR,WAAW,CAAC5Q,CAAC,CAAC,CAC9D8Q,eAAe,CAACnB,QAAQ,CAAC7P,CAAC,CAAGc,WAAW,CAExC;AACAkQ,eAAe,CAAC7O,QAAQ,CAAE8O,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAC3M,MAAM,EAAI2M,KAAK,CAACxM,QAAQ,CAAE,CAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA,KAAM,CAAAyM,WAAW,CAAGD,KAAK,CAACxM,QAAQ,CAAC7E,KAAK,CAAC,CAAC,CAC1CqR,KAAK,CAACxM,QAAQ,CAAGyM,WAAW,CAE5B;AACA,GAAIhS,aAAa,CAAE,CACjBgS,WAAW,CAACvH,KAAK,CAACxJ,GAAG,CAAC,QAAQ,CAAC,CACjC,CAAC,IAAM,CACL+Q,WAAW,CAACvH,KAAK,CAACxJ,GAAG,CAAC,QAAQ,CAAC,CACjC,CACA+Q,WAAW,CAACC,QAAQ,CAAG,GAAI,CAAA3W,KAAK,CAAC4W,KAAK,CAAC,QAAQ,CAAC,CAChDF,WAAW,CAACG,WAAW,CAAG,IAAI,CAC9B;AACAH,WAAW,CAACI,WAAW,CAAG,IAAI,CAChC,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAGC,gBAAgB,CAAC,GAAGxQ,IAAI,CAACyQ,KAAK,CAAClB,QAAQ,CAAClJ,KAAK,CAAC,OAAO,CAAE,CACxEc,eAAe,CAAEjJ,aAAa,CAC5B,CAAEwS,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAAG;AACnC,CAAEkO,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAAG;AACrCqO,SAAS,CAAE,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/C+E,QAAQ,CAAE,EAAE,CACZL,OAAO,CAAE,CACX,CAAC,CAAC,CACFqJ,UAAU,CAAC7J,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE;AAClCoR,UAAU,CAACO,WAAW,CAAG,IAAI,CAAE;AAC/BP,UAAU,CAAC9M,QAAQ,CAACsN,OAAO,CAAG,GAAG,CAAE;AACnCf,eAAe,CAAC9O,GAAG,CAACqP,UAAU,CAAC,CAE/BhV,KAAK,CAAC2F,GAAG,CAAC8O,eAAe,CAAC,CAE1B;AACAhT,aAAa,CAACmC,GAAG,CAACmQ,KAAK,CAAE,CACvBrO,KAAK,CAAE+O,eAAe,CACtBhB,UAAU,CAAE7J,IAAI,CAACC,GAAG,CAAC,CAAC,CACtB8I,IAAI,CAAE,GAAG,CAAE;AACX8C,MAAM,CAAE9S,aAAa,CACrBqS,UAAU,CAAEA,UAAW;AACzB,CAAC,CAAC,CAEF;AAEA;AACA,GAAI,CAAA3W,KAAK,CAACqQ,KAAK,CAAC+F,eAAe,CAACtJ,QAAQ,CAAC,CACtCwD,EAAE,CAAC,CAAElL,CAAC,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACnBmL,MAAM,CAACvQ,KAAK,CAACwQ,MAAM,CAACC,SAAS,CAAC4G,GAAG,CAAC,CAClCxG,KAAK,CAAC,CAAC,CAEV;AACAuF,eAAe,CAAC7O,QAAQ,CAAE8O,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAC3M,MAAM,EAAI2M,KAAK,CAACxM,QAAQ,EAAIwM,KAAK,CAACxM,QAAQ,CAAC4M,WAAW,CAAE,CAChE,GAAI,CAAAzW,KAAK,CAACqQ,KAAK,CAAC,CAAE8G,OAAO,CAAE,GAAI,CAAC,CAAC,CAC9B7G,EAAE,CAAC,CAAE6G,OAAO,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACzB5G,MAAM,CAACvQ,KAAK,CAACwQ,MAAM,CAACC,SAAS,CAAC4G,GAAG,CAAC,CAClC1G,QAAQ,CAAC,UAAW,CACnB0F,KAAK,CAACxM,QAAQ,CAACsN,OAAO,CAAG,IAAI,CAACA,OAAO,CACrCd,KAAK,CAACxM,QAAQ,CAAC6M,WAAW,CAAG,IAAI,CACnC,CAAC,CAAC,CACD7F,KAAK,CAAC,CAAC,CACZ,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAAA7Q,KAAK,CAACqQ,KAAK,CAAC,CAAE8G,OAAO,CAAE,GAAI,CAAC,CAAC,CAC9B7G,EAAE,CAAC,CAAE6G,OAAO,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACzB5G,MAAM,CAACvQ,KAAK,CAACwQ,MAAM,CAACC,SAAS,CAAC4G,GAAG,CAAC,CAClC1G,QAAQ,CAAC,UAAW,CACnBgG,UAAU,CAAC9M,QAAQ,CAACsN,OAAO,CAAG,IAAI,CAACA,OAAO,CAC1CR,UAAU,CAAC9M,QAAQ,CAAC6M,WAAW,CAAG,IAAI,CACxC,CAAC,CAAC,CACD7F,KAAK,CAAC,CAAC,CAEV;AACA,GAAIvM,aAAa,CAAE,CACjBxD,gBAAgB,CAAGsV,eAAe,CAClC9J,eAAe,CAACqJ,QAAQ,CAAC,CACzBzS,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEuS,KAAK,CAAC,CACjC,CACF,CAAC,IAAM,IAAIS,UAAU,CAAE,CACrB;AACA,KAAM,CAAAmB,gBAAgB,CAAGzS,cAAc,CAACqR,WAAW,CAAER,KAAK,CAAC,CAC3D,KAAM,CAAApP,gBAAgB,CAAGL,cAAc,CAACC,WAAW,CAAEwP,KAAK,CAAC,CAE3D;AACAS,UAAU,CAAC9O,KAAK,CAACyF,QAAQ,CAAC8D,IAAI,CAAC0G,gBAAgB,CAAC,CAChDnB,UAAU,CAAC9O,KAAK,CAAC4N,QAAQ,CAAC7P,CAAC,CAAGkB,gBAAgB,CAC9C6P,UAAU,CAAC9O,KAAK,CAACiK,YAAY,CAAC,CAAC,CAC/B6E,UAAU,CAAC9O,KAAK,CAACkK,iBAAiB,CAAC,IAAI,CAAC,CACxC4E,UAAU,CAACf,UAAU,CAAG7J,IAAI,CAACC,GAAG,CAAC,CAAC,CAClC2K,UAAU,CAACiB,MAAM,CAAG9S,aAAa,CAAE;AAEnC;AACA,GAAI6R,UAAU,CAACQ,UAAU,CAAE,CACzBR,UAAU,CAACQ,UAAU,CAAC9M,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CAC5CuM,UAAU,CAAC9O,KAAK,CAACiC,MAAM,CAAC6M,UAAU,CAACQ,UAAU,CAAC,CAChD,CAEA;AACA,KAAM,CAAAA,UAAU,CAAGC,gBAAgB,CAAC,GAAGxQ,IAAI,CAACyQ,KAAK,CAAClB,QAAQ,CAAClJ,KAAK,CAAG,GAAG,CAAC,OAAO,CAAE,CAC9Ec,eAAe,CAAEjJ,aAAa,CAC5B,CAAEwS,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAAG;AACnC,CAAEkO,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAAG;AACrCqO,SAAS,CAAE,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/C+E,QAAQ,CAAE,EAAE,CACZL,OAAO,CAAE,CACX,CAAC,CAAC,CACFqJ,UAAU,CAAC7J,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAAE;AACnCoR,UAAU,CAACO,WAAW,CAAG,IAAI,CAAE;AAC/Bf,UAAU,CAAC9O,KAAK,CAACC,GAAG,CAACqP,UAAU,CAAC,CAChCR,UAAU,CAACQ,UAAU,CAAGA,UAAU,CAElC;AAEA;AACA,GAAIrS,aAAa,CAAE,CACjBxD,gBAAgB,CAAGqV,UAAU,CAAC9O,KAAK,CACnCiF,eAAe,CAACqJ,QAAQ,CAAC,CAC3B,CACF,CAEA;AACA,KAAM,CAAAnK,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CACtB,KAAM,CAAA6J,iBAAiB,CAAG,IAAI,CAAE;AAEhCjS,aAAa,CAAC0E,OAAO,CAAC,CAAC0N,SAAS,CAAEpB,EAAE,GAAK,CACvC,KAAM,CAAAmD,mBAAmB,CAAG/L,GAAG,CAAGgK,SAAS,CAACJ,UAAU,CAEtD;AACA,GAAImC,mBAAmB,CAAGlC,iBAAiB,CAAG,GAAG,EAAIkC,mBAAmB,EAAIlC,iBAAiB,CAAE,CAC7F;AACA,KAAM,CAAA8B,OAAO,CAAG,CAAC,CAEjB3B,SAAS,CAACnO,KAAK,CAACE,QAAQ,CAAE8O,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAC3M,MAAM,EAAI2M,KAAK,CAACxM,QAAQ,CAAE,CAClC;AACA,GAAIwM,KAAK,CAACxM,QAAQ,CAAC4M,WAAW,GAAKe,SAAS,CAAE,CAC5CnB,KAAK,CAACxM,QAAQ,CAAC4N,mBAAmB,CAAGpB,KAAK,CAACxM,QAAQ,CAAC4M,WAAW,EAAI,KAAK,CACxEJ,KAAK,CAACxM,QAAQ,CAAC6N,eAAe,CAAGrB,KAAK,CAACxM,QAAQ,CAACsN,OAAO,EAAI,GAAG,CAChE,CAEA;AACAd,KAAK,CAACxM,QAAQ,CAAC4M,WAAW,CAAG,IAAI,CACjCJ,KAAK,CAACxM,QAAQ,CAACsN,OAAO,CAAGA,OAAO,CAChCd,KAAK,CAACxM,QAAQ,CAAC6M,WAAW,CAAG,IAAI,CACnC,CACF,CAAC,CAAC,CAEF;AACA,GAAIlB,SAAS,CAACmB,UAAU,CAAE,CACxBnB,SAAS,CAACmB,UAAU,CAAC9M,QAAQ,CAACsN,OAAO,CAAGA,OAAO,CAC/C3B,SAAS,CAACmB,UAAU,CAAC9M,QAAQ,CAAC6M,WAAW,CAAG,IAAI,CAClD,CACF,CACA;AAAA,IACK,IAAIa,mBAAmB,CAAGlC,iBAAiB,CAAE,CAChD;AACA,GAAIG,SAAS,CAACmB,UAAU,CAAE,CACxBnB,SAAS,CAACmB,UAAU,CAAC9M,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CAC3C4L,SAAS,CAACmB,UAAU,CAAC9M,QAAQ,CAACD,OAAO,CAAC,CAAC,CACvC4L,SAAS,CAACnO,KAAK,CAACiC,MAAM,CAACkM,SAAS,CAACmB,UAAU,CAAC,CAC9C,CAEAnB,SAAS,CAACnO,KAAK,CAACE,QAAQ,CAAE8O,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAC3M,MAAM,CAAE,CAChB,GAAI2M,KAAK,CAACxM,QAAQ,CAAE,CAClB,GAAI5F,KAAK,CAACC,OAAO,CAACmS,KAAK,CAACxM,QAAQ,CAAC,CAAE,CACjCwM,KAAK,CAACxM,QAAQ,CAAC/B,OAAO,CAAC6P,CAAC,EAAIA,CAAC,CAAC/N,OAAO,CAAC,CAAC,CAAC,CAC1C,CAAC,IAAM,CACLyM,KAAK,CAACxM,QAAQ,CAACD,OAAO,CAAC,CAAC,CAC1B,CACF,CACA,GAAIyM,KAAK,CAAC1M,QAAQ,CAAE0M,KAAK,CAAC1M,QAAQ,CAACC,OAAO,CAAC,CAAC,CAC9C,CACF,CAAC,CAAC,CAEF;AACAjI,KAAK,CAAC2H,MAAM,CAACkM,SAAS,CAACnO,KAAK,CAAC,CAC7BjE,aAAa,CAAC4E,MAAM,CAACoM,EAAE,CAAC,CACxB;AACAnS,oBAAoB,CAAC+F,MAAM,CAACoM,EAAE,CAAC,CAC/BjS,oBAAoB,CAAC6F,MAAM,CAACoM,EAAE,CAAC,CAE/BlR,OAAO,CAACC,GAAG,CAAC,mBAAmBiR,EAAE,EAAE,CAAC,CACtC,CACF,CAAC,CAAC,CAEF,OACF,CAEA;AACA,GAAI7E,KAAK,GAAKnN,WAAW,CAACS,IAAI,CAAE,CAC9B;AAEA,GAAI,CACF,KAAM,CAAA2Q,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACb,OAAO,CAAC,CAEnC;AACA,KAAM,CAAAe,SAAS,CAAGJ,OAAO,CAACK,GAAG,CAC7B,KAAM,CAAAC,gBAAgB,CAAGN,OAAO,CAACO,EAAE,CAEnC;AACA,KAAM,CAAAC,aAAa,CAAG3Q,gBAAgB,CAACqC,GAAG,CAACkO,SAAS,CAAC,CAErD;AACA,GAAII,aAAa,EAAIF,gBAAgB,CAAGE,aAAa,CAAE,CACrD;AACA;AACA;AACA;AACA;AACA,OACF,CAEA;AACA3Q,gBAAgB,CAACkC,GAAG,CAACqO,SAAS,CAAEE,gBAAgB,CAAC,CAEjD;AACA,GAAIN,OAAO,CAAC1P,IAAI,EAAI0P,OAAO,CAAC1P,IAAI,CAACoI,aAAa,EAAIjI,KAAK,CAACC,OAAO,CAACsP,OAAO,CAAC1P,IAAI,CAACoI,aAAa,CAAC,CAAE,CAC3FsH,OAAO,CAAC1P,IAAI,CAACoI,aAAa,CAACpE,OAAO,CAAC+J,YAAY,EAAI,CACjD,KAAM,CAAAzD,OAAO,CAAGyD,YAAY,CAACzD,OAAO,CAEpC,GAAI,CAACA,OAAO,CAAE,CACZlL,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEqN,YAAY,CAAC,CAC/C,OACF,CAEA;AAEA;AACA,GAAIA,YAAY,CAACvD,MAAM,EAAIrK,KAAK,CAACC,OAAO,CAAC2N,YAAY,CAACvD,MAAM,CAAC,CAAE,CAC7D;AACA,KAAM,CAAAsJ,UAAU,CAAG,EAAE,CAErB/F,YAAY,CAACvD,MAAM,CAACxG,OAAO,CAAC+P,KAAK,EAAI,CACnC;AACA,GAAI,CAACA,KAAK,CAACC,OAAO,CAAE,CAClB5U,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAEqT,KAAK,CAAC,CACtC,OACF,CAEA,KAAM,CAAAC,OAAO,CAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC,CACxC;AACA,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACI,YAAY,CAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,CACxCE,iBAAiB,CAACL,OAAO,CAAC,CAE5B;AACA,KAAM,CAAAM,UAAU,CAAGP,KAAK,CAACQ,YAAY,EAAI,GAAG,CAAE;AAC9C,KAAM,CAAAC,UAAU,CAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,EAAI,CAAC,CAElD;AAEA;AACA,KAAM,CAAAE,SAAS,CAAG,CAChBV,OAAO,CACPE,SAAS,CACTK,YAAY,CAAED,UAAU,CACxBE,UACF,CAAC,CAED;AACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC,CAE1B;AACA;AACA,GAAI,CAAAE,eAAe,CAAGC,MAAM,CAACvK,OAAO,CAAC,CACrC,GAAI,CAAAwK,iBAAiB,CAAGrV,gBAAgB,CAACmC,GAAG,CAACgT,eAAe,CAAC,CAE7D,GAAI,CAACE,iBAAiB,CAAE,CACtB;AACAF,eAAe,CAAGH,QAAQ,CAACnK,OAAO,CAAC,CACnCwK,iBAAiB,CAAGrV,gBAAgB,CAACmC,GAAG,CAACgT,eAAe,CAAC,CAC3D,CAEA,GAAIE,iBAAiB,CAAE,CACrB;AACAC,wBAAwB,CAACD,iBAAiB,CAAEJ,SAAS,CAAC,CAEtD;AACA,GAAIzK,oBAAoB,EAAIA,oBAAoB,CAACK,OAAO,GAAKA,OAAO,CAAE,CACpEF,sBAAsB,CAAC4K,IAAI,GAAK,CAC9B,GAAGA,IAAI,CACP3K,OAAO,CAAE,IAAI,CACb2J,OAAO,CACPE,SAAS,CACTxD,KAAK,CAAE4D,UAAU,CACjBE,UACF,CAAC,CAAC,CAAC,CACL,CACF,CAAC,IAAM,CACL;AAAA,CAEJ,CAAC,CAAC,CAEF;AACA,GAAI,CAAAS,QAAQ,CAAG,IAAI,CACnB;AACA,KAAM,CAAAC,KAAK,CAAGL,MAAM,CAACvK,OAAO,CAAC,CAC7B,GAAI7K,gBAAgB,CAACiC,GAAG,CAACwT,KAAK,CAAC,CAAE,CAC/BD,QAAQ,CAAGC,KAAK,CAClB,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,KAAK,CAAGV,QAAQ,CAACnK,OAAO,CAAC,CAC/B,GAAI7K,gBAAgB,CAACiC,GAAG,CAACyT,KAAK,CAAC,CAAE,CAC/BF,QAAQ,CAAGE,KAAK,CAClB,CACF,CAEA,GAAIF,QAAQ,GAAK,IAAI,CAAE,CACrB;AACAvV,kBAAkB,CAAC+B,GAAG,CAACwT,QAAQ,CAAE,CAC/BG,UAAU,CAAE3N,IAAI,CAACC,GAAG,CAAC,CAAC,CACtB8C,MAAM,CAAEsJ,UACV,CAAC,CAAC,CACF1U,OAAO,CAACC,GAAG,CAAC,aAAa4V,QAAQ,KAAK,MAAO,CAAAA,QAAQ,aAAa,CAAC,CAEnE;AACA,GAAIzW,MAAM,CAACiM,mBAAmB,GAC1BjM,MAAM,CAACiM,mBAAmB,CAACwB,OAAO,GAAKgJ,QAAQ,EAC/CzW,MAAM,CAACiM,mBAAmB,CAACwB,OAAO,GAAK4I,MAAM,CAACI,QAAQ,CAAC,EACvDzW,MAAM,CAACiM,mBAAmB,CAACwB,OAAO,GAAKwI,QAAQ,CAACQ,QAAQ,CAAC,CAAC,CAAE,CAE9D7V,OAAO,CAACC,GAAG,CAAC,eAAe4V,QAAQ,aAAa,CAAC,CACjD;AACAzW,MAAM,CAACiM,mBAAmB,CAACwB,OAAO,CAAGgJ,QAAQ,CAE7C;AACA,GAAIzW,MAAM,CAACkM,0BAA0B,EAAI,CAAClM,MAAM,CAACkM,0BAA0B,CAACuB,OAAO,CAAE,CACnF7M,OAAO,CAACC,GAAG,CAAC,SAAS4V,QAAQ,aAAa,CAAC,CAC3CrG,UAAU,CAAC,IAAM,CACfpQ,MAAM,CAACqQ,qBAAqB,CAACoG,QAAQ,CAAC,CACxC,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CACF,CAAC,IAAM,CACL;AACAvV,kBAAkB,CAAC+B,GAAG,CAAC6I,OAAO,CAAE,CAC9B8K,UAAU,CAAE3N,IAAI,CAACC,GAAG,CAAC,CAAC,CACtB8C,MAAM,CAAEsJ,UACV,CAAC,CAAC,CACF;AACF,CACF,CAAC,IAAM,CACL1U,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAEqN,YAAY,CAAC,CAC9C,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACL3O,OAAO,CAACsB,KAAK,CAAC,oCAAoC,CAAEgP,OAAO,CAAC,CAC9D,CACF,CAAE,MAAOhP,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAEqO,OAAO,CAAC,CAC9C,CACA,OACF,CAEA;AACA,GAAItD,KAAK,GAAKnN,WAAW,CAACQ,GAAG,EAAI4Q,OAAO,CAACc,IAAI,GAAK,KAAK,CAAE,CACvD;AAEA;AACAhS,MAAM,CAACwT,WAAW,CAAC,CACjBxB,IAAI,CAAE,KAAK,CACXxQ,IAAI,CAAE0P,OAAO,CAAC1P,IAAI,CAClB+P,GAAG,CAAEL,OAAO,CAACK,GAAG,CAChBE,EAAE,CAAEP,OAAO,CAACO,EACd,CAAC,CAAE,GAAG,CAAC,CAEP,KAAM,CAAAoF,OAAO,CAAG3F,OAAO,CAAC1P,IAAI,CAC5B,KAAM,CAAAsV,KAAK,CAAGD,OAAO,CAACC,KAAK,CAC3B,KAAM,CAAAC,MAAM,CAAGF,OAAO,CAACG,IAAI,EAAI,EAAE,CAEjCD,MAAM,CAACvR,OAAO,CAACyR,KAAK,EAAI,KAAAC,cAAA,CACtB,KAAM,CAAAC,OAAO,CAAGF,KAAK,CAACG,KAAK,CAC3B,KAAM,CAAAC,SAAS,CAAGJ,KAAK,CAACI,SAAS,CACjC,KAAM,CAAAC,WAAW,CAAGL,KAAK,CAACK,WAAW,CACrC,KAAM,CAAAC,SAAS,CAAGN,KAAK,CAACM,SAAS,CACjC,KAAM,CAAAC,OAAO,CAAGP,KAAK,CAACO,OAAO,CAE7B;AACA,KAAM,CAAAjF,QAAQ,CAAGhK,SAAS,CAACkF,OAAO,CAACkC,YAAY,CAC7CC,UAAU,CAACiH,OAAO,CAACY,OAAO,CAAC,CAC3B7H,UAAU,CAACiH,OAAO,CAACa,MAAM,CAC3B,CAAC,CAED;AACA,GAAI,CAAAC,WAAW,CAAG,EAAE,CACpB,GAAI,CAAAC,YAAY,CAAG,EAAE,CAErB,OAAOP,SAAS,EACd,IAAK,KAAK,CAAG;AACXM,WAAW,CAAG,OAAO,CACrBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,OAAO,CACrBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,QAAQ,CACtBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,MAAM,CAAE;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,QACED,WAAW,CAAGL,WAAW,EAAI,MAAM,CACnCM,YAAY,CAAG,SAAS,CAC5B,CAEA;AACAC,iBAAiB,CAACtF,QAAQ,CAAEoF,WAAW,CAAEC,YAAY,CAAEP,SAAS,CAAE,CAChEP,KAAK,CAAE,EAAAI,cAAA,CAAAhG,OAAO,CAAC1P,IAAI,UAAA0V,cAAA,iBAAZA,cAAA,CAAcJ,KAAK,GAAI,SAAS,CACvCK,OAAO,CAAEA,OAAO,CAChBG,WAAW,CAAEA,WACf,CAAC,CAAC,CAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAC,CAAC,CAEF,OACF,CAEA;AACA,GAAIrK,KAAK,GAAKnN,WAAW,CAACT,KAAK,EAAI6R,OAAO,CAACc,IAAI,GAAK,OAAO,CAAE,CAC3D;AAEA,KAAM,CAAA8F,SAAS,CAAG5G,OAAO,CAAC1P,IAAI,CAC9B,KAAM,CAAAuW,OAAO,CAAGD,SAAS,CAACC,OAAO,CACjC,KAAM,CAAAC,SAAS,CAAGF,SAAS,CAACE,SAAS,CACrC,KAAM,CAAAC,SAAS,CAAGH,SAAS,CAACG,SAAS,CACrC,KAAM,CAAAzN,QAAQ,CAAG,CACfN,QAAQ,CAAE0F,UAAU,CAACkI,SAAS,CAACvE,OAAO,CAAC,CACvCtJ,SAAS,CAAE2F,UAAU,CAACkI,SAAS,CAACxE,QAAQ,CAC1C,CAAC,CAED;AACA,KAAM,CAAAf,QAAQ,CAAGhK,SAAS,CAACkF,OAAO,CAACkC,YAAY,CAACnF,QAAQ,CAACP,SAAS,CAAEO,QAAQ,CAACN,QAAQ,CAAC,CAEtF;AACA,OAAO8N,SAAS,EACd,IAAK,GAAG,CAAG;AACTH,iBAAiB,CAACtF,QAAQ,CAAE,UAAU,CAAE,SAAS,CAAE,GAAG,CAAE,CAAEuE,KAAK,EAAAtG,cAAA,CAAEU,OAAO,CAAC1P,IAAI,UAAAgP,cAAA,iBAAZA,cAAA,CAAcsG,KAAK,CAAEgB,SAAU,CAAC,CAAC,CAClG,MACF,IAAK,KAAK,CAAG;AACXD,iBAAiB,CAACtF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAE,MAAM,CAAE,CAAEuE,KAAK,EAAArG,cAAA,CAAES,OAAO,CAAC1P,IAAI,UAAAiP,cAAA,iBAAZA,cAAA,CAAcqG,KAAK,CAAEgB,SAAU,CAAC,CAAC,CACjG,MACF,IAAK,KAAK,CAAG;AACXD,iBAAiB,CAACtF,QAAQ,CAAE,OAAO,CAAE,SAAS,CAAE,KAAK,CAAE,CAAEuE,KAAK,EAAApG,cAAA,CAAEQ,OAAO,CAAC1P,IAAI,UAAAkP,cAAA,iBAAZA,cAAA,CAAcoG,KAAK,CAAEgB,SAAU,CAAC,CAAC,CACjG,MACF,IAAK,IAAI,CAAG;AACV,KAAM,CAAAI,UAAU,CAAGJ,SAAS,CAACK,UAAU,CAAG;AAC1CN,iBAAiB,CAACtF,QAAQ,CAAE,KAAK2F,UAAU,MAAM,CAAE,SAAS,CAAE,IAAI,CAAE,CAAEpB,KAAK,EAAAnG,cAAA,CAAEO,OAAO,CAAC1P,IAAI,UAAAmP,cAAA,iBAAZA,cAAA,CAAcmG,KAAK,CAAEgB,SAAU,CAAC,CAAC,CAC9G,MACF,IAAK,IAAI,CAAG;AACVD,iBAAiB,CAACtF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAE,IAAI,CAAE,CAAEuE,KAAK,EAAAlG,cAAA,CAAEM,OAAO,CAAC1P,IAAI,UAAAoP,cAAA,iBAAZA,cAAA,CAAckG,KAAK,CAAEgB,SAAU,CAAC,CAAC,CAC/F,MACF,IAAK,IAAI,CAAG;AACVD,iBAAiB,CAACtF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAE,IAAI,CAAE,CAAEuE,KAAK,EAAAjG,cAAA,CAAEK,OAAO,CAAC1P,IAAI,UAAAqP,cAAA,iBAAZA,cAAA,CAAciG,KAAK,CAAEgB,SAAU,CAAC,CAAC,CAC/F,MACF,IAAK,MAAM,CAAG;AACZD,iBAAiB,CAACtF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAE,MAAM,CAAE,CAAEuE,KAAK,EAAAhG,cAAA,CAAEI,OAAO,CAAC1P,IAAI,UAAAsP,cAAA,iBAAZA,cAAA,CAAcgG,KAAK,CAAEgB,SAAU,CAAC,CAAC,CACjG,MACF,IAAK,IAAI,CAAG;AACVD,iBAAiB,CAACtF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAE,KAAK,CAAE,CAAEuE,KAAK,EAAA/F,eAAA,CAAEG,OAAO,CAAC1P,IAAI,UAAAuP,eAAA,iBAAZA,eAAA,CAAc+F,KAAK,CAAEgB,SAAU,CAAC,CAAC,CAChG,MACF,IAAK,IAAI,CAAG;AACVD,iBAAiB,CAACtF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAE,KAAK,CAAE,CAAEuE,KAAK,EAAA9F,eAAA,CAAEE,OAAO,CAAC1P,IAAI,UAAAwP,eAAA,iBAAZA,eAAA,CAAc8F,KAAK,CAAEgB,SAAU,CAAC,CAAC,CAChG,MACF,IAAK,KAAK,CAAG;AACX,KAAM,CAAAM,YAAY,CAAGN,SAAS,CAACK,UAAU,CAAG;AAC5C,KAAM,CAAAE,QAAQ,CAAGP,SAAS,CAACQ,UAAU,CAAO;AAC5CT,iBAAiB,CAACtF,QAAQ,CAAE,QAAQgG,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,CAAE,SAAS,CAAE,KAAK,CAAE,CAAEvB,KAAK,EAAA7F,eAAA,CAAEC,OAAO,CAAC1P,IAAI,UAAAyP,eAAA,iBAAZA,eAAA,CAAc6F,KAAK,CAAEgB,SAAU,CAAC,CAAC,CACjJ,MACJ,CAEA,OACF,CACA;AACA;AACA;AACA;AACA;AACA;AAEF,CAAE,MAAO5V,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEqO,OAAO,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAAiI,cAAc,CAAGA,CAAA,GAAM,CAC3B5X,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAE7B,KAAM,CAAA4X,KAAK,CAAG,QAAQ3Y,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO,CACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE4X,KAAK,CAAC,CAEpC;AACA,KAAM,CAAAC,EAAE,CAAG,GAAI,CAAAC,SAAS,CAACF,KAAK,CAAC,CAE/BC,EAAE,CAACE,MAAM,CAAG,IAAM,CAChBhY,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAAC,CAED6X,EAAE,CAACG,SAAS,CAAI5B,KAAK,EAAK,CACxB,GAAI,CACF,KAAM,CAAA1G,OAAO,CAAGY,IAAI,CAACC,KAAK,CAAC6F,KAAK,CAACzV,IAAI,CAAC,CAEtC;AACA,GAAI+O,OAAO,CAACyB,IAAI,GAAK,SAAS,CAAE,CAC9BpR,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE0P,OAAO,CAAC,CAC/B,OACF,CAEA;AACA,GAAIA,OAAO,CAACyB,IAAI,GAAK,MAAM,CAAE,CAC3B,OACF,CAEA;AACA,GAAIzB,OAAO,CAACyB,IAAI,GAAK,SAAS,EAAIzB,OAAO,CAACtD,KAAK,EAAIsD,OAAO,CAACW,OAAO,CAAE,CAClE;AACA,GAAIX,OAAO,CAACuI,SAAS,CAAE,CACrB,GAAIlR,mBAAmB,CAAC1E,GAAG,CAACqN,OAAO,CAACuI,SAAS,CAAC,CAAE,CAC9C;AACA,OACF,CAEA;AACAlR,mBAAmB,CAAC5C,GAAG,CAACuL,OAAO,CAACuI,SAAS,CAAC,CAE1C;AACA,GAAIlR,mBAAmB,CAACmR,IAAI,CAAG,IAAI,CAAE,CACnC;AACA,KAAM,CAAAC,QAAQ,CAAGrX,KAAK,CAACsX,IAAI,CAACrR,mBAAmB,CAAC,CAChD,IAAK,GAAI,CAAA4H,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,GAAG,CAAEA,CAAC,EAAE,CAAE,CAC5B5H,mBAAmB,CAAClC,MAAM,CAACsT,QAAQ,CAACxJ,CAAC,CAAC,CAAC,CACzC,CACF,CACF,CAEA;AACAc,iBAAiB,CAACC,OAAO,CAACtD,KAAK,CAAEkE,IAAI,CAAC+H,SAAS,CAAC3I,OAAO,CAACW,OAAO,CAAC,CAAC,CACnE,CACF,CAAE,MAAOhP,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAC1C,CACF,CAAC,CAEDwW,EAAE,CAACS,OAAO,CAAIjX,KAAK,EAAK,CACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CAAC,CAEDwW,EAAE,CAACU,OAAO,CAAG,IAAM,CACjBxY,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC5B;AACAuP,UAAU,CAACoI,cAAc,CAAE,IAAI,CAAC,CAClC,CAAC,CAED;AACA9P,aAAa,CAAC+E,OAAO,CAAGiL,EAAE,CAC5B,CAAC,CAEDxb,SAAS,CAAC,IAAM,CACd,GAAI,CAACmL,YAAY,CAACoF,OAAO,CAAE,OAE3B;AACA4L,aAAa,CAAC,CAAC,CAEf;AACAha,KAAK,CAAG,GAAI,CAAA/B,KAAK,CAACgc,KAAK,CAAC,CAAC,CAAE;AAE3B;AACA,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAjc,KAAK,CAACkc,iBAAiB,CACxC,EAAE,CACFxZ,MAAM,CAACyZ,UAAU,CAAGzZ,MAAM,CAAC0Z,WAAW,CACtC,GAAG,CACH,IACF,CAAC,CACD;AACAH,MAAM,CAAC/O,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAAE;AAChCsW,MAAM,CAAC7K,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtBlD,SAAS,CAACiC,OAAO,CAAG8L,MAAM,CAE1B;AACA,KAAM,CAAAI,QAAQ,CAAG,GAAI,CAAArc,KAAK,CAACsc,aAAa,CAAC,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,CAC7DF,QAAQ,CAACG,OAAO,CAAC9Z,MAAM,CAACyZ,UAAU,CAAEzZ,MAAM,CAAC0Z,WAAW,CAAC,CACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC,CAChCJ,QAAQ,CAACK,aAAa,CAACha,MAAM,CAACia,gBAAgB,CAAC,CAC/C5R,YAAY,CAACoF,OAAO,CAACyM,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC,CAErD;AACA;AACA,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAA9c,KAAK,CAAC+c,YAAY,CAAC,QAAQ,CAAE,GAAG,CAAC,CAAE;AAC5Dhb,KAAK,CAAC2F,GAAG,CAACoV,YAAY,CAAC,CAEvB;AACA,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAAhd,KAAK,CAACid,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CAAE;AACrED,iBAAiB,CAAC9P,QAAQ,CAACvH,GAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAC1C5D,KAAK,CAAC2F,GAAG,CAACsV,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAAld,KAAK,CAACid,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CACnEC,iBAAiB,CAAChQ,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAAE,CAAC,CAAE,CAAC,EAAE,CAAC,CAC3C5D,KAAK,CAAC2F,GAAG,CAACwV,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAnd,KAAK,CAACod,SAAS,CAAC,QAAQ,CAAE,GAAG,CAAC,CACpDD,SAAS,CAACjQ,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAChCwX,SAAS,CAACE,KAAK,CAAG7W,IAAI,CAACC,EAAE,CAAG,CAAC,CAC7B0W,SAAS,CAACG,QAAQ,CAAG,GAAG,CACxBH,SAAS,CAACI,KAAK,CAAG,CAAC,CACnBJ,SAAS,CAACpX,QAAQ,CAAG,GAAG,CACxBhE,KAAK,CAAC2F,GAAG,CAACyV,SAAS,CAAC,CAEpB;AACAzb,QAAQ,CAAG,GAAI,CAAAxB,aAAa,CAAC+b,MAAM,CAAEI,QAAQ,CAACQ,UAAU,CAAC,CACzDnb,QAAQ,CAAC8b,aAAa,CAAG,IAAI,CAC7B9b,QAAQ,CAAC+b,aAAa,CAAG,IAAI,CAC7B/b,QAAQ,CAACgc,kBAAkB,CAAG,KAAK,CACnChc,QAAQ,CAAC4P,WAAW,CAAG,EAAE,CACzB5P,QAAQ,CAAC6P,WAAW,CAAG,GAAG,CAC1B7P,QAAQ,CAAC8P,aAAa,CAAGhL,IAAI,CAACC,EAAE,CAAG,GAAG,CACtC/E,QAAQ,CAAC+P,aAAa,CAAG,CAAC,CAC1B/P,QAAQ,CAACwP,MAAM,CAACvL,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CAEjB;AACA/N,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE,CACnB0Y,MAAM,CAAE,CAAC,CAACA,MAAM,CAChBva,QAAQ,CAAE,CAAC,CAACA,QAAQ,CACpBwM,SAAS,CAAE,CAAC,CAACA,SAAS,CAACiC,OACzB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAwN,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,MAAO,IAAI,CAAAC,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAC,aAAa,CAAG,GAAI,CAAA9d,UAAU,CAAC,CAAC,CACtC8d,aAAa,CAACC,IAAI,CAChB,GAAG9a,QAAQ,uBAAuB,CACjC+a,IAAI,EAAK,CACR,KAAM,CAAAC,YAAY,CAAGD,IAAI,CAAClc,KAAK,CAE/B;AACA,KAAM,CAAAoc,gBAAgB,CAAG,GAAI,CAAAne,KAAK,CAACoe,KAAK,CAAC,CAAC,CAE1C;AACAF,YAAY,CAACvW,QAAQ,CAAE8O,KAAK,EAAK,CAC/B,GAAIA,KAAK,CAAC3M,MAAM,CAAE,CAChB;AACA,GAAI2M,KAAK,CAACxM,QAAQ,CAAE,CAClB;AACA,KAAM,CAAAyM,WAAW,CAAG,GAAI,CAAA1W,KAAK,CAACqe,oBAAoB,CAAC,CACjDlP,KAAK,CAAE,QAAQ,CAAO;AACtBmP,SAAS,CAAE,GAAG,CAAQ;AACtBC,SAAS,CAAE,GAAG,CAAQ;AACtBC,eAAe,CAAE,GAAK;AACxB,CAAC,CAAC,CAEF;AACA,GAAI/H,KAAK,CAACxM,QAAQ,CAAClB,GAAG,CAAE,CACtB2N,WAAW,CAAC3N,GAAG,CAAG0N,KAAK,CAACxM,QAAQ,CAAClB,GAAG,CACtC,CAEA;AACA0N,KAAK,CAACxM,QAAQ,CAAGyM,WAAW,CAE5BpT,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEkT,KAAK,CAACtE,IAAI,CAAC,CACrC,CACF,CACF,CAAC,CAAC,CAEF;AACA,MAAM+L,YAAY,CAACO,QAAQ,CAAChW,MAAM,CAAG,CAAC,CAAE,CACtC,KAAM,CAAAgO,KAAK,CAAGyH,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC,CACtCN,gBAAgB,CAACzW,GAAG,CAAC+O,KAAK,CAAC,CAC7B,CAEA;AACA1U,KAAK,CAAC2F,GAAG,CAACyW,gBAAgB,CAAC,CAE3B;AACAjd,gBAAgB,CAAGid,gBAAgB,CAEnC7a,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAC9Bmb,kBAAkB,CAAC,IAAI,CAAC,CACxBb,OAAO,CAACM,gBAAgB,CAAC,CAC3B,CAAC,CACAQ,GAAG,EAAK,CACPrb,OAAO,CAACC,GAAG,CAAC,aAAa,CAACob,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAE3Y,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACxE,CAAC,CACD4X,MACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAgB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF;AACA;AAEA;AACA5D,cAAc,CAAC,CAAC,CAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEF,CAAE,MAAOtW,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAAma,kBAAkB,CAAG,QAAAA,CAACC,GAAG,CAAqB,IAAnB,CAAAC,UAAU,CAAAC,SAAA,CAAAzW,MAAA,IAAAyW,SAAA,MAAAtH,SAAA,CAAAsH,SAAA,IAAG,CAAC,CAC7C,MAAO,IAAI,CAAAtB,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAqB,WAAW,CAAIC,WAAW,EAAK,CACnC9b,OAAO,CAACC,GAAG,CAAC,WAAWyb,GAAG,aAAaI,WAAW,EAAE,CAAC,CAErD,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAApf,UAAU,CAAC,CAAC,CAC/Bof,MAAM,CAACrB,IAAI,CACTgB,GAAG,CACFf,IAAI,EAAK,CACR3a,OAAO,CAACC,GAAG,CAAC,WAAWyb,GAAG,EAAE,CAAC,CAC7BnB,OAAO,CAACI,IAAI,CAAC,CACf,CAAC,CACAU,GAAG,EAAK,CACPrb,OAAO,CAACC,GAAG,CAAC,SAAS,CAACob,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAE3Y,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACpE,CAAC,CACAtB,KAAK,EAAK,CACTtB,OAAO,CAACsB,KAAK,CAAC,SAASoa,GAAG,EAAE,CAAEpa,KAAK,CAAC,CACpC,GAAIwa,WAAW,CAAG,CAAC,CAAE,CACnB9b,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3BuP,UAAU,CAAC,IAAMqM,WAAW,CAACC,WAAW,CAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACtD,CAAC,IAAM,CACLtB,MAAM,CAAClZ,KAAK,CAAC,CACf,CACF,CACF,CAAC,CACH,CAAC,CAEDua,WAAW,CAACF,UAAU,CAAC,CACzB,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAApf,UAAU,CAAC,CAAC,CAC/Bof,MAAM,CAACrB,IAAI,CACT,GAAG9a,QAAQ,4BAA4B,CACvC,KAAO,CAAA+a,IAAI,EAAK,CACd,GAAI,CACF,KAAM,CAAAxW,KAAK,CAAGwW,IAAI,CAAClc,KAAK,CACxB0F,KAAK,CAAC6N,KAAK,CAAC3P,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxB8B,KAAK,CAACyF,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAE3B;AACA,GAAI5D,KAAK,CAAE,CACXA,KAAK,CAAC2F,GAAG,CAACD,KAAK,CAAC,CAEhB;AACA,KAAM,CAAAqX,eAAe,CAAC,CAAC,CACvB,CAAC,IAAM,CACLxb,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC,CAChC,CACF,CAAE,MAAOA,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CACA+Z,GAAG,EAAK,CACPrb,OAAO,CAACC,GAAG,CAAC,SAAS,CAACob,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAE3Y,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACpE,CAAC,CACAtB,KAAK,EAAK,CACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,CAAE,CACrB0a,IAAI,CAAE1a,KAAK,CAAC8P,IAAI,CAChB6K,IAAI,CAAE3a,KAAK,CAACqO,OAAO,CACnBuM,KAAK,CAAE,GAAGtc,QAAQ,4BAA4B,CAC9Cuc,KAAK,CAAE,GAAGvc,QAAQ,4BACpB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAwc,OAAO,CAAGA,CAAA,GAAM,CACpBrU,iBAAiB,CAAC8E,OAAO,CAAGwP,qBAAqB,CAACD,OAAO,CAAC,CAE1D;AACAtf,KAAK,CAACiR,MAAM,CAAC,CAAC,CAEd;AACA,KAAM,CAAAuO,SAAS,CAAG/b,KAAK,CAACgc,QAAQ,CAAC,CAAC,CAElC;AACA,GAAIhU,qBAAqB,CAAC4P,IAAI,CAAG,CAAC,CAAE,CAClC5P,qBAAqB,CAAC3D,OAAO,CAAEV,KAAK,EAAK,CACvCA,KAAK,CAAC6J,MAAM,CAACuO,SAAS,CAAC,CACzB,CAAC,CAAC,CACJ,CAEA,GAAIne,UAAU,GAAK,QAAQ,EAAIP,gBAAgB,CAAE,CAC/C;AACAQ,QAAQ,CAAC0O,OAAO,CAAG,KAAK,CAExB;AACA,KAAM,CAAA0P,UAAU,CAAG5e,gBAAgB,CAACgM,QAAQ,CAAC9H,KAAK,CAAC,CAAC,CAEpD;AACA,KAAM,CAAA2a,eAAe,CAAG7e,gBAAgB,CAACmU,QAAQ,CAAC7P,CAAC,CAEnD;AACA;AACA,KAAM,CAAAwa,gBAAgB,CAAG,EAAED,eAAe,CAAGvZ,IAAI,CAACC,EAAE,CAAC,CAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC,CAEnE;AACA,KAAM,CAAAwZ,YAAY,CAAG,GAAI,CAAAjgB,KAAK,CAACoG,OAAO,CACpC,CAAC,EAAE,CAAGI,IAAI,CAAC0Z,GAAG,CAACF,gBAAgB,CAAC,CAChC,GAAG,CACH,CAAC,EAAE,CAAGxZ,IAAI,CAAC2Z,GAAG,CAACH,gBAAgB,CACjC,CAAC,CAED;AACA,KAAM,CAAAI,oBAAoB,CAAGN,UAAU,CAAC1a,KAAK,CAAC,CAAC,CAACsC,GAAG,CAACuY,YAAY,CAAC,CACjE,KAAM,CAAAI,YAAY,CAAGP,UAAU,CAAC1a,KAAK,CAAC,CAAC,CAEvC;AACA,GAAI,CAACmG,kBAAkB,CAAC4E,OAAO,CAAE,CAC/B5E,kBAAkB,CAAC4E,OAAO,CAAGiQ,oBAAoB,CAAChb,KAAK,CAAC,CAAC,CAC3D,CAEA,GAAI,CAACoG,gBAAgB,CAAC2E,OAAO,CAAE,CAC7B3E,gBAAgB,CAAC2E,OAAO,CAAGkQ,YAAY,CAACjb,KAAK,CAAC,CAAC,CACjD,CAEA;AACAmG,kBAAkB,CAAC4E,OAAO,CAACmQ,IAAI,CAACF,oBAAoB,CAAE,CAAC,CAAG3U,eAAe,CAAC,CAC1ED,gBAAgB,CAAC2E,OAAO,CAACmQ,IAAI,CAACD,YAAY,CAAE,CAAC,CAAG5U,eAAe,CAAC,CAEhE;AACAwQ,MAAM,CAAC/O,QAAQ,CAAC8D,IAAI,CAACzF,kBAAkB,CAAC4E,OAAO,CAAC,CAEhD;AACA8L,MAAM,CAACzL,EAAE,CAAC7K,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACAsW,MAAM,CAAC7K,MAAM,CAAC5F,gBAAgB,CAAC2E,OAAO,CAAC,CAEvC;AACA8L,MAAM,CAACsE,sBAAsB,CAAC,CAAC,CAC/BtE,MAAM,CAACvK,YAAY,CAAC,CAAC,CACrBuK,MAAM,CAACtK,iBAAiB,CAAC,IAAI,CAAC,CAE9B;AACAjQ,QAAQ,CAAC0O,OAAO,CAAG,KAAK,CAExB;AACA1O,QAAQ,CAACwP,MAAM,CAACF,IAAI,CAACxF,gBAAgB,CAAC2E,OAAO,CAAC,CAC9CzO,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CAEjB/N,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE,CACnBid,IAAI,CAAEV,UAAU,CAACnN,OAAO,CAAC,CAAC,CAC1BD,IAAI,CAAEuJ,MAAM,CAAC/O,QAAQ,CAACyF,OAAO,CAAC,CAAC,CAC/B8N,IAAI,CAAEjV,gBAAgB,CAAC2E,OAAO,CAACwC,OAAO,CAAC,CAAC,CACxC+N,IAAI,CAAEzE,MAAM,CAAC0E,iBAAiB,CAAC,GAAI,CAAA3gB,KAAK,CAACoG,OAAO,CAAC,CAAC,CAAC,CAACuM,OAAO,CAAC,CAC9D,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIlR,UAAU,GAAK,QAAQ,CAAE,CAClC;AACA8J,kBAAkB,CAAC4E,OAAO,CAAG,IAAI,CACjC3E,gBAAgB,CAAC2E,OAAO,CAAG,IAAI,CAE/B;AACAzO,QAAQ,CAAC0O,OAAO,CAAG,IAAI,CAEvB;AACA6L,MAAM,CAACzL,EAAE,CAAC7K,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACA,GAAIa,IAAI,CAACK,GAAG,CAACoV,MAAM,CAAC/O,QAAQ,CAAC1H,CAAC,CAAC,CAAG,EAAE,CAAE,CACpCyW,MAAM,CAAC/O,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAC9BjE,QAAQ,CAACwP,MAAM,CAACvL,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BsW,MAAM,CAAC7K,MAAM,CAAC1P,QAAQ,CAACwP,MAAM,CAAC,CAC9BxP,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CACnB,CAEA;AACA;AACA4K,MAAM,CAACvK,YAAY,CAAC,CAAC,CACrBuK,MAAM,CAACtK,iBAAiB,CAAC,IAAI,CAAC,CAEhC,CAAC,IAAM,IAAIlQ,UAAU,GAAK,cAAc,CAAE,CACxC;AACA8J,kBAAkB,CAAC4E,OAAO,CAAG,IAAI,CACjC3E,gBAAgB,CAAC2E,OAAO,CAAG,IAAI,CAE/B;AACAzO,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CACnB,CAEA,GAAI3P,QAAQ,CAAEA,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CAC/B,GAAItP,KAAK,EAAIka,MAAM,CAAE,CACnBI,QAAQ,CAACuE,MAAM,CAAC7e,KAAK,CAAEka,MAAM,CAAC,CAChC,CACF,CAAC,CAEDyD,OAAO,CAAC,CAAC,CAET;AACA,KAAM,CAAAmB,YAAY,CAAGA,CAAA,GAAM,CACzB5E,MAAM,CAAC6E,MAAM,CAAGpe,MAAM,CAACyZ,UAAU,CAAGzZ,MAAM,CAAC0Z,WAAW,CACtDH,MAAM,CAACsE,sBAAsB,CAAC,CAAC,CAC/BlE,QAAQ,CAACG,OAAO,CAAC9Z,MAAM,CAACyZ,UAAU,CAAEzZ,MAAM,CAAC0Z,WAAW,CAAC,CACzD,CAAC,CACD1Z,MAAM,CAACqe,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAE/C;AACA,KAAM,CAAAG,oBAAoB,CAAGC,WAAW,CAAC,IAAM,CAC7CC,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX;AACAxe,MAAM,CAACye,aAAa,CAAG,IAAM,CAC3B,GAAIjT,SAAS,CAACiC,OAAO,CAAE,CACrBjC,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACzCuI,SAAS,CAACiC,OAAO,CAACiB,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACjClD,SAAS,CAACiC,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChCxD,SAAS,CAACiC,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzC,GAAIjQ,QAAQ,CAAE,CACZA,QAAQ,CAACwP,MAAM,CAACvL,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAAC0O,OAAO,CAAG,IAAI,CACvB1O,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CACnB,CAEA5P,UAAU,CAAG,QAAQ,CACrB6B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAC,CAED;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACAb,MAAM,CAAC0e,eAAe,CAAG,IAAM,CAC7B9d,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC5B2d,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAED;AACA,MAAO,IAAM,CACX5d,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CAExB;AACA,GAAI8H,iBAAiB,CAAC8E,OAAO,CAAE,CAC7BkR,oBAAoB,CAAChW,iBAAiB,CAAC8E,OAAO,CAAC,CAC/C9E,iBAAiB,CAAC8E,OAAO,CAAG,IAAI,CAClC,CAEA;AACA,GAAI6Q,oBAAoB,CAAE,CACxBM,aAAa,CAACN,oBAAoB,CAAC,CACrC,CAEA;AACAvW,YAAY,CAACvC,OAAO,CAAEqZ,MAAM,EAAK,CAC/B,GAAIxf,KAAK,EAAIwf,MAAM,CAAE,CACnBxf,KAAK,CAAC2H,MAAM,CAAC6X,MAAM,CAAC,CACtB,CACF,CAAC,CAAC,CACF9W,YAAY,CAAClB,KAAK,CAAC,CAAC,CACpBgB,cAAc,CAAC9B,MAAM,CAAG,CAAC,CAEzB;AACArI,KAAK,CAACohB,SAAS,CAAC,CAAC,CAEjB;AACA3V,qBAAqB,CAAC3D,OAAO,CAACV,KAAK,EAAI,CACrCN,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC,CACpC,CAAC,CAAC,CACFqE,qBAAqB,CAACtC,KAAK,CAAC,CAAC,CAE7B;AACArC,eAAe,CAACoC,OAAO,CAAC,CAAC,CAEzB;AACA,GAAIvH,KAAK,CAAE,CACT,KAAM,CAAA0f,eAAe,CAAG,EAAE,CAC1B1f,KAAK,CAAC4F,QAAQ,CAAEC,MAAM,EAAK,CACzB,GAAIA,MAAM,CAACkC,MAAM,CAAE,CACjB,GAAIlC,MAAM,CAACmC,QAAQ,CAAE,CACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAC3B,CACA,GAAIpC,MAAM,CAACqC,QAAQ,CAAE,CACnB,GAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,CAAE,CAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,EAAI,CAClC,GAAIA,QAAQ,CAAClB,GAAG,CAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC,CACpB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,GAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC,CAC3B,CACF,CACA,GAAIpC,MAAM,GAAK7F,KAAK,CAAE,CACpB0f,eAAe,CAAC5I,IAAI,CAACjR,MAAM,CAAC,CAC9B,CACF,CACF,CAAC,CAAC,CAEF;AACA6Z,eAAe,CAACvZ,OAAO,CAACwZ,GAAG,EAAI,CAC7B,GAAIA,GAAG,CAACjY,MAAM,CAAE,CACdiY,GAAG,CAACjY,MAAM,CAACC,MAAM,CAACgY,GAAG,CAAC,CACxB,CACF,CAAC,CAAC,CAEF3f,KAAK,CAACwH,KAAK,CAAC,CAAC,CACf,CAEA;AACA,GAAI8S,QAAQ,CAAE,CACZA,QAAQ,CAACsF,gBAAgB,CAAC,IAAI,CAAC,CAC/B,GAAI5W,YAAY,CAACoF,OAAO,EAAIkM,QAAQ,CAACQ,UAAU,EAAIR,QAAQ,CAACQ,UAAU,CAAC+E,UAAU,GAAK7W,YAAY,CAACoF,OAAO,CAAE,CAC1GpF,YAAY,CAACoF,OAAO,CAAC0R,WAAW,CAACxF,QAAQ,CAACQ,UAAU,CAAC,CACvD,CACAR,QAAQ,CAACrS,OAAO,CAAC,CAAC,CAClBqS,QAAQ,CAACyF,gBAAgB,CAAC,CAAC,CAC7B,CAEA;AACA,GAAIpgB,QAAQ,CAAE,CACZA,QAAQ,CAACsI,OAAO,CAAC,CAAC,CACpB,CAEA;AACA3H,oBAAoB,CAACkH,KAAK,CAAC,CAAC,CAC5BhH,oBAAoB,CAACgH,KAAK,CAAC,CAAC,CAC5B9F,gBAAgB,CAAC8F,KAAK,CAAC,CAAC,CACxB/F,aAAa,CAAC+F,KAAK,CAAC,CAAC,CACrB5F,gBAAgB,CAAC4F,KAAK,CAAC,CAAC,CACxB3F,kBAAkB,CAAC2F,KAAK,CAAC,CAAC,CAE1BjG,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACvB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA3D,SAAS,CAAC,IAAM,CACd;AACAmE,qBAAqB,CAAC,CAAC,CAEvB;AACA,KAAM,CAAAge,uBAAuB,CAAGA,CAAA,GAAM,CACpCze,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjCQ,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAED;AACArB,MAAM,CAACqe,gBAAgB,CAAC,oBAAoB,CAAEgB,uBAAuB,CAAC,CAEtE;AACA,KAAM,CAAAC,UAAU,CAAGf,WAAW,CAAC,IAAM,CACnCld,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAAE,KAAK,CAAC,CAET;AACA,MAAO,IAAM,CACXrB,MAAM,CAACuf,mBAAmB,CAAC,oBAAoB,CAAEF,uBAAuB,CAAC,CACzET,aAAa,CAACU,UAAU,CAAC,CAC3B,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACApiB,SAAS,CAAC,IAAM,CACd;AACA,GAAImC,KAAK,EAAIkJ,SAAS,CAACkF,OAAO,EAAI7D,aAAa,EAAIA,aAAa,CAAC7D,MAAM,CAAG,CAAC,CAAE,CAC3E;AACA,KAAM,CAAAyZ,KAAK,CAAGpP,UAAU,CAAC,IAAM,CAC7B,GAAI/Q,KAAK,EAAIkJ,SAAS,CAACkF,OAAO,EAAI7D,aAAa,EAAIA,aAAa,CAAC7D,MAAM,CAAG,CAAC,CAAE,CAAG;AAC9E0Z,mBAAmB,CAAClX,SAAS,CAACkF,OAAO,CAAE7D,aAAa,CAAC,CACvD,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAM8V,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,IAAM,CACL5e,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAC1C,CACF,CAAC,CAAE,CAACxB,KAAK,CAAEuK,aAAa,CAAC,CAAC,CAE1B;AACA1M,SAAS,CAAC,IAAM,CACd,GAAImL,YAAY,CAACoF,OAAO,CAAE,CACxB;AACA,KAAM,CAAAkS,WAAW,CAAI1I,KAAK,EAAK,CAC7B,GAAI5X,KAAK,EAAImM,SAAS,CAACiC,OAAO,CAAE,CAC9BmS,gBAAgB,CAAC3I,KAAK,CAAE5O,YAAY,CAACoF,OAAO,CAAEpO,KAAK,CAAEmM,SAAS,CAACiC,OAAO,CAAC,CACzE,CACF,CAAC,CAED;AACApF,YAAY,CAACoF,OAAO,CAAC4Q,gBAAgB,CAAC,OAAO,CAAEsB,WAAW,CAAC,CAE3D;AACA/e,OAAO,CAACC,GAAG,CAAC,eAAe,CAAE,CAAC,CAACwH,YAAY,CAACoF,OAAO,CAAC,CAEpD;AACA,MAAO,IAAM,CACX,GAAIpF,YAAY,CAACoF,OAAO,CAAE,CACxBpF,YAAY,CAACoF,OAAO,CAAC8R,mBAAmB,CAAC,OAAO,CAAEI,WAAW,CAAC,CAC9D/e,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CAC3B,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACxB,KAAK,CAAEmM,SAAS,CAACiC,OAAO,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAoS,SAAS,CAAGxiB,WAAW,CAAC,IAAM,CAClCuD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B;AACF,CAAC,CAAE,CAACwH,YAAY,CAAEwE,aAAa,CAAE5L,gBAAgB,CAAC,CAAC,CAEnD;AACA,KAAM,CAAA6e,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAzY,QAAQ,CAAG,GAAI,CAAA/J,KAAK,CAACyiB,WAAW,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAChD,KAAM,CAAAxY,QAAQ,CAAG,GAAI,CAAAjK,KAAK,CAAC0iB,iBAAiB,CAAC,CAAEvT,KAAK,CAAE,QAAS,CAAC,CAAC,CACjE,KAAM,CAAA6J,iBAAiB,CAAG,GAAI,CAAAhZ,KAAK,CAAC2iB,IAAI,CAAC5Y,QAAQ,CAAEE,QAAQ,CAAC,CAE5D;AACA,KAAM,CAAA2Y,YAAY,CAAG,GAAI,CAAA5iB,KAAK,CAAC6iB,gBAAgB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAC,CAC5D,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAA9iB,KAAK,CAAC0iB,iBAAiB,CAAC,CAAEvT,KAAK,CAAE,QAAS,CAAC,CAAC,CACrE,KAAM,CAAA4T,SAAS,CAAG,GAAI,CAAA/iB,KAAK,CAAC2iB,IAAI,CAACC,YAAY,CAAEE,YAAY,CAAC,CAC5DC,SAAS,CAAC7V,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,CAAC,GAAG,CAAE,CAAC,CAAC,CAClCqT,iBAAiB,CAACtR,GAAG,CAACqb,SAAS,CAAC,CAEhC,MAAO,CAAA/J,iBAAiB,CAC1B,CAAC,CAED;AACA,KAAM,CAAAgK,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACjhB,KAAK,CAAE,OAEZ;AACA4B,gBAAgB,CAACuE,OAAO,CAAC,CAAC+a,QAAQ,CAAEzU,OAAO,GAAK,CAC9C,GAAIyU,QAAQ,CAACxb,KAAK,CAAE,CAClB;AACA,KAAM,CAAAyb,cAAc,CAAG,GAAI,CAAAljB,KAAK,CAACmjB,cAAc,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxD,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAApjB,KAAK,CAAC0iB,iBAAiB,CAAC,CACjDvT,KAAK,CAAE,QAAQ,CAAC;AAChB0H,WAAW,CAAE,KAAK,CAClBU,OAAO,CAAE,GAAG,CAAG;AACf8L,UAAU,CAAE,KACd,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAtjB,KAAK,CAAC2iB,IAAI,CAACO,cAAc,CAAEE,cAAc,CAAC,CACjEE,UAAU,CAACpW,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAG;AAEnC;AACA2d,UAAU,CAACC,QAAQ,CAAG,CACpB7O,IAAI,CAAE,cAAc,CACpBlG,OAAO,CAAEA,OAAO,CAChB2D,IAAI,CAAE8Q,QAAQ,CAAChR,YAAY,CAACE,IAAI,CAChCqR,aAAa,CAAE,IACjB,CAAC,CAED;AACAP,QAAQ,CAACxb,KAAK,CAACC,GAAG,CAAC4b,UAAU,CAAC,CAE9BhgB,OAAO,CAACC,GAAG,CAAC,OAAO0f,QAAQ,CAAChR,YAAY,CAACE,IAAI,KAAK3D,OAAO,aAAa,CAAC,CACzE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA5O,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAsiB,KAAK,CAAGpP,UAAU,CAAC,IAAM,CAC7B,GAAInP,gBAAgB,CAAC8X,IAAI,CAAG,CAAC,CAAE,CAC7BnY,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACF,CACF,CAAC,CAAE,IAAI,CAAC,CAAG;AAEX,MAAO,IAAM6e,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACAtiB,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACX;AACA,GAAIgP,0BAA0B,CAACuB,OAAO,CAAE,CACtCmR,aAAa,CAAC1S,0BAA0B,CAACuB,OAAO,CAAC,CACjDvB,0BAA0B,CAACuB,OAAO,CAAG,IAAI,CACzC7M,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACA3D,SAAS,CAAC,IAAM,CACd,GAAI,CAACyO,mBAAmB,CAACE,OAAO,EAAIK,0BAA0B,CAACuB,OAAO,CAAE,CACtEmR,aAAa,CAAC1S,0BAA0B,CAACuB,OAAO,CAAC,CACjDvB,0BAA0B,CAACuB,OAAO,CAAG,IAAI,CACzCxB,mBAAmB,CAACwB,OAAO,CAAG,IAAI,CAClC7M,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACnC,CACF,CAAC,CAAE,CAAC8K,mBAAmB,CAACE,OAAO,CAAC,CAAC,CAEjC;AACA3O,SAAS,CAAC,IAAM,CACd;AACA,GAAI0M,aAAa,EAAIA,aAAa,CAAC7D,MAAM,CAAG,CAAC,CAAE,CAC7C;AACA,GAAI,CAAC0F,oBAAoB,CAAE,CACzB;AACA,KAAM,CAAAsV,6BAA6B,CAAGnX,aAAa,CAAC9H,IAAI,CACtDyN,YAAY,EAAIA,YAAY,CAACY,eAAe,GAAK,KAAK,EAAIZ,YAAY,CAACzD,OACzE,CAAC,CAED;AACA,KAAM,CAAAkV,kBAAkB,CAAGD,6BAA6B,EAAInX,aAAa,CAAC,CAAC,CAAC,CAE5EhJ,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEmgB,kBAAkB,CAACvR,IAAI,CAClC,QAAQ,CAAEsR,6BAA6B,CAAG,GAAG,CAAG,GAAG,CAAC,CAEhE;AACA,KAAM,CAAAvB,KAAK,CAAGpP,UAAU,CAAC,IAAM,CAC7Bf,wBAAwB,CAAC2R,kBAAkB,CAACvR,IAAI,CAAC,CACnD,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMiQ,YAAY,CAACF,KAAK,CAAC,CAClC,CACF,CACF,CAAC,CAAE,CAAC5V,aAAa,CAAE6B,oBAAoB,CAAC,CAAC,CAEzC;AACA,KAAM,CAAAwV,yBAAyB,CAAIC,iBAAiB,EAAK,CACvD,GAAI,CAAC7hB,KAAK,EAAI,MAAO,CAAAA,KAAK,CAAC4F,QAAQ,GAAK,UAAU,EAAI,CAACic,iBAAiB,CAAE,CACxEtgB,OAAO,CAACugB,IAAI,CAAC,cAAc,CAAE,CAC3B9hB,KAAK,CAAE,CAAC,CAACA,KAAK,CACd+hB,aAAa,CAAE/hB,KAAK,EAAI,MAAO,CAAAA,KAAK,CAAC4F,QAAQ,GAAK,UAAU,CAC5DsD,SAAS,CAAE,CAAC,CAAC2Y,iBACf,CAAC,CAAC,CACF,OACF,CAEA,GAAI,CAAC7X,WAAW,CAACE,OAAO,EAAIF,WAAW,CAACE,OAAO,CAACxD,MAAM,GAAK,CAAC,CAAE,CAC5DnF,OAAO,CAACugB,IAAI,CAAC,kBAAkB,CAAC,CAChC,OAAQ;AACV,CAEA,GAAI,CAACvX,aAAa,EAAIA,aAAa,CAAC7D,MAAM,GAAK,CAAC,CAAE,CAChDnF,OAAO,CAACugB,IAAI,CAAC,kBAAkB,CAAC,CAChC,OACF,CAEAvgB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE,CACvBwgB,IAAI,CAAEhY,WAAW,CAACE,OAAO,CAACxD,MAAM,CAChCub,IAAI,CAAE1X,aAAa,CAAC7D,MACtB,CAAC,CAAC,CAEF,GAAI,CACF;AACA,KAAM,CAAAwb,aAAa,CAAGliB,KAAK,CAAC0c,QAAQ,CAACvV,MAAM,CAACwY,GAAG,EAAIA,GAAG,CAAC6B,QAAQ,EAAI7B,GAAG,CAAC6B,QAAQ,CAACW,qBAAqB,CAAC,CACtGD,aAAa,CAAC/b,OAAO,CAACwZ,GAAG,EAAI3f,KAAK,CAAC2H,MAAM,CAACgY,GAAG,CAAC,CAAC,CAC/Cpe,OAAO,CAACC,GAAG,CAAC,KAAK,CAAE0gB,aAAa,CAACxb,MAAM,CAAE,SAAS,CAAC,CAEnD,GAAI,CAAA0b,oBAAoB,CAAG,CAAC,CAE5B7X,aAAa,CAACpE,OAAO,CAAC+J,YAAY,EAAI,CACpC,GAAI,CAACA,YAAY,CAACmS,SAAS,EAAI,CAAC/f,KAAK,CAACC,OAAO,CAAC2N,YAAY,CAACmS,SAAS,CAAC,CAAE,OACvEnS,YAAY,CAACmS,SAAS,CAAClc,OAAO,CAAEmc,QAAQ,EAAK,CAC3C,GAAI,CAACA,QAAQ,CAAC1X,SAAS,EAAI,CAAC0X,QAAQ,CAACzX,QAAQ,EAAI0X,KAAK,CAAChS,UAAU,CAAC+R,QAAQ,CAAC1X,SAAS,CAAC,CAAC,EAAI2X,KAAK,CAAChS,UAAU,CAAC+R,QAAQ,CAACzX,QAAQ,CAAC,CAAC,CAAE,CAC9H,OACF,CAEA,KAAM,CAAAX,OAAO,CAAGF,WAAW,CAACE,OAAO,CAAC/C,MAAM,CACxCqb,CAAC,EAAIA,CAAC,CAAC5hB,QAAQ,GAAKsP,YAAY,CAACE,IAAI,EAAIoS,CAAC,CAACF,QAAQ,GAAKA,QAAQ,CAAClS,IACnE,CAAC,CACD,GAAIlG,OAAO,CAACxD,MAAM,GAAK,CAAC,CAAE,OAE1B;AACAnF,OAAO,CAACC,GAAG,CAAC,MAAM,CAAE0O,YAAY,CAACE,IAAI,CAAE,IAAI,CAAEkS,QAAQ,CAAClS,IAAI,CAAE,MAAM,CAAElG,OAAO,CAACxD,MAAM,CAAC,CACnF,KAAM,CAAAwM,QAAQ,CAAG2O,iBAAiB,CAACvR,YAAY,CAC7CC,UAAU,CAAC+R,QAAQ,CAAC1X,SAAS,CAAC,CAC9B2F,UAAU,CAAC+R,QAAQ,CAACzX,QAAQ,CAC9B,CAAC,CACD;AACA,KAAM,CAAA4X,KAAK,CAAG,GAAI,CAAAxkB,KAAK,CAACoe,KAAK,CAAC,CAAC,CAC/BoG,KAAK,CAACtX,QAAQ,CAACvH,GAAG,CAACsP,QAAQ,CAAC3P,CAAC,CAAE,EAAE,CAAE,CAAC2P,QAAQ,CAACzP,CAAC,CAAC,CAAE;AACjDgf,KAAK,CAACjB,QAAQ,CAAG,CAAEW,qBAAqB,CAAE,IAAK,CAAC,CAChD;AACA,KAAM,CAAAO,QAAQ,CAAG,EAAE,CAAE;AACrB,KAAM,CAAAC,WAAW,CAAG,CAAC,CAAE;AACvB,KAAM,CAAAC,UAAU,CAAG1Y,OAAO,CAACxD,MAAM,CAAGgc,QAAQ,CAAG,CAACxY,OAAO,CAACxD,MAAM,CAAG,CAAC,EAAIic,WAAW,CACjF;AACA,KAAM,CAAAE,MAAM,CAAG,GAAG,CAAE;AACpB,KAAM,CAAAC,SAAS,CAAG,GAAG,CAAE;AACvB,KAAM,CAAAC,MAAM,CAAG,EAAE,CAAC7Y,OAAO,CAACxD,MAAM,CAAG,CAAC,GAAKmc,MAAM,CAAGC,SAAS,CAAC,CAAC,CAAG,CAAC,CACjE5Y,OAAO,CAAC/D,OAAO,CAAC,CAAC+H,MAAM,CAAE8U,GAAG,GAAK,CAC/B;AACA,KAAM,CAAAC,aAAa,CAAG,GAAI,CAAAhlB,KAAK,CAACilB,aAAa,CAAC,CAAC,CAC/C,KAAM,CAAAC,QAAQ,CAAG,GAAGhiB,QAAQ,WAAW+M,MAAM,CAACyE,IAAI,MAAM,CACxD;AACA,KAAM,CAAAyQ,YAAY,CAAG,GAAI,CAAAnlB,KAAK,CAAC0iB,iBAAiB,CAAC,CAC/C3Z,GAAG,CAAEic,aAAa,CAAChH,IAAI,CAACkH,QAAQ,CAAC,CACjCrO,WAAW,CAAE,IAAI,CACjBU,OAAO,CAAE,CAAE;AACb,CAAC,CAAC,CACF;AACA,KAAM,CAAA6N,UAAU,CAAG,GAAI,CAAAplB,KAAK,CAAC0iB,iBAAiB,CAAC,CAC7CvT,KAAK,CAAE,QAAQ,CACf0H,WAAW,CAAE,IAAI,CACjBU,OAAO,CAAE,GAAI;AACf,CAAC,CAAC,CACF;AACA,KAAM,CAAA8N,OAAO,CAAGT,MAAM,CAAG,IAAI,CAC7B,KAAM,CAAAU,QAAQ,CAAGV,MAAM,CAAG,IAAI,CAC9B;AACA;AACA,KAAM,CAAAW,UAAU,CAAG,GAAI,CAAAvlB,KAAK,CAACwlB,aAAa,CAACH,OAAO,CAAEC,QAAQ,CAAC,CAC7D,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAzlB,KAAK,CAAC2iB,IAAI,CAAC4C,UAAU,CAAEH,UAAU,CAAC,CACrD;AACA,KAAM,CAAAM,YAAY,CAAG,GAAI,CAAA1lB,KAAK,CAACwlB,aAAa,CAACZ,MAAM,CAAEA,MAAM,CAAC,CAC5D,KAAM,CAAAe,QAAQ,CAAG,GAAI,CAAA3lB,KAAK,CAAC2iB,IAAI,CAAC+C,YAAY,CAAEP,YAAY,CAAC,CAC3D;AACAQ,QAAQ,CAACzY,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAC,CACjC;AACA,KAAM,CAAAigB,SAAS,CAAG,GAAI,CAAA5lB,KAAK,CAACoe,KAAK,CAAC,CAAC,CACnCwH,SAAS,CAACle,GAAG,CAAC+d,MAAM,CAAC,CACrBG,SAAS,CAACle,GAAG,CAACie,QAAQ,CAAC,CACvB;AACAC,SAAS,CAAC1Y,QAAQ,CAACvH,GAAG,CAACmf,MAAM,CAAGC,GAAG,EAAIH,MAAM,CAAGC,SAAS,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACjE;AACAe,SAAS,CAACtO,WAAW,CAAG,GAAG,CAAE;AAC7BsO,SAAS,CAACrC,QAAQ,CAAG,CACnBzT,QAAQ,CAAEG,MAAM,CAACuE,EAAE,CACnBqR,UAAU,CAAE5V,MAAM,CAACyE,IAAI,CACvB2P,QAAQ,CAAEA,QAAQ,CAAClS,IAAI,CACvB2T,oBAAoB,CAAE,IACxB,CAAC,CACDtB,KAAK,CAAC9c,GAAG,CAACke,SAAS,CAAC,CACtB,CAAC,CAAC,CACF;AACA;AACA,KAAM,CAAAG,YAAY,CAAIvB,KAAK,CAACtX,QAAQ,CAAC1H,CAAC,CAAE;AACxC,KAAM,CAAAwgB,cAAc,CAAG,GAAI,CAAAhmB,KAAK,CAAC6iB,gBAAgB,CAAC,GAAG,CAAE,GAAG,CAAEkD,YAAY,CAAE,EAAE,CAAC,CAC7E,KAAM,CAAAE,cAAc,CAAG,GAAI,CAAAjmB,KAAK,CAAC0iB,iBAAiB,CAAC,CAAEvT,KAAK,CAAE,QAAQ,CAAE0H,WAAW,CAAE,IAAI,CAAEU,OAAO,CAAE,GAAI,CAAC,CAAC,CACxG,KAAM,CAAA2O,MAAM,CAAG,GAAI,CAAAlmB,KAAK,CAAC2iB,IAAI,CAACqD,cAAc,CAAEC,cAAc,CAAC,CAC7D;AACAC,MAAM,CAAChZ,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,CAACogB,YAAY,CAAG,CAAC,CAAE,CAAC,CAAC,CAC5C;AACA;AACAG,MAAM,CAAC3C,QAAQ,CAAG,CAAE4C,sBAAsB,CAAE,IAAK,CAAC,CAClD3B,KAAK,CAAC9c,GAAG,CAACwe,MAAM,CAAC,CACjBnkB,KAAK,CAAC2F,GAAG,CAAC8c,KAAK,CAAC,CAClB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEJ,CAAE,MAAOnb,CAAC,CAAE,CACV/F,OAAO,CAACsB,KAAK,CAAC,iCAAiC,CAAEyE,CAAC,CAAC,CACnD,OACF,CACF,CAAC,CAED;AACAzJ,SAAS,CAAC,IAAM,CACd,GAAImC,KAAK,EAAI,MAAO,CAAAA,KAAK,CAAC4F,QAAQ,GAAK,UAAU,EAAIsD,SAAS,CAACkF,OAAO,EAAIpE,WAAW,CAACE,OAAO,CAAE,KAAAma,oBAAA,CAC7F9iB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE,CACvBxB,KAAK,CAAE,CAAC,CAACA,KAAK,CACdkJ,SAAS,CAAE,CAAC,CAACA,SAAS,CAACkF,OAAO,CAC9BkW,YAAY,CAAE,EAAAD,oBAAA,CAAAra,WAAW,CAACE,OAAO,UAAAma,oBAAA,iBAAnBA,oBAAA,CAAqB3d,MAAM,GAAI,CAAC,CAC9C6d,kBAAkB,CAAE,CAAAha,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE7D,MAAM,GAAI,CAC/C,CAAC,CAAC,CAEF;AACA,KAAM,CAAAyZ,KAAK,CAAGpP,UAAU,CAAC,IAAM,CAC7B6Q,yBAAyB,CAAC1Y,SAAS,CAACkF,OAAO,CAAC,CAC9C,CAAC,CAAE,GAAG,CAAC,CAAE;AAET,MAAO,IAAMiS,YAAY,CAACF,KAAK,CAAC,CAClC,CACF,CAAC,CAAE,CAACngB,KAAK,CAAEkJ,SAAS,CAACkF,OAAO,CAAEpE,WAAW,CAACE,OAAO,CAAEK,aAAa,CAAC,CAAC,CAAE;AAEpE;AACA1M,SAAS,CAAC,IAAM,CACd,GAAImC,KAAK,EAAIkJ,SAAS,CAACkF,OAAO,EAAIpE,WAAW,CAACE,OAAO,EAAIK,aAAa,CAAE,CACtE;AACA,KAAM,CAAAia,UAAU,CAAGzT,UAAU,CAAC,IAAM,CAClC,KAAM,CAAAmR,aAAa,CAAGliB,KAAK,CAAC0c,QAAQ,CAACvV,MAAM,CAACwY,GAAG,EAAIA,GAAG,CAAC6B,QAAQ,EAAI7B,GAAG,CAAC6B,QAAQ,CAACW,qBAAqB,CAAC,CACtG,GAAID,aAAa,CAACxb,MAAM,GAAK,CAAC,CAAE,CAC9BnF,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CAC/BogB,yBAAyB,CAAC1Y,SAAS,CAACkF,OAAO,CAAC,CAC9C,CAAC,IAAM,CACL7M,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE0gB,aAAa,CAACxb,MAAM,CAAE,MAAM,CAAC,CACzD,CACF,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAM2Z,YAAY,CAACmE,UAAU,CAAC,CACvC,CACF,CAAC,CAAE,CAACxkB,KAAK,CAAEkJ,SAAS,CAACkF,OAAO,CAAEpE,WAAW,CAACE,OAAO,CAAEK,aAAa,CAAC,CAAC,CAElE;AACA1M,SAAS,CAAC,IAAM,CACd,GAAI,CAACmC,KAAK,EAAI,CAACmM,SAAS,CAACiC,OAAO,CAAE,OAClC,KAAM,CAAAqW,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACAzkB,KAAK,CAAC0c,QAAQ,CAACvW,OAAO,CAACwZ,GAAG,EAAI,CAC5B;AACA,GAAIA,GAAG,CAAC6B,QAAQ,EAAI7B,GAAG,CAAC6B,QAAQ,CAACW,qBAAqB,CAAE,CACtDxC,GAAG,CAACtQ,MAAM,CAAClD,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAAC5H,CAAC,CAAEoc,GAAG,CAACxU,QAAQ,CAAC1H,CAAC,CAAE0I,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAACxH,CAAC,CAAC,CACxF,CACA;AACA,GAAIgc,GAAG,CAAC6B,QAAQ,EAAI7B,GAAG,CAAC6B,QAAQ,CAAC7O,IAAI,GAAK,aAAa,CAAE,CACvDgN,GAAG,CAACtQ,MAAM,CAAClD,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAAC5H,CAAC,CAAEoc,GAAG,CAACxU,QAAQ,CAAC1H,CAAC,CAAE0I,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAACxH,CAAC,CAAC,CACxF,CACF,CAAC,CAAC,CACFia,qBAAqB,CAAC6G,gBAAgB,CAAC,CACzC,CAAC,CACDA,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAACzkB,KAAK,CAAC,CAAC,CAEX;AACA,KAAM,CAAAugB,gBAAgB,CAAGA,CAAC3I,KAAK,CAAE8M,SAAS,CAAEC,aAAa,CAAEC,cAAc,GAAK,CAC5E,GAAI,CAACF,SAAS,EAAI,CAACC,aAAa,EAAI,CAACC,cAAc,CAAE,OACrD,KAAM,CAAAC,IAAI,CAAGH,SAAS,CAACI,qBAAqB,CAAC,CAAC,CAC9C,KAAM,CAAAC,MAAM,CAAI,CAACnN,KAAK,CAACoN,OAAO,CAAGH,IAAI,CAACxZ,IAAI,EAAIqZ,SAAS,CAACO,WAAW,CAAI,CAAC,CAAG,CAAC,CAC5E,KAAM,CAAAC,MAAM,CAAG,EAAE,CAACtN,KAAK,CAACuN,OAAO,CAAGN,IAAI,CAAC7X,GAAG,EAAI0X,SAAS,CAACU,YAAY,CAAC,CAAG,CAAC,CAAG,CAAC,CAC7E,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAApnB,KAAK,CAACqnB,SAAS,CAAC,CAAC,CACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,CAAG,CAAC,CACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,CAAG,CAAC,CACnC,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAA1nB,KAAK,CAAC2nB,OAAO,CAACb,MAAM,CAAEG,MAAM,CAAC,CACrDG,SAAS,CAACQ,aAAa,CAACF,WAAW,CAAEf,cAAc,CAAC,CACpD,KAAM,CAAAkB,UAAU,CAAGT,SAAS,CAACU,gBAAgB,CAACpB,aAAa,CAACjI,QAAQ,CAAE,IAAI,CAAC,CAC3E,GAAIoJ,UAAU,CAACpf,MAAM,CAAG,CAAC,CAAE,CACzB,IAAK,GAAI,CAAAyJ,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG2V,UAAU,CAACpf,MAAM,CAAEyJ,CAAC,EAAE,CAAE,CAC1C,KAAM,CAAAwP,GAAG,CAAGmG,UAAU,CAAC3V,CAAC,CAAC,CAACtK,MAAM,CAChC,GAAI8Z,GAAG,CAACjY,MAAM,EAAIiY,GAAG,CAACjY,MAAM,CAAC8Z,QAAQ,EAAI7B,GAAG,CAACjY,MAAM,CAAC8Z,QAAQ,CAACuC,oBAAoB,CAAE,CACjF,KAAM,CAAAhW,QAAQ,CAAG4R,GAAG,CAACjY,MAAM,CAAC8Z,QAAQ,CAACzT,QAAQ,CAC7CxM,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEwI,WAAW,CAAC,CACvD,KAAM,CAAAkE,MAAM,CAAGlE,WAAW,CAACE,OAAO,CAACzH,IAAI,CAAC+f,CAAC,EAAIA,CAAC,CAAC/P,EAAE,GAAK1E,QAAQ,CAAC,CAC/D,GAAIG,MAAM,CAAE,CACV,KAAM,CAAA3K,CAAC,CAAGqU,KAAK,CAACoN,OAAO,CACvB,KAAM,CAAAvhB,CAAC,CAAGmU,KAAK,CAACuN,OAAO,CACvBrX,gBAAgB,CAAC,CACftB,OAAO,CAAE,IAAI,CACbuB,QAAQ,CACR5C,QAAQ,CAAE,CAAE5H,CAAC,CAAEE,CAAE,CAAC,CAClBiJ,OAAO,CAAEuB,0BAA0B,CAACC,MAAM,CAC5C,CAAC,CAAC,CACF,OAAQ;AACV,CACF,CACF,CACF,CACA;AACA;AACF,CAAC,CAKD,mBACElP,KAAA,CAAAE,SAAA,EAAAwd,QAAA,eACE5d,IAAA,SAAMknB,KAAK,CAAE9Y,UAAW,CAAAwP,QAAA,CAAC,gCAAK,CAAM,CAAC,cACrC5d,IAAA,CAACN,MAAM,EACLwnB,KAAK,CAAEjZ,uBAAwB,CAC/BkZ,WAAW,CAAC,4CAAS,CACrBC,QAAQ,CAAElW,wBAAyB,CACnCmW,QAAQ,CAAEnW,wBAA0B;AAAA,CACpCoW,OAAO,CAAE7b,aAAa,CAACvD,GAAG,CAACkJ,YAAY,GAAK,CAC1CD,KAAK,CAAEC,YAAY,CAACE,IAAI,CACxBiW,KAAK,CAAEnW,YAAY,CAACE,IACtB,CAAC,CAAC,CAAE,CACJsJ,IAAI,CAAC,OAAO,CACZ4M,QAAQ,CAAE,IAAK,CACfC,aAAa,CAAE,CACbhb,MAAM,CAAE,IAAI,CACZib,SAAS,CAAE,OACb,CAAE,CACFvW,KAAK,CAAE7D,oBAAoB,CAAGA,oBAAoB,CAACgE,IAAI,CAAGyF,SAAU,CACrE,CAAC,cACF/W,IAAA,QAAK2nB,GAAG,CAAEzd,YAAa,CAACgd,KAAK,CAAE,CAAE/Y,KAAK,CAAE,MAAM,CAAEoG,MAAM,CAAE,MAAO,CAAE,CAAE,CAAC,CAGnE/G,mBAAmB,CAACE,OAAO,eAC1BxN,KAAA,QACEgnB,KAAK,CAAE,CACL7a,QAAQ,CAAE,UAAU,CACpBE,IAAI,CAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAAC5H,CAAC,IAAI,CAC3CyJ,GAAG,CAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAAC1H,CAAC,IAAI,CAC1C6H,SAAS,CAAE,wBAAwB,CACnCC,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,qBAAqB,CACtCwB,KAAK,CAAE,OAAO,CACdtB,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,8BAA8B,CACzCN,OAAO,CAAE,GAAG,CACZ+a,QAAQ,CAAE,OAAO,CAAE;AACnB1a,QAAQ,CAAE,MAAO;AACnB,CAAE,CAAA0Q,QAAA,EAEDpQ,mBAAmB,CAACI,OAAO,cAC5B5N,IAAA,WACEknB,KAAK,CAAE,CACL7a,QAAQ,CAAE,UAAU,CACpB6B,GAAG,CAAE,KAAK,CACV2Z,KAAK,CAAE,KAAK,CACZC,UAAU,CAAE,MAAM,CAClB/a,MAAM,CAAE,MAAM,CACduB,KAAK,CAAE,OAAO,CACdpB,QAAQ,CAAE,MAAM,CAChBD,MAAM,CAAE,SAAS,CACjBJ,OAAO,CAAE,SACX,CAAE,CACFkb,OAAO,CAAEA,CAAA,GAAMC,kBAAkB,CAACva,sBAAsB,CAAE,CAAAmQ,QAAA,CAC3D,MAED,CAAQ,CAAC,EACN,CACN,cAED1d,KAAA,QAAKgnB,KAAK,CAAE9a,oBAAqB,CAAAwR,QAAA,eAC/B5d,IAAA,WACEknB,KAAK,CAAE,CACL,GAAGta,WAAW,CACdE,eAAe,CAAEZ,QAAQ,GAAK,QAAQ,CAAG,SAAS,CAAG,0BAA0B,CAC/EoC,KAAK,CAAEpC,QAAQ,GAAK,QAAQ,CAAG,OAAO,CAAG,OAC3C,CAAE,CACF6b,OAAO,CAAE1Y,kBAAmB,CAAAuO,QAAA,CAC7B,0BAED,CAAQ,CAAC,cACT5d,IAAA,WACEknB,KAAK,CAAE,CACL,GAAGta,WAAW,CACdE,eAAe,CAAEZ,QAAQ,GAAK,QAAQ,CAAG,SAAS,CAAG,0BAA0B,CAC/EoC,KAAK,CAAEpC,QAAQ,GAAK,QAAQ,CAAG,OAAO,CAAG,OAC3C,CAAE,CACF6b,OAAO,CAAEvY,kBAAmB,CAAAoO,QAAA,CAC7B,0BAED,CAAQ,CAAC,EACN,CAAC,CACL7O,aAAa,CAACrB,OAAO,eACpBxN,KAAA,QACEgnB,KAAK,CAAE,CACL7a,QAAQ,CAAE,UAAU,CACpBE,IAAI,CAAE,GAAGwC,aAAa,CAAC1C,QAAQ,CAAC5H,CAAC,IAAI,CACrCyJ,GAAG,CAAE,GAAGa,aAAa,CAAC1C,QAAQ,CAAC1H,CAAC,IAAI,CACpC6H,SAAS,CAAE,wBAAwB,CACnCC,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,qBAAqB,CACtCwB,KAAK,CAAE,OAAO,CACdtB,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,6BAA6B,CACxCN,OAAO,CAAE,CAAC,CACVob,QAAQ,CAAE,GAAG,CACbL,QAAQ,CAAE,GAAG,CACb1a,QAAQ,CAAE,EACZ,CAAE,CAAA0Q,QAAA,EAED7O,aAAa,CAACnB,OAAO,cACtB5N,IAAA,WACEknB,KAAK,CAAE,CACL7a,QAAQ,CAAE,UAAU,CACpB6B,GAAG,CAAE,KAAK,CACV2Z,KAAK,CAAE,KAAK,CACZC,UAAU,CAAE,MAAM,CAClB/a,MAAM,CAAE,MAAM,CACduB,KAAK,CAAE,OAAO,CACdpB,QAAQ,CAAE,MAAM,CAChBD,MAAM,CAAE,SAAS,CACjBJ,OAAO,CAAE,UAAU,CACnBJ,MAAM,CAAE,IACV,CAAE,CACFsb,OAAO,CAAE7Y,wBAAyB,CAAA0O,QAAA,CACnC,MAAC,CAAQ,CAAC,EACR,CACN,EACD,CAAC,CAEP,CAAC,CAED;AACA,QAAS,CAAAzH,gBAAgBA,CAAC+R,IAAI,CAAmB,IAAjB,CAAAC,UAAU,CAAA9J,SAAA,CAAAzW,MAAA,IAAAyW,SAAA,MAAAtH,SAAA,CAAAsH,SAAA,IAAG,CAAC,CAAC,CAC7C,KAAM,CAAAoI,MAAM,CAAG,CACb2B,QAAQ,CAAED,UAAU,CAACC,QAAQ,EAAI,OAAO,CACxClb,QAAQ,CAAEib,UAAU,CAACjb,QAAQ,EAAI,EAAE,CAAE;AACrCqB,UAAU,CAAE4Z,UAAU,CAAC5Z,UAAU,EAAI,MAAM,CAC3C8Z,eAAe,CAAEF,UAAU,CAACE,eAAe,EAAI,CAAC,CAChDC,WAAW,CAAEH,UAAU,CAACG,WAAW,EAAI,CAAEjS,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEpO,CAAC,CAAE,GAAI,CAAC,CACnE2E,eAAe,CAAEqb,UAAU,CAACrb,eAAe,EAAI,CAAEuJ,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEpO,CAAC,CAAE,GAAI,CAAC,CACjFqO,SAAS,CAAE2R,UAAU,CAAC3R,SAAS,EAAI,CAAEH,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAC/D0E,OAAO,CAAEsb,UAAU,CAACtb,OAAO,EAAI,CACjC,CAAC,CAED;AACA,KAAM,CAAA0b,MAAM,CAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAC/C,KAAM,CAAAC,OAAO,CAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC,CAEvC;AACA;AAEA;AACA,KAAM,CAAAC,SAAS,CAAGF,OAAO,CAACG,WAAW,CAACX,IAAI,CAAC,CAAC/Z,KAAK,CAEjD;AACA,KAAM,CAAAA,KAAK,CAAGya,SAAS,CAAG,CAAC,CAAGnC,MAAM,CAAC5Z,OAAO,CAAG,CAAC,CAAG4Z,MAAM,CAAC4B,eAAe,CACzE,KAAM,CAAA9T,MAAM,CAAGkS,MAAM,CAACvZ,QAAQ,CAAG,CAAC,CAAGuZ,MAAM,CAAC5Z,OAAO,CAAG,CAAC,CAAG4Z,MAAM,CAAC4B,eAAe,CAEhFE,MAAM,CAACpa,KAAK,CAAGA,KAAK,CACpBoa,MAAM,CAAChU,MAAM,CAAGA,MAAM,CAEtB;AACAmU,OAAO,CAACI,IAAI,CAAG,GAAGrC,MAAM,CAAClY,UAAU,IAAIkY,MAAM,CAACvZ,QAAQ,MAAMuZ,MAAM,CAAC2B,QAAQ,EAAE,CAC7EM,OAAO,CAACK,YAAY,CAAG,QAAQ,CAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACAL,OAAO,CAACM,SAAS,CAAG,QAAQvC,MAAM,CAACjQ,SAAS,CAACH,CAAC,KAAKoQ,MAAM,CAACjQ,SAAS,CAACF,CAAC,KAAKmQ,MAAM,CAACjQ,SAAS,CAACD,CAAC,KAAKkQ,MAAM,CAACjQ,SAAS,CAACrO,CAAC,GAAG,CACtHugB,OAAO,CAACO,SAAS,CAAG,QAAQ,CAE5B;AACAP,OAAO,CAACQ,QAAQ,CAAChB,IAAI,CAAE/Z,KAAK,CAAG,CAAC,CAAEoG,MAAM,CAAG,CAAC,CAAC,CAE7C;AACA,KAAM,CAAA4U,OAAO,CAAG,GAAI,CAAAhqB,KAAK,CAACiqB,aAAa,CAACb,MAAM,CAAC,CAC/CY,OAAO,CAACE,SAAS,CAAGlqB,KAAK,CAACmqB,YAAY,CACtCH,OAAO,CAAClT,WAAW,CAAG,IAAI,CAE1B;AACA,KAAM,CAAAsT,cAAc,CAAG,GAAI,CAAApqB,KAAK,CAACqqB,cAAc,CAAC,CAC9CthB,GAAG,CAAEihB,OAAO,CACZnT,WAAW,CAAE,IACf,CAAC,CAAC,CAEF;AACA,KAAM,CAAAyT,MAAM,CAAG,GAAI,CAAAtqB,KAAK,CAACuqB,MAAM,CAACH,cAAc,CAAC,CAC/C;AACAE,MAAM,CAAChV,KAAK,CAAC3P,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAAE;AAC7B2kB,MAAM,CAACrgB,QAAQ,CAACugB,SAAS,CAAG,KAAK,CAAE;AAEnC;AACAF,MAAM,CAAC/G,QAAQ,CAAG,CAChBwF,IAAI,CAAEA,IAAI,CACVzB,MAAM,CAAEA,MACV,CAAC,CAED,MAAO,CAAAgD,MAAM,CACf,CAIA;AACA5nB,MAAM,CAAC+nB,eAAe,CAAG,IAAM,CAC7B,GAAI,CACF;AACA,KAAM,CAAAxO,MAAM,CAAGoN,QAAQ,CAACqB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc,CAC5E,GAAI3O,MAAM,CAAE,CACV;AACA,KAAM,CAAA4O,MAAM,CAAG5O,MAAM,CAAC/O,QAAQ,CAAC9H,KAAK,CAAC,CAAC,CAEtC;AACA6W,MAAM,CAAC/O,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAC9BsW,MAAM,CAACzL,EAAE,CAAC7K,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtBsW,MAAM,CAAC7K,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACA6K,MAAM,CAACvK,YAAY,CAAC,CAAC,CACrBuK,MAAM,CAACtK,iBAAiB,CAAC,IAAI,CAAC,CAE9B;AACA,KAAM,CAAAjQ,QAAQ,CAAG2nB,QAAQ,CAACqB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB,CAChF,GAAIppB,QAAQ,CAAE,CACZA,QAAQ,CAACwP,MAAM,CAACvL,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAAC2P,MAAM,CAAC,CAAC,CACnB,CAEA/N,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE,CACxBwnB,GAAG,CAAEF,MAAM,CAAClY,OAAO,CAAC,CAAC,CACrBqY,GAAG,CAAE/O,MAAM,CAAC/O,QAAQ,CAACyF,OAAO,CAAC,CAC/B,CAAC,CAAC,CAEF,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAE,MAAOtJ,CAAC,CAAE,CACV/F,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEyE,CAAC,CAAC,CAC9B,MAAO,MAAK,CACd,CACF,CAAC,CAGD;AACA,KAAM,CAAA0S,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFzY,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,KAAM,CAAA8b,MAAM,CAAG,GAAI,CAAApf,UAAU,CAAC,CAAC,CAE/B;AACA,GAAI,CACF,KAAM,CAAEgrB,gBAAgB,CAAEC,WAAW,CAAEC,WAAW,CAAEC,UAAU,CAAC,CAAG,KAAM,CAAAxN,OAAO,CAACyN,GAAG,CAAC,CAClFhM,MAAM,CAACiM,SAAS,CAAC,GAAGpoB,QAAQ,4BAA4B,CAAC,CAC3Dmc,MAAM,CAACiM,SAAS,CAAC,GAAGpoB,QAAQ,uBAAuB,CAAC,CACpDmc,MAAM,CAACiM,SAAS,CAAC,GAAGpoB,QAAQ,uBAAuB,CAAC,CAClDmc,MAAM,CAACiM,SAAS,CAAC,GAAGpoB,QAAQ,sBAAsB,CAAC,CAEtD,CAAC,CAIF;AACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB5B,qBAAqB,CAAGupB,WAAW,CAACnpB,KAAK,CACzCJ,qBAAqB,CAACgG,QAAQ,CAAE8O,KAAK,EAAK,CACxC,GAAIA,KAAK,CAAC3M,MAAM,CAAE,CACd,KAAM,CAAA4M,WAAW,CAAG,GAAI,CAAA1W,KAAK,CAACqe,oBAAoB,CAAC,CACnDlP,KAAK,CAAE,QAAQ,CAAG;AAClBmP,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdC,eAAe,CAAE,GACnB,CAAC,CAAC,CAEA;AACA,GAAI/H,KAAK,CAACxM,QAAQ,CAAClB,GAAG,CAAE,CACtB2N,WAAW,CAAC3N,GAAG,CAAG0N,KAAK,CAACxM,QAAQ,CAAClB,GAAG,CACtC,CACA0N,KAAK,CAAC8U,OAAO,CAAG7U,WAAW,CAC/B,CACF,CAAC,CAAC,CAEFpT,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACA3B,qBAAqB,CAAGupB,WAAW,CAACppB,KAAK,CACzC;AACAH,qBAAqB,CAAC0T,KAAK,CAAC3P,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxC;AACA/D,qBAAqB,CAAC+F,QAAQ,CAAE8O,KAAK,EAAK,CACxC,GAAIA,KAAK,CAAC3M,MAAM,EAAI2M,KAAK,CAACxM,QAAQ,CAAE,CAClC;AACAwM,KAAK,CAACxM,QAAQ,CAACqU,SAAS,CAAG,GAAG,CAC9B7H,KAAK,CAACxM,QAAQ,CAACsU,SAAS,CAAG,GAAG,CAC9B9H,KAAK,CAACxM,QAAQ,CAACuU,eAAe,CAAG,GAAG,CACtC,CACF,CAAC,CAAC,CAEFlb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB;AACA1B,oBAAoB,CAAGupB,UAAU,CAACrpB,KAAK,CACvC;AACA;AACA;AACAF,oBAAoB,CAAC8F,QAAQ,CAAE8O,KAAK,EAAK,CACvC,GAAIA,KAAK,CAAC3M,MAAM,EAAI2M,KAAK,CAACxM,QAAQ,CAAE,CAClC;AACAwM,KAAK,CAACxM,QAAQ,CAACqU,SAAS,CAAG,GAAG,CAC9B7H,KAAK,CAACxM,QAAQ,CAACsU,SAAS,CAAG,GAAG,CAC9B9H,KAAK,CAACxM,QAAQ,CAACuU,eAAe,CAAG,GAAG,CAEtC,CACA,GAAI/H,KAAK,CAAC3M,MAAM,CAAC,CACf2M,KAAK,CAAC+U,UAAU,CAAG,IAAI,CACzB,CACF,CAAC,CAAC,CAIF;AACAloB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE6nB,UAAU,CAAC5iB,UAAU,CAACC,MAAM,CAAE,GAAG,CAAC,CAC5D,GAAI2iB,UAAU,CAAC5iB,UAAU,EAAI4iB,UAAU,CAAC5iB,UAAU,CAACC,MAAM,CAAG,CAAC,CAAE,CAC7DnF,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE6nB,UAAU,CAAC5iB,UAAU,CAACC,MAAM,CAAE,GAAG,CAAC,CACzDzG,eAAe,CAAGopB,UAAU,CAC9B,CAAC,IAAM,CACL9nB,OAAO,CAACugB,IAAI,CAAC,cAAc,CAAC,CAC9B,CAEAvgB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CAEzB;AACAzB,0BAA0B,CAAGmpB,gBAAgB,CAAClpB,KAAK,CACnDuB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEzB,0BAA0B,CAAC,CACjD;AACAA,0BAA0B,CAACwT,KAAK,CAAC3P,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7C;AACA7D,0BAA0B,CAAC6F,QAAQ,CAAE8O,KAAK,EAAK,CAC7C,GAAIA,KAAK,CAAC3M,MAAM,EAAI2M,KAAK,CAACxM,QAAQ,CAAE,CAClC;AACAwM,KAAK,CAACxM,QAAQ,CAACqU,SAAS,CAAG,GAAG,CAC9B7H,KAAK,CAACxM,QAAQ,CAACsU,SAAS,CAAG,GAAG,CAC9B9H,KAAK,CAACxM,QAAQ,CAACuU,eAAe,CAAG,GAAG,CACxC,CACF,CAAC,CAAC,CAEAlb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,CAAE,MAAOqB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAExC;AACA,GAAI,CACF,GAAI,CAACjD,qBAAqB,CAAE,CAC1B,KAAM,CAAAupB,WAAW,CAAG,KAAM,CAAA7L,MAAM,CAACiM,SAAS,CAAC,GAAGpoB,QAAQ,uBAAuB,CAAC,CAC9EvB,qBAAqB,CAAGupB,WAAW,CAACnpB,KAAK,CAC3C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAE,MAAO0pB,GAAG,CAAE,CACZnoB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAE6mB,GAAG,CAAC,CAClC,CACF,CACF,CAAE,MAAO7mB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAAqW,mBAAmB,CAAIvG,IAAI,EAAK,CACpC,KAAM,CAAAgX,KAAK,CAAG,CACZ,GAAG,CAAE,OAAO,CACZ,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,OACP,CAAC,CACD,MAAO,CAAAA,KAAK,CAAChX,IAAI,CAAC,EAAI,MAAM,CAC9B,CAAC,CAED;AACA;AACA,KAAM,CAAAiX,iBAAiB,CAAGA,CAACC,EAAE,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CAE5C,KAAM,CAAAC,CAAC,CAAGxlB,IAAI,CAACylB,IAAI,CAAC,CAACL,EAAE,CAACE,EAAE,GAAGF,EAAE,CAACE,EAAE,CAAC,CAAC,CAACD,EAAE,CAACE,EAAE,GAAGF,EAAE,CAACE,EAAE,CAAC,CAAC,CACpD,MAAQ,CAAAC,CAAC,CACX,CAAC,CAED;AACA,KAAM,CAAAE,kBAAkB,CAAInS,SAAS,EAAK,CACxC,OAAOA,SAAS,EACd,IAAK,KAAK,CAAE;AACV,MAAO,CAAEoS,aAAa,CAAE,MAAM,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC3D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,IAAK,KAAK,CAAE;AACZ,IAAK,KAAK,CAAE;AACZ,IAAK,MAAM,CAAE;AACX,MAAO,CAAED,aAAa,CAAE,MAAM,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC3D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,QACE,MAAO,CAAED,aAAa,CAAE,IAAI,CAAEC,iBAAiB,CAAE,CAAE,CAAC,CAAE;AAC1D,CACF,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAACtS,SAAS,CAAEuS,QAAQ,CAAEC,WAAW,CAAEjc,UAAU,GAAK,CAC5E,KAAM,CAAE6b,aAAa,CAAEC,iBAAkB,CAAC,CAAGF,kBAAkB,CAACnS,SAAS,CAAC,CAE1E;AACA,IAAK,GAAI,CAAA7H,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG3H,cAAc,CAAC9B,MAAM,CAAEyJ,CAAC,EAAE,CAAE,CAC9C,KAAM,CAAAsa,WAAW,CAAGjiB,cAAc,CAAC2H,CAAC,CAAC,CAErC;AACA,GAAIsa,WAAW,CAACzS,SAAS,GAAKA,SAAS,CAAE,CACvC,SACF,CAEA;AACA,KAAM,CAAA0S,QAAQ,CAAGF,WAAW,CAAGC,WAAW,CAACE,cAAc,CAEzD;AACA,GAAID,QAAQ,CAAGN,aAAa,CAAE,CAC5B,SACF,CAEA;AACA,KAAM,CAAApmB,QAAQ,CAAG4lB,iBAAiB,CAChCrb,UAAU,CAAChL,CAAC,CAAEgL,UAAU,CAAC9K,CAAC,CAC1BgnB,WAAW,CAACtf,QAAQ,CAAC5H,CAAC,CAAEknB,WAAW,CAACtf,QAAQ,CAAC1H,CAC/C,CAAC,CAED;AACA,GAAIO,QAAQ,EAAIqmB,iBAAiB,CAAE,CACjC;AACAI,WAAW,CAACF,QAAQ,CAAGA,QAAQ,CAC/BE,WAAW,CAACE,cAAc,CAAGH,WAAW,CACxCC,WAAW,CAACtf,QAAQ,CAAG,CAAE,GAAGoD,UAAW,CAAC,CACxCkc,WAAW,CAACG,WAAW,CAAG,CAACH,WAAW,CAACG,WAAW,EAAI,CAAC,EAAI,CAAC,CAE5DrpB,OAAO,CAACC,GAAG,CAAC,kBAAkBwW,SAAS,SAASyS,WAAW,CAAC3S,OAAO,UAAU,CAAC4S,QAAQ,CAAC,IAAI,EAAEvmB,OAAO,CAAC,CAAC,CAAC,SAASH,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,WAAWsmB,WAAW,CAACG,WAAW,EAAE,CAAC,CAExK,MAAO,CACLC,WAAW,CAAE,IAAI,CACjB/S,OAAO,CAAE2S,WAAW,CAAC3S,OAAO,CAC5BgT,YAAY,CAAEL,WAChB,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAM,UAAU,CAAG,UAAUtiB,cAAc,CAAC2N,QAAQ,CAAC,CAAC,CAAC4U,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACzEviB,cAAc,EAAE,CAEhB,KAAM,CAAAwiB,QAAQ,CAAG,CACfnT,OAAO,CAAEiT,UAAU,CACnB/S,SAAS,CAAEA,SAAS,CACpBuS,QAAQ,CAAEA,QAAQ,CAClBW,iBAAiB,CAAEV,WAAW,CAC9BG,cAAc,CAAEH,WAAW,CAC3Brf,QAAQ,CAAE,CAAE,GAAGoD,UAAW,CAAC,CAC3Bqc,WAAW,CAAE,CACf,CAAC,CAED;AACApiB,cAAc,CAACsO,IAAI,CAACmU,QAAQ,CAAC,CAI7B,MAAO,CACLJ,WAAW,CAAE,KAAK,CAClB/S,OAAO,CAAEiT,UAAU,CACnBE,QAAQ,CAAEA,QACZ,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAA9L,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAqL,WAAW,CAAG5gB,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9B,KAAM,CAAAshB,iBAAiB,CAAG,IAAI,CAAE;AAEhC,KAAM,CAAAC,YAAY,CAAG5iB,cAAc,CAAC9B,MAAM,CAC1C,KAAM,CAAA2kB,aAAa,CAAG,EAAE,CAExB7iB,cAAc,CAAGA,cAAc,CAACrB,MAAM,CAACyQ,KAAK,EAAI,CAC9C,KAAM,CAAAhC,mBAAmB,CAAG4U,WAAW,CAAG5S,KAAK,CAAC+S,cAAc,CAC9D,GAAI/U,mBAAmB,CAAGuV,iBAAiB,CAAE,CAC3CE,aAAa,CAACvU,IAAI,CAAC,CACjBrE,EAAE,CAAEmF,KAAK,CAACE,OAAO,CACjBnF,IAAI,CAAEiF,KAAK,CAACI,SAAS,CACrBsT,YAAY,CAAE,CAAC1V,mBAAmB,CAAG,IAAI,EAAEzR,OAAO,CAAC,CAAC,CACtD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAqb,MAAM,CAAG9W,YAAY,CAAC3E,GAAG,CAAC6T,KAAK,CAACE,OAAO,CAAC,CAC9C,GAAI0H,MAAM,EAAIxf,KAAK,CAAE,CACnBA,KAAK,CAAC2H,MAAM,CAAC6X,MAAM,CAAC,CACpB9W,YAAY,CAACrC,MAAM,CAACuR,KAAK,CAACE,OAAO,CAAC,CACpC,CAEA,MAAO,MAAK,CAAE;AAChB,CACA,MAAO,KAAI,CAAE;AACf,CAAC,CAAC,CAEF,KAAM,CAAAyT,YAAY,CAAGH,YAAY,CAAG5iB,cAAc,CAAC9B,MAAM,CACzD,GAAI6kB,YAAY,CAAG,CAAC,CAAE,CACpBhqB,OAAO,CAACC,GAAG,CAAC,eAAe+pB,YAAY,cAAc,CAAC,CACtDF,aAAa,CAACllB,OAAO,CAACyR,KAAK,EAAI,CAC7BrW,OAAO,CAACC,GAAG,CAAC,WAAWoW,KAAK,CAACnF,EAAE,SAASmF,KAAK,CAACjF,IAAI,YAAYiF,KAAK,CAAC0T,YAAY,GAAG,CAAC,CACtF,CAAC,CAAC,CACF/pB,OAAO,CAACC,GAAG,CAAC,mBAAmBgH,cAAc,CAAC9B,MAAM,EAAE,CAAC,CACzD,CACF,CAAC,CAED;AACA,KAAM,CAAA8kB,uBAAuB,CAAIxT,SAAS,EAAK,CAC7C,OAAOA,SAAS,EACd,IAAK,KAAK,CAAE;AACV,MAAO,SAAQ,CAAE;AACnB,IAAK,KAAK,CAAE;AACV,MAAO,SAAQ,CAAE;AACnB,IAAK,KAAK,CAAE;AACV,MAAO,SAAQ,CAAE;AACnB,IAAK,KAAK,CAAE;AACV,MAAO,SAAQ,CAAE;AACnB,IAAK,KAAK,CAAE;AACV,MAAO,SAAQ,CAAE;AACnB,IAAK,MAAM,CAAE;AACX,MAAO,SAAQ,CAAE;AACnB,IAAK,KAAK,CAAE;AACV,MAAO,SAAQ,CAAE;AACnB,IAAK,GAAG,CAAE;AACR,MAAO,SAAQ,CAAE;AACnB,IAAK,IAAI,CAAE;AACT,MAAO,SAAQ,CAAE;AACnB,IAAK,IAAI,CAAE;AACT,MAAO,SAAQ,CAAE;AACnB,IAAK,KAAK,CAAE;AACV,MAAO,SAAQ,CAAE;AACnB,QACE,MAAO,SAAQ,CAAE;AACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAyT,gBAAgB,CAAIzT,SAAS,EAAK,CACtC,OAAOA,SAAS,EACd,IAAK,KAAK,CAAE,MAAO,KAAK,CACxB,IAAK,KAAK,CAAE,MAAO,KAAK,CACxB,IAAK,KAAK,CAAE,MAAO,IAAI,CACvB,IAAK,KAAK,CAAE,MAAO,IAAI,CACvB,IAAK,KAAK,CAAE,MAAO,IAAI,CACvB,IAAK,MAAM,CAAE,MAAO,IAAI,CACxB,IAAK,KAAK,CAAE,MAAO,IAAI,CACvB,IAAK,GAAG,CAAE,MAAO,IAAI,CACrB,IAAK,IAAI,CAAE,MAAO,IAAI,CACtB,IAAK,IAAI,CAAE,MAAO,IAAI,CACtB,IAAK,KAAK,CAAE,MAAO,IAAI,CACvB,QAAS,MAAO,KAAKA,SAAS,EAAE,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAA0T,iBAAiB,CAAGA,CAAC1T,SAAS,CAAE7M,QAAQ,CAAE2M,OAAO,GAAK,CAC1D,GAAI,CAAC9X,KAAK,CAAE,CACVuB,OAAO,CAACugB,IAAI,CAAC,oBAAoB,CAAC,CAClC,MAAO,KAAI,CACb,CAEA,GAAI,CACF;AACA,KAAM,CAAA6J,WAAW,CAAG,GAAI,CAAA1tB,KAAK,CAACoe,KAAK,CAAC,CAAC,CAErC;AACA,KAAM,CAAAuP,kBAAkB,CAAG,GAAI,CAAA3tB,KAAK,CAACwlB,aAAa,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE;AAC1D,KAAM,CAAA7X,eAAe,CAAG4f,uBAAuB,CAACxT,SAAS,CAAC,CAAE;AAC5D,KAAM,CAAA6T,kBAAkB,CAAG,GAAI,CAAA5tB,KAAK,CAAC0iB,iBAAiB,CAAC,CACrDvT,KAAK,CAAExB,eAAe,CAAE;AACxBkJ,WAAW,CAAE,IAAI,CACjBU,OAAO,CAAE,GAAG,CAAE;AACdsW,IAAI,CAAE7tB,KAAK,CAAC8tB,UACd,CAAC,CAAC,CACF,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAA/tB,KAAK,CAAC2iB,IAAI,CAACgL,kBAAkB,CAAEC,kBAAkB,CAAC,CAC7EG,cAAc,CAAC7gB,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,IAAI,CAAC,CAAE;AAC3CooB,cAAc,CAACzW,WAAW,CAAG,GAAG,CAAE;AAClCoW,WAAW,CAAChmB,GAAG,CAACqmB,cAAc,CAAC,CAE/B;AACA,KAAM,CAAA/I,aAAa,CAAG,GAAI,CAAAhlB,KAAK,CAACilB,aAAa,CAAC,CAAC,CAC/C,KAAM,CAAAC,QAAQ,CAAG,GAAGhiB,QAAQ,WAAW6W,SAAS,MAAM,CAAE;AAExD,KAAM,CAAAoL,YAAY,CAAG,GAAI,CAAAnlB,KAAK,CAAC0iB,iBAAiB,CAAC,CAC/C3Z,GAAG,CAAEic,aAAa,CAAChH,IAAI,CAACkH,QAAQ,CAC9B;AACC8E,OAAO,EAAK,CACX1mB,OAAO,CAACC,GAAG,CAAC,eAAewW,SAAS,MAAM,CAAC,CAC7C,CAAC,CACD;AACAnC,SAAS,CACT;AACChT,KAAK,EAAK,CACTtB,OAAO,CAACugB,IAAI,CAAC,eAAe9J,SAAS,aAAa,CAAC,CACnD;AACF,CACF,CAAC,CACDlD,WAAW,CAAE,IAAI,CACjBU,OAAO,CAAE,CAAC,CAAE;AACZsW,IAAI,CAAE7tB,KAAK,CAAC8tB,UACd,CAAC,CAAC,CAEF;AACA,KAAM,CAAApI,YAAY,CAAG,GAAI,CAAA1lB,KAAK,CAACwlB,aAAa,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE;AACpD,KAAM,CAAAG,QAAQ,CAAG,GAAI,CAAA3lB,KAAK,CAAC2iB,IAAI,CAAC+C,YAAY,CAAEP,YAAY,CAAC,CAC3DQ,QAAQ,CAACzY,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,IAAI,CAAC,CAAE;AACrCggB,QAAQ,CAACrO,WAAW,CAAG,GAAG,CAAE;AAC5BoW,WAAW,CAAChmB,GAAG,CAACie,QAAQ,CAAC,CAEzB;AACA,KAAM,CAAAqI,aAAa,CAAGR,gBAAgB,CAACzT,SAAS,CAAC,CACjD,KAAM,CAAAkU,SAAS,CAAGjX,gBAAgB,CAACgX,aAAa,CAAE,CAChDrgB,eAAe,CAAE,CAAEuJ,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/CqO,SAAS,CAAE,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEpO,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/C+E,QAAQ,CAAE,EAAE,CAAE;AACdL,OAAO,CAAE,CAAC,CAAE;AACZwb,eAAe,CAAE,CAAC,CAAE;AACpB9Z,UAAU,CAAE,MAAO;AACrB,CAAC,CAAC,CACF6e,SAAS,CAAC/gB,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,CAAC,GAAG,CAAE,IAAI,CAAC,CAAE;AACvCsoB,SAAS,CAAC3W,WAAW,CAAG,IAAI,CAAE;AAC9B2W,SAAS,CAAC3Y,KAAK,CAAC3P,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE;AAC9B+nB,WAAW,CAAChmB,GAAG,CAACumB,SAAS,CAAC,CAE1B;AACAP,WAAW,CAACxgB,QAAQ,CAACvH,GAAG,CAACuH,QAAQ,CAAC5H,CAAC,CAAE,EAAE,CAAE,CAAC4H,QAAQ,CAAC1H,CAAC,CAAC,CAErD;AACA;AACJ;AACI;AACA;AACA;AACA;AACA;AACA;AACAkoB,WAAW,CAACpW,WAAW,CAAG,GAAG,CAE7B;AACAoW,WAAW,CAACnK,QAAQ,CAAG,CACrB7O,IAAI,CAAE,aAAa,CACnBmF,OAAO,CAAEA,OAAO,CAChBE,SAAS,CAAEA,SACb,CAAC,CAED;AACAhY,KAAK,CAAC2F,GAAG,CAACgmB,WAAW,CAAC,CAEtB;AACAjjB,YAAY,CAAC9E,GAAG,CAACkU,OAAO,CAAE6T,WAAW,CAAC,CAEtCpqB,OAAO,CAACC,GAAG,CAAC,aAAawW,SAAS,KAAKiU,aAAa,UAAUnU,OAAO,UAAU3M,QAAQ,CAAC5H,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,KAAKgH,QAAQ,CAAC1H,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,WAAWyH,eAAe,CAACwK,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAExK,MAAO,CAAAuV,WAAW,CACpB,CAAE,MAAO9oB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,KAAI,CACb,CACF,CAAC,CAED;AACA,KAAM,CAAAspB,yBAAyB,CAAGA,CAACrU,OAAO,CAAEvD,WAAW,GAAK,CAC1D,KAAM,CAAAoX,WAAW,CAAGjjB,YAAY,CAAC3E,GAAG,CAAC+T,OAAO,CAAC,CAC7C,GAAI6T,WAAW,EAAI3rB,KAAK,CAAE,CACxB2rB,WAAW,CAACxgB,QAAQ,CAACvH,GAAG,CAAC2Q,WAAW,CAAChR,CAAC,CAAE,EAAE,CAAE,CAACgR,WAAW,CAAC9Q,CAAC,CAAC,CAC3D;AACA;AACAlC,OAAO,CAACC,GAAG,CAAC,oBAAoBsW,OAAO,WAAWvD,WAAW,CAAChR,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,KAAKoQ,WAAW,CAAC9Q,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAC7G,CACF,CAAC,CAED,KAAM,CAAAqU,iBAAiB,CAAG,QAAAA,CAACrN,QAAQ,CAAE6b,IAAI,CAAE5Z,KAAK,CAAwC,IAAtC,CAAA4K,SAAS,CAAAmF,SAAA,CAAAzW,MAAA,IAAAyW,SAAA,MAAAtH,SAAA,CAAAsH,SAAA,IAAG,KAAK,IAAE,CAAAiP,SAAS,CAAAjP,SAAA,CAAAzW,MAAA,IAAAyW,SAAA,MAAAtH,SAAA,CAAAsH,SAAA,IAAG,CAAC,CAAC,CACjF;AACA,GAAI,CAACnd,KAAK,CAAE,CACVuB,OAAO,CAACugB,IAAI,CAAC,oBAAoB,CAAC,CAClC,OACF,CAEA,GAAI,CACF;AACA,KAAM,CAAA0I,WAAW,CAAG5gB,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9B,KAAM,CAAA0gB,QAAQ,CAAG,GAAG6B,SAAS,CAAC3U,KAAK,EAAI,SAAS,IAAIO,SAAS,IAAI7M,QAAQ,CAAC5H,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,IAAIgH,QAAQ,CAAC1H,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,EAAE,CAEjH;AACA;AACA;AAEA;AACA,KAAM,CAAAkoB,eAAe,CAAG/B,mBAAmB,CACzCtS,SAAS,CACTuS,QAAQ,CACRC,WAAW,CACXrf,QACF,CAAC,CAED,KAAM,CAAA0f,WAAW,CAAGwB,eAAe,CAACxB,WAAW,CAE/CtpB,OAAO,CAACC,GAAG,CAAC,uBAAuBwW,SAAS,eAAeuS,QAAQ,UAAUpf,QAAQ,CAAC5H,CAAC,KAAK4H,QAAQ,CAAC1H,CAAC,UAAUonB,WAAW,CAAG,IAAI,CAAG,KAAK,EAAE,CAAC,CAE7I,GAAIA,WAAW,CAAE,CACf;AACAsB,yBAAyB,CAACE,eAAe,CAACvU,OAAO,CAAE3M,QAAQ,CAAC,CAC9D,CAAC,IAAM,CACL;AACAugB,iBAAiB,CAAC1T,SAAS,CAAE7M,QAAQ,CAAEkhB,eAAe,CAACvU,OAAO,CAAC,CACjE,CAEF,CAAE,MAAOjV,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CACpC,CACF,CAAC,CAED;AACA,KAAM,CAAAud,mBAAmB,CAAGA,CAACyB,iBAAiB,CAAEtX,aAAa,GAAK,CAChE,GAAI,CAACvK,KAAK,CAAE,CACVuB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC,CAC/B,OACF,CAEA,GAAI,CAACgf,iBAAiB,CAAE,CACtBtgB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC,CAClC,OACF,CAEA;AACA,GAAI,CAAC9C,0BAA0B,CAAE,CAC/BwB,OAAO,CAACsB,KAAK,CAAC,oBAAoB,CAAC,CACnC;AACA,KAAM,CAAAya,MAAM,CAAG,GAAI,CAAApf,UAAU,CAAC,CAAC,CAC/Bof,MAAM,CAACiM,SAAS,CAAC,GAAGpoB,QAAQ,4BAA4B,CAAC,CACtDmrB,IAAI,CAACpD,gBAAgB,EAAI,CACxBnpB,0BAA0B,CAAGmpB,gBAAgB,CAAClpB,KAAK,CACnDD,0BAA0B,CAACwT,KAAK,CAAC3P,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7C7D,0BAA0B,CAAC6F,QAAQ,CAAE8O,KAAK,EAAK,CAC7C,GAAIA,KAAK,CAAC3M,MAAM,EAAI2M,KAAK,CAACxM,QAAQ,CAAE,CAClCwM,KAAK,CAACxM,QAAQ,CAACqU,SAAS,CAAG,GAAG,CAC9B7H,KAAK,CAACxM,QAAQ,CAACsU,SAAS,CAAG,GAAG,CAC9B9H,KAAK,CAACxM,QAAQ,CAACuU,eAAe,CAAG,GAAG,CACtC,CACF,CAAC,CAAC,CACFlb,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CACrC;AACA4e,mBAAmB,CAACyB,iBAAiB,CAAEtX,aAAa,CAAC,CACvD,CAAC,CAAC,CACDgiB,KAAK,CAAC1pB,KAAK,EAAI,CACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC;AACA2pB,2BAA2B,CAAC3K,iBAAiB,CAAEtX,aAAa,CAAC,CAC/D,CAAC,CAAC,CACJ,OACF,CAEA;AACA3I,gBAAgB,CAACuE,OAAO,CAAE+a,QAAQ,EAAK,CACrC,GAAIlhB,KAAK,EAAIkhB,QAAQ,CAACxb,KAAK,CAAE,CAC3B1F,KAAK,CAAC2H,MAAM,CAACuZ,QAAQ,CAACxb,KAAK,CAAC,CAC9B,CACF,CAAC,CAAC,CACF9D,gBAAgB,CAAC4F,KAAK,CAAC,CAAC,CAExB;AACA+C,aAAa,CAACpE,OAAO,CAAC+J,YAAY,EAAI,CACpC,GAAIA,YAAY,CAACY,eAAe,GAAK,KAAK,CAAE,CAC1CvP,OAAO,CAACC,GAAG,CAAC,UAAU0O,YAAY,CAACE,IAAI,kBAAkB,CAAC,CAC1D,OACF,CAEA,GAAIF,YAAY,CAACrF,QAAQ,EAAIqF,YAAY,CAACtF,SAAS,EAAIsF,YAAY,CAACzD,OAAO,CAAE,CAC3E,KAAM,CAAAyG,QAAQ,CAAG2O,iBAAiB,CAACvR,YAAY,CAC7CC,UAAU,CAACL,YAAY,CAACtF,SAAS,CAAC,CAClC2F,UAAU,CAACL,YAAY,CAACrF,QAAQ,CAClC,CAAC,CAEDtJ,OAAO,CAACC,GAAG,CAAC,SAAS0O,YAAY,CAACE,IAAI,KAAKF,YAAY,CAACzD,OAAO,iBAAiByG,QAAQ,CAAC3P,CAAC,KAAK2P,QAAQ,CAACzP,CAAC,GAAG,CAAC,CAE7G,GAAI,CACF;AACA,GAAI,CAAC1D,0BAA0B,EAAI,CAACA,0BAA0B,CAACsD,KAAK,CAAE,CACpE,KAAM,IAAI,CAAAopB,KAAK,CAAC,cAAc,CAAC,CACjC,CAEA;AACA,KAAM,CAAAxV,iBAAiB,CAAGlX,0BAA0B,CAACsD,KAAK,CAAC,CAAC,CAE5D;AACA4T,iBAAiB,CAAC7G,IAAI,CAAG,OAAOF,YAAY,CAACE,IAAI,EAAE,CAEnD;AACA6G,iBAAiB,CAAC9L,QAAQ,CAACvH,GAAG,CAACsP,QAAQ,CAAC3P,CAAC,CAAE,EAAE,CAAE,CAAC2P,QAAQ,CAACzP,CAAC,CAAC,CAE3D;AACAwT,iBAAiB,CAAC1D,KAAK,CAAC3P,GAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEvC;AACAqT,iBAAiB,CAAC1B,WAAW,CAAG,GAAG,CAEnC;AACA0B,iBAAiB,CAACrR,QAAQ,CAAC8O,KAAK,EAAI,CAClC,GAAIA,KAAK,CAAC3M,MAAM,CAAE,CAChB2M,KAAK,CAACxM,QAAQ,CAAC4M,WAAW,CAAG,KAAK,CAClCJ,KAAK,CAACxM,QAAQ,CAACsN,OAAO,CAAG,GAAG,CAC5Bd,KAAK,CAACxM,QAAQ,CAAC4jB,IAAI,CAAG7tB,KAAK,CAAC8tB,UAAU,CACtCrX,KAAK,CAACxM,QAAQ,CAACoZ,UAAU,CAAG,IAAI,CAChC5M,KAAK,CAACxM,QAAQ,CAACugB,SAAS,CAAG,IAAI,CAC/B/T,KAAK,CAACxM,QAAQ,CAAC6M,WAAW,CAAG,IAAI,CACjCL,KAAK,CAACa,WAAW,CAAG,GAAG,CACzB,CACF,CAAC,CAAC,CAEF;AACA0B,iBAAiB,CAACuK,QAAQ,CAAG,CAC3B7O,IAAI,CAAE,cAAc,CACpBlG,OAAO,CAAEyD,YAAY,CAACzD,OAAO,CAC7B2D,IAAI,CAAEF,YAAY,CAACE,IACrB,CAAC,CAED;AACA;AAEA;AACA;AACA,KAAM,CAAAsc,oBAAoB,CAAG,GAAI,CAAAzuB,KAAK,CAACilB,aAAa,CAAC,CAAC,CACtD,KAAM,CAAAyJ,eAAe,CAAG,GAAGxrB,QAAQ,qBAAqB,CAAE;AAC1D,KAAM,CAAAyrB,eAAe,CAAG,GAAI,CAAA3uB,KAAK,CAAC0iB,iBAAiB,CAAC,CAClD3Z,GAAG,CAAE0lB,oBAAoB,CAACzQ,IAAI,CAAC0Q,eAAe,CAAC,CAC/C7X,WAAW,CAAE,IAAI,CACjBU,OAAO,CAAE,CACX,CAAC,CAAC,CACF;AACA,KAAM,CAAAqX,eAAe,CAAG,GAAI,CAAA5uB,KAAK,CAACwlB,aAAa,CAAC,CAAC,CAAE,CAAC,CAAC,CACrD,KAAM,CAAAqJ,WAAW,CAAG,GAAI,CAAA7uB,KAAK,CAAC2iB,IAAI,CAACiM,eAAe,CAAED,eAAe,CAAC,CACpE;AACAE,WAAW,CAAC3hB,QAAQ,CAACvH,GAAG,CAACsP,QAAQ,CAAC3P,CAAC,CAAC,EAAE,CAAE,GAAG,CAAE,EAAE2P,QAAQ,CAACzP,CAAC,CAAC,EAAE,CAAC,CAAC,CAC9D;AACAqpB,WAAW,CAACxZ,QAAQ,CAAC/P,CAAC,CAAG,CAACkB,IAAI,CAACC,EAAE,CAAG,CAAC,CACrC;AACAooB,WAAW,CAACvX,WAAW,CAAG,GAAG,CAC7B;AACAuX,WAAW,CAACtL,QAAQ,CAAG,CACrB7O,IAAI,CAAE,aAAa,CACnBlG,OAAO,CAAEyD,YAAY,CAACzD,OAAO,CAC7B2D,IAAI,CAAEF,YAAY,CAACE,IACrB,CAAC,CACD;AACApQ,KAAK,CAAC2F,GAAG,CAACmnB,WAAW,CAAC,CACtB;AAEA;AACAlrB,gBAAgB,CAACgC,GAAG,CAACsM,YAAY,CAACzD,OAAO,CAAE,CACzC/G,KAAK,CAAEuR,iBAAiB,CACxB/G,YAAY,CAAEA,YAAY,CAC1B/E,QAAQ,CAAE+H,QACZ,CAAC,CAAC,CAEF3R,OAAO,CAACC,GAAG,CAAC,SAAS0O,YAAY,CAACE,IAAI,KAAKF,YAAY,CAACzD,OAAO,kBAAkByG,QAAQ,CAAC3P,CAAC,KAAK,CAAC2P,QAAQ,CAACzP,CAAC,GAAG,CAAC,CACjH,CAAE,MAAOZ,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQqN,YAAY,CAACE,IAAI,YAAY,CAAEvN,KAAK,CAAC,CAC3D;AACA;AACF,CACF,CACF,CAAC,CAAC,CAEF;AACAtB,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAAC8X,IAAI,SAAS,CAAC,CAClD9X,gBAAgB,CAACuE,OAAO,CAAC,CAAC+a,QAAQ,CAAEzU,OAAO,GAAK,CAC9ClL,OAAO,CAACC,GAAG,CAAC,QAAQiL,OAAO,KAAKyU,QAAQ,CAAChR,YAAY,CAACE,IAAI,EAAE,CAAC,CAC/D,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAoc,2BAA2B,CAAGA,CAAC3K,iBAAiB,CAAEtX,aAAa,GAAK,CACxEA,aAAa,CAACpE,OAAO,CAAC+J,YAAY,EAAI,CACpC;AACA,GAAIA,YAAY,CAACY,eAAe,GAAK,KAAK,CAAE,CAC1C,OACF,CAEA,GAAIZ,YAAY,CAACrF,QAAQ,EAAIqF,YAAY,CAACtF,SAAS,EAAIsF,YAAY,CAACzD,OAAO,CAAE,CAC3E;AACA,KAAM,CAAAyG,QAAQ,CAAG2O,iBAAiB,CAACvR,YAAY,CAC7CC,UAAU,CAACL,YAAY,CAACtF,SAAS,CAAC,CAClC2F,UAAU,CAACL,YAAY,CAACrF,QAAQ,CAClC,CAAC,CAED4V,wBAAwB,CAACvQ,YAAY,CAAEgD,QAAQ,CAAE2O,iBAAiB,CAAC,CACrE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAApB,wBAAwB,CAAGA,CAACvQ,YAAY,CAAEgD,QAAQ,CAAE2O,iBAAiB,GAAK,CAC9E;AACA,KAAM,CAAA7Z,QAAQ,CAAG,GAAI,CAAA/J,KAAK,CAACyiB,WAAW,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAClD,KAAM,CAAAxY,QAAQ,CAAG,GAAI,CAAAjK,KAAK,CAAC0iB,iBAAiB,CAAC,CAC3CvT,KAAK,CAAE,QAAQ,CACf0H,WAAW,CAAE,KAAK,CAClBU,OAAO,CAAE,GACX,CAAC,CAAC,CACF,KAAM,CAAAyB,iBAAiB,CAAG,GAAI,CAAAhZ,KAAK,CAAC2iB,IAAI,CAAC5Y,QAAQ,CAAEE,QAAQ,CAAC,CAE5D;AACA+O,iBAAiB,CAAC7G,IAAI,CAAG,SAASF,YAAY,CAACE,IAAI,EAAE,CAErD;AACA6G,iBAAiB,CAAC9L,QAAQ,CAACvH,GAAG,CAACsP,QAAQ,CAAC3P,CAAC,CAAE,EAAE,CAAE,CAAC2P,QAAQ,CAACzP,CAAC,CAAC,CAE3D;AACAwT,iBAAiB,CAAC1B,WAAW,CAAG,GAAG,CAEnC;AACA0B,iBAAiB,CAACuK,QAAQ,CAAG,CAC3B7O,IAAI,CAAE,cAAc,CACpBlG,OAAO,CAAEyD,YAAY,CAACzD,OAAO,CAC7B2D,IAAI,CAAEF,YAAY,CAACE,IACrB,CAAC,CAED;AACA,KAAM,CAAA2c,gBAAgB,CAAG,GAAI,CAAA9uB,KAAK,CAACmjB,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CAC5D,KAAM,CAAA4L,gBAAgB,CAAG,GAAI,CAAA/uB,KAAK,CAAC0iB,iBAAiB,CAAC,CACnDvT,KAAK,CAAE,QAAQ,CACf0H,WAAW,CAAE,IAAI,CACjBU,OAAO,CAAE,GAAG,CAAG;AACf8L,UAAU,CAAE,KACd,CAAC,CAAC,CAEF,KAAM,CAAA2L,QAAQ,CAAG,GAAI,CAAAhvB,KAAK,CAAC2iB,IAAI,CAACmM,gBAAgB,CAAEC,gBAAgB,CAAC,CACnEC,QAAQ,CAAC7c,IAAI,CAAG,YAAYF,YAAY,CAACE,IAAI,EAAE,CAC/C6c,QAAQ,CAACzL,QAAQ,CAAG,CAClB7O,IAAI,CAAE,cAAc,CACpBlG,OAAO,CAAEyD,YAAY,CAACzD,OAAO,CAC7B2D,IAAI,CAAEF,YAAY,CAACE,IAAI,CACvB8c,UAAU,CAAE,IACd,CAAC,CAEDjW,iBAAiB,CAACtR,GAAG,CAACsnB,QAAQ,CAAC,CAE/B;AACAjtB,KAAK,CAAC2F,GAAG,CAACsR,iBAAiB,CAAC,CAE5B;AACArV,gBAAgB,CAACgC,GAAG,CAACsM,YAAY,CAACzD,OAAO,CAAE,CACzC/G,KAAK,CAAEuR,iBAAiB,CACxB/G,YAAY,CAAEA,YAAY,CAC1B/E,QAAQ,CAAE+H,QACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAia,aAAa,CAAG,GAAI,CAAAlvB,KAAK,CAACmjB,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACzD,KAAM,CAAAgM,aAAa,CAAG,GAAI,CAAAnvB,KAAK,CAAC0iB,iBAAiB,CAAC,CAAEvT,KAAK,CAAE,QAAS,CAAC,CAAC,CACtE,KAAM,CAAAigB,SAAS,CAAG,GAAI,CAAApvB,KAAK,CAAC2iB,IAAI,CAACuM,aAAa,CAAEC,aAAa,CAAC,CAC9DC,SAAS,CAACliB,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAEhC;AACAypB,SAAS,CAAC7L,QAAQ,CAAG,CACnB7O,IAAI,CAAE,cAAc,CACpBlG,OAAO,CAAEyD,YAAY,CAACzD,OAAO,CAC7B2D,IAAI,CAAEF,YAAY,CAACE,IACrB,CAAC,CAED6G,iBAAiB,CAACtR,GAAG,CAAC0nB,SAAS,CAAC,CAEhC9rB,OAAO,CAACC,GAAG,CAAC,SAAS0O,YAAY,CAACE,IAAI,KAAKF,YAAY,CAACzD,OAAO,kBAAkByG,QAAQ,CAAC3P,CAAC,KAAK,CAAC2P,QAAQ,CAACzP,CAAC,GAAG,CAAC,CACjH,CAAC,CAED;AACA,KAAM,CAAA+S,iBAAiB,CAAIL,OAAO,EAAK,CACrC,OAAOA,OAAO,EACZ,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,QAAS,MAAO,KAAKA,OAAO,EAAE,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAI,oBAAoB,CAAI+W,OAAO,EAAK,CACxC,OAAOA,OAAO,EACZ,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,QAAS,MAAO,KAAKA,OAAO,EAAE,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAxG,kBAAkB,CAAIyG,eAAe,EAAK,CAC9C;AACA,GAAI5sB,MAAM,CAACkM,0BAA0B,EAAIlM,MAAM,CAACkM,0BAA0B,CAACuB,OAAO,CAAE,CAClFmR,aAAa,CAAC5e,MAAM,CAACkM,0BAA0B,CAACuB,OAAO,CAAC,CACxDzN,MAAM,CAACkM,0BAA0B,CAACuB,OAAO,CAAG,IAAI,CAChD7M,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAEA;AACA,GAAIb,MAAM,CAACiM,mBAAmB,CAAE,CAC9BjM,MAAM,CAACiM,mBAAmB,CAACwB,OAAO,CAAG,IAAI,CAC3C,CAEA;AACAmf,eAAe,CAACpW,IAAI,GAAK,CACvB,GAAGA,IAAI,CACP3K,OAAO,CAAE,KAAK,CACdE,OAAO,CAAE,IAAI,CAAE;AACfC,MAAM,CAAE,EAAO;AACjB,CAAC,CAAC,CAAC,CAEHpL,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAChC,CAAC,CAED;AACAb,MAAM,CAAC6sB,qBAAqB,CAAI/gB,OAAO,EAAK,CAC1C,GAAI,KAAAghB,qBAAA,CAAAC,sBAAA,CACF;AACA,KAAM,CAAAhX,YAAY,CAAG9U,gBAAgB,CAACmC,GAAG,CAAC0I,OAAO,EAAI,GAAG,CAAC,CACzD,GAAI,CAACiK,YAAY,CAAE,CACjBnV,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAE4J,OAAO,CAAC,CAEtC;AACAlL,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxBI,gBAAgB,CAACuE,OAAO,CAAC,CAACwnB,KAAK,CAAElb,EAAE,GAAK,CACtClR,OAAO,CAACC,GAAG,CAAC,KAAKiR,EAAE,KAAKkb,KAAK,CAACzd,YAAY,CAACE,IAAI,EAAE,CAAC,CACpD,CAAC,CAAC,CAEF,MAAO,MAAK,CACd,CAEA;AACA,KAAM,CAAAwd,UAAU,CAAGlX,YAAY,CAAChR,KAAK,CAErC;AACA,KAAM,CAAAmoB,SAAS,CAAGhsB,kBAAkB,CAACkC,GAAG,CAAC0I,OAAO,CAAC,CACjD,KAAM,CAAAyD,YAAY,CAAGwG,YAAY,CAACxG,YAAY,CAE9C;AACA,GAAI,CAAAxD,OAAO,CAEX,GAAImhB,SAAS,EAAIA,SAAS,CAAClhB,MAAM,CAAE,CACjCD,OAAO,cACL1N,KAAA,QAAKgnB,KAAK,CAAE,CAAEra,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAEuZ,SAAS,CAAE,OAAO,CAAEsH,SAAS,CAAE,MAAO,CAAE,CAAApR,QAAA,eACpF1d,KAAA,QAAKgnB,KAAK,CAAE,CACV3Y,UAAU,CAAE,MAAM,CAClB0gB,YAAY,CAAE,KAAK,CACnB/hB,QAAQ,CAAE,MAAM,CAChBgiB,YAAY,CAAE,gBAAgB,CAC9BC,aAAa,CAAE,KACjB,CAAE,CAAAvR,QAAA,EACCxM,YAAY,CAACE,IAAI,CAAC,QAAM,CAAC3D,OAAO,CAAC,GACpC,EAAK,CAAC,cACN3N,IAAA,QAAA4d,QAAA,CACGmR,SAAS,CAAClhB,MAAM,CAAC3F,GAAG,CAAC,CAACkP,KAAK,CAAEgY,KAAK,GAAK,CACtC,GAAI,CAAAC,UAAU,CACd,OAAQjY,KAAK,CAACQ,YAAY,EACxB,IAAK,GAAG,CAAEyX,UAAU,CAAG,SAAS,CAAE,MAClC,IAAK,GAAG,CAAEA,UAAU,CAAG,SAAS,CAAE,MAClC,IAAK,GAAG,CAAE,QAASA,UAAU,CAAG,SAAS,CAAE,MAC7C,CAEA,mBACEnvB,KAAA,QAAiBgnB,KAAK,CAAE,CACtB+H,YAAY,CAAE,KAAK,CACnBniB,eAAe,CAAE,uBAAuB,CACxCD,OAAO,CAAE,KAAK,CACdG,YAAY,CAAE,KAAK,CACnBE,QAAQ,CAAE,MACZ,CAAE,CAAA0Q,QAAA,eACA5d,IAAA,QAAKknB,KAAK,CAAE,CAAE3Y,UAAU,CAAE,MAAO,CAAE,CAAAqP,QAAA,CAChClG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC,CAC9B,CAAC,cACNnX,KAAA,QAAKgnB,KAAK,CAAE,CAAExa,OAAO,CAAE,MAAM,CAAE4iB,cAAc,CAAE,eAAgB,CAAE,CAAA1R,QAAA,eAC/D5d,IAAA,SAAA4d,QAAA,CAAM,gBAAI,CAAM,CAAC,cACjB5d,IAAA,SAAMknB,KAAK,CAAE,CACX5Y,KAAK,CAAE+gB,UAAU,CACjB9gB,UAAU,CAAE,MAAM,CAClBzB,eAAe,CAAE,iBAAiB,CAClCD,OAAO,CAAE,OAAO,CAChBG,YAAY,CAAE,KAChB,CAAE,CAAA4Q,QAAA,CACCxG,KAAK,CAACQ,YAAY,GAAK,GAAG,CAAG,IAAI,CAAGR,KAAK,CAACQ,YAAY,GAAK,GAAG,CAAG,IAAI,CAAG,IAAI,CACzE,CAAC,EACJ,CAAC,cACN1X,KAAA,QAAKgnB,KAAK,CAAE,CAAExa,OAAO,CAAE,MAAM,CAAE4iB,cAAc,CAAE,eAAgB,CAAE,CAAA1R,QAAA,eAC/D5d,IAAA,SAAA4d,QAAA,CAAM,sBAAK,CAAM,CAAC,cAClB1d,KAAA,SAAMgnB,KAAK,CAAE,CAAE3Y,UAAU,CAAE,MAAO,CAAE,CAAAqP,QAAA,EAAExG,KAAK,CAACS,UAAU,CAAC,SAAE,EAAM,CAAC,EAC7D,CAAC,GAzBEuX,KA0BL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cACNlvB,KAAA,QAAKgnB,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAEriB,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAO,CAAE,CAAAsP,QAAA,EAAC,4BAC3D,CAAC,GAAI,CAAA9S,IAAI,CAAC,CAAC,CAAC0kB,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CACH,CAAC,IAAM,CACL5hB,OAAO,cACL1N,KAAA,QAAKgnB,KAAK,CAAE,CAAEra,OAAO,CAAE,KAAK,CAAE+a,QAAQ,CAAE,OAAQ,CAAE,CAAAhK,QAAA,eAChD5d,IAAA,QAAKknB,KAAK,CAAE,CAAE3Y,UAAU,CAAE,MAAM,CAAE0gB,YAAY,CAAE,KAAM,CAAE,CAAArR,QAAA,CAAExM,YAAY,CAACE,IAAI,CAAM,CAAC,cAClFpR,KAAA,QAAA0d,QAAA,EAAK,kBAAM,CAACjQ,OAAO,EAAM,CAAC,cAC1B3N,IAAA,QAAA4d,QAAA,CAAK,8DAAU,CAAK,CAAC,EAClB,CACN,CACH,CAEA;AACA,KAAM,CAAA6R,OAAO,CAAG5tB,MAAM,CAACyZ,UAAU,CAAG,CAAC,CAAE,GAAG,CAC1C,KAAM,CAAAoU,OAAO,CAAG7tB,MAAM,CAAC0Z,WAAW,CAAG,CAAC,CAAE,GAAG,CAE3C;AACA,KAAM,CAAAkT,eAAe,EAAAE,qBAAA,CAAGnG,QAAQ,CAACqB,aAAa,CAAC,OAAO,CAAC,UAAA8E,qBAAA,kBAAAC,sBAAA,CAA/BD,qBAAA,CAAiCgB,gBAAgB,UAAAf,sBAAA,iBAAjDA,sBAAA,CAAmDnhB,sBAAsB,CAEjG,GAAIghB,eAAe,CAAE,CACnB;AACAA,eAAe,CAAC,CACd/gB,OAAO,CAAE,IAAI,CACbC,OAAO,CAAEA,OAAO,CAChBtB,QAAQ,CAAE,CAAE5H,CAAC,CAAEgrB,OAAO,CAAE9qB,CAAC,CAAE+qB,OAAQ,CAAC,CACpC9hB,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAE,CAAAkhB,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAElhB,MAAM,GAAI,EAC/B,CAAC,CAAC,CAEFpL,OAAO,CAACC,GAAG,CAAC,SAAS0O,YAAY,CAACE,IAAI,KAAK3D,OAAO,UAAU,CAAC,CAC7D,MAAO,KAAI,CACb,CAAC,IAAM,CACL;AACA,KAAM,CAAAiiB,OAAO,CAAGpH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC7CmH,OAAO,CAAC1I,KAAK,CAAC7a,QAAQ,CAAG,UAAU,CACnCujB,OAAO,CAAC1I,KAAK,CAAC3a,IAAI,CAAG,GAAGkjB,OAAO,IAAI,CACnCG,OAAO,CAAC1I,KAAK,CAAChZ,GAAG,CAAG,GAAGwhB,OAAO,IAAI,CAClCE,OAAO,CAAC1I,KAAK,CAAC1a,SAAS,CAAG,wBAAwB,CAClDojB,OAAO,CAAC1I,KAAK,CAACza,MAAM,CAAG,MAAM,CAC7BmjB,OAAO,CAAC1I,KAAK,CAACpa,eAAe,CAAG,qBAAqB,CACrD8iB,OAAO,CAAC1I,KAAK,CAAC5Y,KAAK,CAAG,OAAO,CAC7BshB,OAAO,CAAC1I,KAAK,CAACla,YAAY,CAAG,KAAK,CAClC4iB,OAAO,CAAC1I,KAAK,CAAC/Z,SAAS,CAAG,8BAA8B,CACxDyiB,OAAO,CAAC1I,KAAK,CAACra,OAAO,CAAG,KAAK,CAC7B+iB,OAAO,CAAC1I,KAAK,CAACU,QAAQ,CAAG,OAAO,CAChCgI,OAAO,CAAC1I,KAAK,CAACha,QAAQ,CAAG,MAAM,CAE/B0iB,OAAO,CAACC,SAAS,CAAG;AAC1B;AACA,YAAYze,YAAY,CAACE,IAAI,SAAS3D,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAeohB,SAAS,CAAG,UAAU,CAAG,YAAY;AACpD;AACA;AACA,OAAO,CAEDvG,QAAQ,CAACsH,IAAI,CAAC/T,WAAW,CAAC6T,OAAO,CAAC,CAElC;AACA,KAAM,CAAAG,WAAW,CAAGH,OAAO,CAAC/F,aAAa,CAAC,QAAQ,CAAC,CACnD,GAAIkG,WAAW,CAAE,CACfA,WAAW,CAAC7P,gBAAgB,CAAC,OAAO,CAAE,IAAM,CAC1CsI,QAAQ,CAACsH,IAAI,CAAC9O,WAAW,CAAC4O,OAAO,CAAC,CACpC,CAAC,CAAC,CACJ,CAEAntB,OAAO,CAACC,GAAG,CAAC,gBAAgB0O,YAAY,CAACE,IAAI,KAAK3D,OAAO,OAAO,CAAC,CACjE,MAAO,KAAI,CACb,CACF,CAAE,MAAO5J,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACAlC,MAAM,CAACmuB,iBAAiB,CAAG,IAAM,CAC/BvtB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CAErB,GAAI,CAACI,gBAAgB,EAAIA,gBAAgB,CAAC8X,IAAI,GAAK,CAAC,CAAE,CACpDnY,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,MAAO,EAAE,CACX,CAEA,KAAM,CAAAutB,IAAI,CAAG,EAAE,CACfntB,gBAAgB,CAACuE,OAAO,CAAC,CAACwnB,KAAK,CAAElb,EAAE,GAAK,CACtClR,OAAO,CAACC,GAAG,CAAC,SAASiR,EAAE,SAASkb,KAAK,CAACzd,YAAY,CAACE,IAAI,EAAE,CAAC,CAC1D2e,IAAI,CAACjY,IAAI,CAAC,CACRrE,EAAE,CACFrC,IAAI,CAAEud,KAAK,CAACzd,YAAY,CAACE,IAAI,CAC7BjF,QAAQ,CAAEwiB,KAAK,CAACxiB,QAClB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAA4jB,IAAI,CACb,CAAC,CAGD;AACApuB,MAAM,CAACqQ,qBAAqB,CAAIvE,OAAO,EAAK,CAC1C,GAAI,CACF;AACAA,OAAO,CAAGuK,MAAM,CAACvK,OAAO,CAAC,CAEzBlL,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEiL,OAAO,CAAE,KAAK,CAAE,MAAO,CAAAA,OAAO,CAAC,CAC/ElL,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEI,gBAAgB,CAAC8X,IAAI,CAAC,CAE3D;AACA,GAAI,CAAAhD,YAAY,CAAG9U,gBAAgB,CAACmC,GAAG,CAAC0I,OAAO,CAAC,CAChD,GAAI,CAACiK,YAAY,CAAE,CACjB;AACA,KAAM,CAAAsY,SAAS,CAAGpY,QAAQ,CAACnK,OAAO,CAAC,CACnCiK,YAAY,CAAG9U,gBAAgB,CAACmC,GAAG,CAACirB,SAAS,CAAC,CAE9C,GAAItY,YAAY,CAAE,CAChBnV,OAAO,CAACC,GAAG,CAAC,UAAUwtB,SAAS,SAAS,CAAC,CACzCviB,OAAO,CAAGuiB,SAAS,CAAE;AACvB,CACF,CAEA,GAAI,CAACtY,YAAY,CAAE,CACjBnV,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAE4J,OAAO,CAAC,CACtC,MAAO,MAAK,CACd,CAEA,KAAM,CAAAohB,SAAS,CAAGhsB,kBAAkB,CAACkC,GAAG,CAAC0I,OAAO,CAAC,CACjD,KAAM,CAAAyD,YAAY,CAAGwG,YAAY,CAACxG,YAAY,CAE9C;AACA,KAAM,CAAA+e,YAAY,CAAGpB,SAAS,EAAIA,SAAS,CAAClhB,MAAM,EAAIkhB,SAAS,CAAClhB,MAAM,CAACjG,MAAM,CAAG,CAAC,CAEjF,GAAI,CAAAgG,OAAO,CAEX;AACA,KAAM,CAAAwiB,YAAY,CAAG,CACnB/jB,QAAQ,CAAE,UAAU,CACpB6B,GAAG,CAAE,KAAK,CACV2Z,KAAK,CAAE,MAAM,CACb1Z,KAAK,CAAE,MAAM,CACboG,MAAM,CAAE,MAAM,CACd7H,OAAO,CAAE,MAAM,CACf4iB,cAAc,CAAE,QAAQ,CACxBe,UAAU,CAAE,QAAQ,CACpBrjB,YAAY,CAAE,KAAK,CACnB8a,UAAU,CAAE,iBAAiB,CAC7Brb,MAAM,CAAE,EACV,CAAC,CAED;AACA,KAAM,CAAA6jB,WAAW,CAAGA,CAAA,gBAClBtwB,IAAA,QAAKknB,KAAK,CAAEkJ,YAAa,CAAAxS,QAAA,cACvB1d,KAAA,QAAKgnB,KAAK,CAAE,CACVxa,OAAO,CAAE,MAAM,CACf6jB,aAAa,CAAE,QAAQ,CACvBF,UAAU,CAAE,QAAQ,CACpB7jB,SAAS,CAAE,cACb,CAAE,CAAAoR,QAAA,eACA5d,IAAA,SAAMknB,KAAK,CAAE,CACX5Y,KAAK,CAAE,SAAS,CAChBpB,QAAQ,CAAE,MAAM,CAChBqB,UAAU,CAAE,MAAM,CAClBF,UAAU,CAAE,MACd,CAAE,CAAAuP,QAAA,CAAC,GAAC,CAAM,CAAC,cACX5d,IAAA,SAAMknB,KAAK,CAAE,CACX/Y,KAAK,CAAE,CAAC,CACRoG,MAAM,CAAE,CAAC,CACTic,UAAU,CAAE,uBAAuB,CACnCC,WAAW,CAAE,uBAAuB,CACpCvB,YAAY,CAAE,oBAAoB,CAClCK,SAAS,CAAE,MACb,CAAE,CAAO,CAAC,EACP,CAAC,CACH,CACN,CAED,GAAIY,YAAY,CAAE,CAChB;AACA,KAAM,CAAAO,QAAQ,CAAG,CACf,GAAG,CAAE,CAAEC,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,MAAO,CAAC,CAAE,GAAG,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,UAAW,CAAC,CAAE,GAAG,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,OAAQ,CAAC,CACtG,GAAG,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,MAAO,CAAC,CAAE,GAAG,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,UAAW,CAAC,CAAE,GAAG,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,OAAQ,CAAC,CACtG,GAAG,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,MAAO,CAAC,CAAE,IAAI,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,UAAW,CAAC,CAAE,IAAI,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,OAAQ,CAAC,CACxG,IAAI,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,MAAO,CAAC,CAAE,IAAI,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,UAAW,CAAC,CAAE,IAAI,CAAE,CAAE8c,GAAG,CAAE,GAAG,CAAE9c,IAAI,CAAE,OAAQ,CAC1G,CAAC,CAED,KAAM,CAAA+c,SAAS,CAAG,CAAC,MAAM,CAAE,UAAU,CAAE,OAAO,CAAC,CAC/C,KAAM,CAAAC,QAAQ,CAAG,CAAEC,CAAC,CAAE,SAAS,CAAEC,CAAC,CAAE,SAAS,CAAEC,CAAC,CAAE,SAAU,CAAC,CAC7D,KAAM,CAAAC,OAAO,CAAG,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,CAAC,CAE9CtC,SAAS,CAAClhB,MAAM,CAACxG,OAAO,CAAC+P,KAAK,EAAI,CAChC,KAAM,CAAAlP,GAAG,CAAGwoB,QAAQ,CAACtZ,KAAK,CAACC,OAAO,CAAC,CACnC,GAAInP,GAAG,CAAE,CACP+oB,OAAO,CAAC/oB,GAAG,CAACyoB,GAAG,CAAC,CAACzoB,GAAG,CAAC2L,IAAI,CAAC,CAAG,CAC3BvF,KAAK,CAAEuiB,QAAQ,CAACzZ,KAAK,CAACQ,YAAY,CAAC,EAAI,MAAM,CAC7CC,UAAU,CAAET,KAAK,CAACS,UACpB,CAAC,CACH,CACF,CAAC,CAAC,CAEFjK,OAAO,cACL1N,KAAA,QAAKgnB,KAAK,CAAE,CAAEra,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAE2Z,UAAU,CAAE,kBAAkB,CAAEzb,QAAQ,CAAE,UAAW,CAAE,CAAAuR,QAAA,eACnG5d,IAAA,CAACswB,WAAW,GAAE,CAAC,cACfpwB,KAAA,QAAKgnB,KAAK,CAAE,CAAE3Y,UAAU,CAAE,MAAM,CAAE0gB,YAAY,CAAE,KAAK,CAAE/hB,QAAQ,CAAE,MAAM,CAAE+b,SAAS,CAAE,QAAS,CAAE,CAAArL,QAAA,EAAExM,YAAY,CAACE,IAAI,CAAC,cAAE,EAAK,CAAC,cAC3HpR,KAAA,QAAKgnB,KAAK,CAAE,CACVxa,OAAO,CAAE,MAAM,CACf4kB,gBAAgB,CAAE,gBAAgB,CAClCC,mBAAmB,CAAE,gBAAgB,CACrCjC,cAAc,CAAE,QAAQ,CACxBe,UAAU,CAAE,QAAQ,CACpBvI,UAAU,CAAE,wBAAwB,CACpC9a,YAAY,CAAE,KAAK,CACnBwkB,MAAM,CAAE,QAAQ,CAChBnlB,QAAQ,CAAE,UACZ,CAAE,CAAAuR,QAAA,eAGA5d,IAAA,QAAKknB,KAAK,CAAE,CAAEuK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEzI,SAAS,CAAE,QAAQ,CAAEvc,OAAO,CAAE,MAAM,CAAE4iB,cAAc,CAAE,QAAQ,CAAEnhB,KAAK,CAAE,MAAO,CAAE,CAAAyP,QAAA,CACtHgT,SAAS,CAAC1oB,GAAG,CAAC,CAAC2L,IAAI,CAAEub,KAAK,GAAK,CAC9B;AACA,GAAI,CAAAuC,YAAY,CAAGvC,KAAK,CACxB,GAAIA,KAAK,GAAK,CAAC,CAAEuC,YAAY,CAAG,CAAC,CAAC,IAC7B,IAAIvC,KAAK,GAAK,CAAC,CAAEuC,YAAY,CAAG,CAAC,CAEtC,KAAM,CAAAC,WAAW,CAAGhB,SAAS,CAACe,YAAY,CAAC,CAE3C;AACA,KAAM,CAAAE,WAAW,CAAG,CAAC,CAAC,CACtB,GAAID,WAAW,GAAK,MAAM,CAAE,CAAE;AAC5BC,WAAW,CAACC,WAAW,CAAG,KAAK,CACjC,CAAC,IAAM,IAAIF,WAAW,GAAK,UAAU,CAAE,CAAE;AACvCC,WAAW,CAACE,UAAU,CAAG,MAAM,CAC/BF,WAAW,CAACC,WAAW,CAAG,MAAM,CAClC,CAAC,IAAM,IAAIF,WAAW,GAAK,OAAO,CAAE,CAAE;AACpCC,WAAW,CAACE,UAAU,CAAG,KAAK,CAChC,CAEA,MAAO,CAAAd,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,eAC3B;AACA;AACA;AACA;AACA;AACA;AACA1xB,KAAA,QAAuBgnB,KAAK,CAAE,CAAC4K,WAAW,CAAEF,WAAW,GAAK,MAAM,CAAG,CAAC,CAAG,MAAM,CAAEllB,OAAO,CAAE,MAAM,CAAE6jB,aAAa,CAAE,QAAQ,CAAEF,UAAU,CAAE,QAAQ,CAAE,CAAAzS,QAAA,eAC/I5d,IAAA,QAAKknB,KAAK,CAAE,CAACha,QAAQ,CAAC,MAAM,CAAEoB,KAAK,CAAE2iB,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAACtjB,KAAK,CAAEC,UAAU,CAAC,MAAM,CAAE0gB,YAAY,CAAE,KAAK,CAAE,CAAArR,QAAA,CAAEqT,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAC/Z,UAAU,CAAM,CAAC,cACrJ7X,IAAA,SAAMknB,KAAK,CAAE,CAAC5Y,KAAK,CAAE2iB,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAACtjB,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEmB,UAAU,CAAE,MAAM,CAAE,CAAAuP,QAAA,CACrFgU,WAAW,GAAK,MAAM,CAAG,WAAW,CAAGA,WAAW,GAAK,UAAU,CAAG,WAAW,CAAG,WAAW,CAC1F,CAAC,GAJCA,WAKL,CACN,CACH,CAAC,CAAC,CACC,CAAC,cAGN5xB,IAAA,QAAKknB,KAAK,CAAE,CAAEuK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEzI,SAAS,CAAE,QAAQ,CAAEvc,OAAO,CAAE,MAAM,CAAE4iB,cAAc,CAAE,QAAQ,CAAEnhB,KAAK,CAAE,MAAO,CAAE,CAAAyP,QAAA,CACtHgT,SAAS,CAAC1oB,GAAG,CAAC2L,IAAI,EAAIod,OAAO,CAACG,CAAC,CAACvd,IAAI,CAAC,eACpC3T,KAAA,QAAgBgnB,KAAK,CAAE,CAAC4K,WAAW,CAAEje,IAAI,GAAK,OAAO,CAAG,CAAC,CAAG,MAAM,CAAEnH,OAAO,CAAE,MAAM,CAAE6jB,aAAa,CAAE,QAAQ,CAAEF,UAAU,CAAE,QAAQ,CAAE,CAAAzS,QAAA,eAClI5d,IAAA,SAAMknB,KAAK,CAAE,CAAC5Y,KAAK,CAAE2iB,OAAO,CAACG,CAAC,CAACvd,IAAI,CAAC,CAACvF,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEmB,UAAU,CAAE,MAAM,CAAE,CAAAuP,QAAA,CAC9E/J,IAAI,GAAK,MAAM,CAAG,WAAW,CAAGA,IAAI,GAAK,UAAU,CAAG,WAAW,CAAG,WAAW,CAC5E,CAAC,cACP7T,IAAA,QAAKknB,KAAK,CAAE,CAACha,QAAQ,CAAC,MAAM,CAAEoB,KAAK,CAAE2iB,OAAO,CAACG,CAAC,CAACvd,IAAI,CAAC,CAACvF,KAAK,CAAEC,UAAU,CAAC,MAAM,CAAEghB,SAAS,CAAE,KAAK,CAAE,CAAA3R,QAAA,CAAEqT,OAAO,CAACG,CAAC,CAACvd,IAAI,CAAC,CAACgE,UAAU,CAAM,CAAC,GAJ5HhE,IAKL,CACN,CAAC,CACC,CAAC,cAIN7T,IAAA,QAAKknB,KAAK,CAAE,CAAEuK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEzI,SAAS,CAAE,QAAS,CAAE,CAAArL,QAAA,CAC5DgT,SAAS,CAAC1oB,GAAG,CAAC,CAAC2L,IAAI,CAAEub,KAAK,GAAK,CAC9B;AACA,GAAI,CAAAuC,YAAY,CAAGvC,KAAK,CACxB,GAAIA,KAAK,GAAK,CAAC,CAAEuC,YAAY,CAAG,CAAC,CAAC,IAC7B,IAAIvC,KAAK,GAAK,CAAC,CAAEuC,YAAY,CAAG,CAAC,CAEtC,KAAM,CAAAC,WAAW,CAAGhB,SAAS,CAACe,YAAY,CAAC,CAE3C,MAAO,CAAAV,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,eAC3B1xB,KAAA,QAAuBgnB,KAAK,CAAE,CAAC+H,YAAY,CAAC,KAAK,CAAEviB,OAAO,CAAE,MAAM,CAAE2jB,UAAU,CAAE,QAAQ,CAAEf,cAAc,CAAE,YAAY,CAAE,CAAA1R,QAAA,eACtH5d,IAAA,SAAMknB,KAAK,CAAE,CAAC5Y,KAAK,CAAE2iB,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAACtjB,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEmB,UAAU,CAAE,MAAM,CAAE,CAAAuP,QAAA,CACrFgU,WAAW,GAAK,MAAM,CAAG,WAAW,CAAGA,WAAW,GAAK,UAAU,CAAG,WAAW,CAAG,WAAW,CAC1F,CAAC,cACP5xB,IAAA,QAAKknB,KAAK,CAAE,CACVha,QAAQ,CAAC,MAAM,CACfoB,KAAK,CAAE2iB,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAACtjB,KAAK,CACnCC,UAAU,CAAC,MAAM,CACjBwjB,UAAU,CAAE,KACd,CAAE,CAAAnU,QAAA,CAAEqT,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAC/Z,UAAU,CAAM,CAAC,GATpC+Z,WAUL,CACN,CACH,CAAC,CAAC,CACC,CAAC,cAGN5xB,IAAA,QAAKknB,KAAK,CAAE,CAAEuK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEzI,SAAS,CAAE,QAAS,CAAE,CAAArL,QAAA,CAC5DgT,SAAS,CAAC1oB,GAAG,CAAC2L,IAAI,EAAIod,OAAO,CAACI,CAAC,CAACxd,IAAI,CAAC,eACpC3T,KAAA,QAAgBgnB,KAAK,CAAE,CAAC+H,YAAY,CAAC,KAAK,CAAEviB,OAAO,CAAE,MAAM,CAAE2jB,UAAU,CAAE,QAAQ,CAAEf,cAAc,CAAE,UAAU,CAAE,CAAA1R,QAAA,eAC7G5d,IAAA,QAAKknB,KAAK,CAAE,CACVha,QAAQ,CAAC,MAAM,CACfoB,KAAK,CAAE2iB,OAAO,CAACI,CAAC,CAACxd,IAAI,CAAC,CAACvF,KAAK,CAC5BC,UAAU,CAAC,MAAM,CACjBujB,WAAW,CAAE,KACf,CAAE,CAAAlU,QAAA,CAAEqT,OAAO,CAACI,CAAC,CAACxd,IAAI,CAAC,CAACgE,UAAU,CAAM,CAAC,cACrC7X,IAAA,SAAMknB,KAAK,CAAE,CAAC5Y,KAAK,CAAE2iB,OAAO,CAACI,CAAC,CAACxd,IAAI,CAAC,CAACvF,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEmB,UAAU,CAAE,MAAM,CAAE,CAAAuP,QAAA,CAC9E/J,IAAI,GAAK,MAAM,CAAG,WAAW,CAAGA,IAAI,GAAK,UAAU,CAAG,WAAW,CAAG,WAAW,CAC5E,CAAC,GATCA,IAUL,CACN,CAAC,CACC,CAAC,EACH,CAAC,cACN3T,KAAA,QAAKgnB,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAEriB,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAM,CAAE2a,SAAS,CAAE,QAAS,CAAE,CAAArL,QAAA,EAAC,4BAChF,CAAC,GAAI,CAAA9S,IAAI,CAAC,CAAC,CAAC0kB,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CACH,CAAC,IAAM,CACL;AACA5hB,OAAO,cACL1N,KAAA,QAAKgnB,KAAK,CAAE,CAAEra,OAAO,CAAE,MAAM,CAAEsB,KAAK,CAAE,OAAO,CAAE2Z,UAAU,CAAE,kBAAkB,CAAEzb,QAAQ,CAAE,UAAW,CAAE,CAAAuR,QAAA,eACpG5d,IAAA,CAACswB,WAAW,GAAE,CAAC,cACftwB,IAAA,QAAKknB,KAAK,CAAE,CAAE3Y,UAAU,CAAE,MAAM,CAAE0gB,YAAY,CAAE,MAAM,CAAE/hB,QAAQ,CAAE,MAAM,CAAE+b,SAAS,CAAE,QAAS,CAAE,CAAArL,QAAA,CAAExM,YAAY,CAACE,IAAI,CAAM,CAAC,cAC1HtR,IAAA,QAAKknB,KAAK,CAAE,CACV+B,SAAS,CAAE,QAAQ,CACnBpc,OAAO,CAAE,QAAQ,CACjByB,KAAK,CAAE,SAAS,CAChBpB,QAAQ,CAAE,MAAM,CAChBqB,UAAU,CAAE,MAAM,CAClBuZ,UAAU,CAAE,uBAAuB,CACnC9a,YAAY,CAAE,KAAK,CACnBiiB,YAAY,CAAE,MAChB,CAAE,CAAArR,QAAA,CAAC,8DAEH,CAAK,CAAC,cACN1d,KAAA,QAAKgnB,KAAK,CAAE,CAAEha,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAM,CAAE2a,SAAS,CAAE,QAAS,CAAE,CAAArL,QAAA,EAAC,kBAC9D,CAACjQ,OAAO,EACX,CAAC,cACNzN,KAAA,QAAKgnB,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAEriB,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAM,CAAE2a,SAAS,CAAE,QAAS,CAAE,CAAArL,QAAA,EAAC,4BAChF,CAAC,GAAI,CAAA9S,IAAI,CAAC,CAAC,CAAC0kB,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CACH,CAEA;AACA,KAAM,CAAA/qB,CAAC,CAAG,GAAG,CACb,KAAM,CAAAE,CAAC,CAAG,GAAG,CAEb;AACA,GAAI9C,MAAM,CAACiM,mBAAmB,CAAE,CAC9BjM,MAAM,CAACiM,mBAAmB,CAACwB,OAAO,CAAG3B,OAAO,CAC9C,CAEA;AACA,GAAI9L,MAAM,CAACkM,0BAA0B,EAAIlM,MAAM,CAACkM,0BAA0B,CAACuB,OAAO,CAAE,CAClFmR,aAAa,CAAC5e,MAAM,CAACkM,0BAA0B,CAACuB,OAAO,CAAC,CACxDzN,MAAM,CAACkM,0BAA0B,CAACuB,OAAO,CAAG,IAAI,CAClD,CAEA;AACA,GAAIzN,MAAM,CAACmM,uBAAuB,CAAE,CAClCnM,MAAM,CAACmM,uBAAuB,CAAC,CAC7BN,OAAO,CAAE,IAAI,CACbC,OAAO,CAAEA,OAAO,CAChBtB,QAAQ,CAAE,CAAE5H,CAAC,CAAEE,CAAE,CAAC,CAClBiJ,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAE,CAAAkhB,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAElhB,MAAM,GAAI,EAC/B,CAAC,CAAC,CAEF;AACA,GAAIhM,MAAM,CAACkM,0BAA0B,CAAE,CACrClM,MAAM,CAACkM,0BAA0B,CAACuB,OAAO,CAAG8Q,WAAW,CAAC,IAAM,CAC5Dve,MAAM,CAACqQ,qBAAqB,CAACvE,OAAO,CAAC,CACvC,CAAC,CAAE,IAAI,CAAC,CACV,CAEA,MAAO,KAAI,CACb,CAAC,IAAM,CACLlL,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC,CAC7C,MAAO,MAAK,CACd,CACF,CAAE,MAAOA,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAAC,CAID;AACA,KAAM,CAAAiuB,yBAAyB,CAAIjrB,MAAM,EAAK,CAC5C,GAAI,CAAAuI,OAAO,CAAGvI,MAAM,CAEpB;AACA,GAAIuI,OAAO,EAAIA,OAAO,CAACoT,QAAQ,EAAIpT,OAAO,CAACoT,QAAQ,CAAC7O,IAAI,GAAK,cAAc,CAAE,CAC3EpR,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE4M,OAAO,CAACgC,IAAI,EAAI,KAAK,CAAC,CAChD,MAAO,CAAAhC,OAAO,CAChB,CAEA;AACA,MAAOA,OAAO,EAAIA,OAAO,CAAC1G,MAAM,CAAE,CAChC0G,OAAO,CAAGA,OAAO,CAAC1G,MAAM,CACxB,GAAI0G,OAAO,CAACoT,QAAQ,EAAIpT,OAAO,CAACoT,QAAQ,CAAC7O,IAAI,GAAK,cAAc,CAAE,CAChEpR,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE4M,OAAO,CAACgC,IAAI,EAAI,KAAK,CAAC,CAChD,MAAO,CAAAhC,OAAO,CAChB,CACF,CAEA,MAAO,KAAI,CACb,CAAC,CAED;AACAzN,MAAM,CAACowB,kBAAkB,CAAG,CAACxtB,CAAC,CAAEE,CAAC,GAAK,CACpC,GAAI,CACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE+B,CAAC,CAAEE,CAAC,CAAC,CAEnC;AACA,KAAM,CAAA4jB,MAAM,CAAGC,QAAQ,CAACqB,aAAa,CAAC,QAAQ,CAAC,CAC/C,GAAI,CAACtB,MAAM,CAAE,CACX9lB,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC,CACrC,MAAO,MAAK,CACd,CAEA;AACA,GAAI,CAAC7C,KAAK,EAAI,CAACmM,SAAS,CAACiC,OAAO,CAAE,CAChC7M,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC,CAChC,MAAO,MAAK,CACd,CAEA;AACA,GAAIU,CAAC,GAAKsS,SAAS,EAAIpS,CAAC,GAAKoS,SAAS,CAAE,CACtCtS,CAAC,CAAG5C,MAAM,CAACyZ,UAAU,CAAG,CAAC,CACzB3W,CAAC,CAAG9C,MAAM,CAAC0Z,WAAW,CAAG,CAAC,CAC5B,CAEA;AACA,KAAM,CAAAwK,IAAI,CAAGwC,MAAM,CAACvC,qBAAqB,CAAC,CAAC,CAC3C,KAAM,CAAAC,MAAM,CAAI,CAACxhB,CAAC,CAAGshB,IAAI,CAACxZ,IAAI,EAAIgc,MAAM,CAACpC,WAAW,CAAI,CAAC,CAAG,CAAC,CAC7D,KAAM,CAAAC,MAAM,CAAG,EAAE,CAACzhB,CAAC,CAAGohB,IAAI,CAAC7X,GAAG,EAAIqa,MAAM,CAACjC,YAAY,CAAC,CAAG,CAAC,CAAG,CAAC,CAE9D7jB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEujB,MAAM,CAAEG,MAAM,CAAC,CAErC;AACA,KAAM,CAAAG,SAAS,CAAG,GAAI,CAAApnB,KAAK,CAACqnB,SAAS,CAAC,CAAC,CACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,CAAG,CAAC,CACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,CAAG,CAAC,CAEnC,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAA1nB,KAAK,CAAC2nB,OAAO,CAACb,MAAM,CAAEG,MAAM,CAAC,CACrDG,SAAS,CAACQ,aAAa,CAACF,WAAW,CAAExZ,SAAS,CAACiC,OAAO,CAAC,CAEvD;AACA,KAAM,CAAA4iB,mBAAmB,CAAG,EAAE,CAC9BpvB,gBAAgB,CAACuE,OAAO,CAAC,CAAC+a,QAAQ,CAAEzU,OAAO,GAAK,CAC9C,GAAIyU,QAAQ,CAACxb,KAAK,CAAE,CAClBsrB,mBAAmB,CAACla,IAAI,CAACoK,QAAQ,CAACxb,KAAK,CAAC,CACxCnE,OAAO,CAACC,GAAG,CAAC,SAASiL,OAAO,QAAQ,CAAC,CACvC,CACF,CAAC,CAAC,CAEF;AACAlL,OAAO,CAACC,GAAG,CAAC,QAAQwvB,mBAAmB,CAACtqB,MAAM,YAAY,CAAC,CAC3D,KAAM,CAAAuqB,YAAY,CAAG5L,SAAS,CAACU,gBAAgB,CAACiL,mBAAmB,CAAE,IAAI,CAAC,CAE1E,GAAIC,YAAY,CAACvqB,MAAM,CAAG,CAAC,CAAE,CAC3BnF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1ByvB,YAAY,CAAC9qB,OAAO,CAAC,CAAC+qB,SAAS,CAAE/gB,CAAC,GAAK,CACrC5O,OAAO,CAACC,GAAG,CAAC,MAAM2O,CAAC,GAAG,CAAE+gB,SAAS,CAACrrB,MAAM,CAACuK,IAAI,EAAI,KAAK,CAC1C,KAAK,CAAE8gB,SAAS,CAACltB,QAAQ,CACzB,WAAW,CAAEktB,SAAS,CAACrrB,MAAM,CAACsF,QAAQ,CAACyF,OAAO,CAAC,CAAC,CAChD,WAAW,CAAEsgB,SAAS,CAACrrB,MAAM,CAAC2b,QAAQ,CAAC,CAEnD;AACA,KAAM,CAAA7B,GAAG,CAAGmR,yBAAyB,CAACI,SAAS,CAACrrB,MAAM,CAAC,CACvD,GAAI8Z,GAAG,EAAIA,GAAG,CAAC6B,QAAQ,EAAI7B,GAAG,CAAC6B,QAAQ,CAAC7O,IAAI,GAAK,cAAc,CAAE,CAC/DpR,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEme,GAAG,CAAC6B,QAAQ,CAAC/U,OAAO,CAAC,CAC/C,CACF,CAAC,CAAC,CAEF,MAAO,KAAI,CACb,CAEA;AACAlL,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B,KAAM,CAAA2vB,eAAe,CAAG9L,SAAS,CAACU,gBAAgB,CAAC/lB,KAAK,CAAC0c,QAAQ,CAAE,IAAI,CAAC,CAExEnb,OAAO,CAACC,GAAG,CAAC,WAAW2vB,eAAe,CAACzqB,MAAM,MAAM,CAAC,CACpDyqB,eAAe,CAAChrB,OAAO,CAAC,CAAC+qB,SAAS,CAAE/gB,CAAC,GAAK,CACxC,KAAM,CAAAwP,GAAG,CAAGuR,SAAS,CAACrrB,MAAM,CAC5BtE,OAAO,CAACC,GAAG,CAAC,QAAQ2O,CAAC,GAAG,CAAEwP,GAAG,CAACvP,IAAI,EAAI,KAAK,CAC/B,KAAK,CAAEuP,GAAG,CAAChN,IAAI,CACf,KAAK,CAAEgN,GAAG,CAACxU,QAAQ,CAACyF,OAAO,CAAC,CAAC,CAC7B,KAAK,CAAEsgB,SAAS,CAACltB,QAAQ,CACzB,WAAW,CAAE2b,GAAG,CAAC6B,QAAQ,CAAC,CACxC,CAAC,CAAC,CAEF;AACAjgB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,GAAI,CAAA4vB,YAAY,CAAG,CAAC,CAEpBxvB,gBAAgB,CAACuE,OAAO,CAAC,CAAC+a,QAAQ,CAAEzU,OAAO,GAAK,CAC9C,GAAIyU,QAAQ,CAACxb,KAAK,CAAE,KAAA2rB,qBAAA,CAClB;AACA,GAAI,CAAAC,SAAS,CAAGpQ,QAAQ,CAACxb,KAAK,CAAC8G,OAAO,CACtC,GAAI,CAAA+kB,cAAc,CAAG,IAAI,CAEzB;AACA,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAvzB,KAAK,CAACoG,OAAO,CAAC,CAAC,CACpC6c,QAAQ,CAACxb,KAAK,CAAC+rB,gBAAgB,CAACD,QAAQ,CAAC,CAEzC;AACA,KAAM,CAAAE,gBAAgB,CAAGF,QAAQ,CAACvtB,UAAU,CAACkI,SAAS,CAACiC,OAAO,CAACjD,QAAQ,CAAC,CAExE;AACA,KAAM,CAAAwmB,SAAS,CAAGH,QAAQ,CAACnuB,KAAK,CAAC,CAAC,CAACuuB,OAAO,CAACzlB,SAAS,CAACiC,OAAO,CAAC,CAC7D,GAAI3J,IAAI,CAACK,GAAG,CAAC6sB,SAAS,CAACpuB,CAAC,CAAC,CAAG,CAAC,EAAIkB,IAAI,CAACK,GAAG,CAAC6sB,SAAS,CAACluB,CAAC,CAAC,CAAG,CAAC,EAAIkuB,SAAS,CAAChuB,CAAC,CAAG,CAAC,CAAC,EAAIguB,SAAS,CAAChuB,CAAC,CAAG,CAAC,CAAE,CACjG4tB,cAAc,CAAG,KAAK,CACxB,CAEA,GAAID,SAAS,CAAE,CACbF,YAAY,EAAE,CAChB,CAEA7vB,OAAO,CAACC,GAAG,CAAC,OAAOiL,OAAO,GAAG,CAAE,CAC7BolB,EAAE,CAAE,EAAAR,qBAAA,CAAAnQ,QAAQ,CAAChR,YAAY,UAAAmhB,qBAAA,iBAArBA,qBAAA,CAAuBjhB,IAAI,GAAI,IAAI,CACvC0hB,GAAG,CAAER,SAAS,CACdS,KAAK,CAAER,cAAc,CACrBS,IAAI,CAAER,QAAQ,CAAC5gB,OAAO,CAAC,CAAC,CACxBqhB,IAAI,CAAE,CAACN,SAAS,CAACpuB,CAAC,CAAEouB,SAAS,CAACluB,CAAC,CAAEkuB,SAAS,CAAChuB,CAAC,CAAC,CAC7CuuB,MAAM,CAAER,gBACV,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFnwB,OAAO,CAACC,GAAG,CAAC,MAAM4vB,YAAY,IAAIxvB,gBAAgB,CAAC8X,IAAI,WAAW,CAAC,CAEnE;AACA,MAAO,CAAAyX,eAAe,CAACzqB,MAAM,CAAG,CAAC,CACnC,CAAE,MAAO7D,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,MAAO,MAAK,CACd,CACF,CAAC,CAID;AACA,KAAM,CAAAqU,wBAAwB,CAAGA,CAACR,YAAY,CAAEG,SAAS,GAAK,KAAAsb,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC5D,GAAI,CAAC3b,YAAY,EAAI,CAACA,YAAY,CAAChR,KAAK,EAAI,CAACmR,SAAS,CAAE,CACtD,OACF,CAEA;AACA,KAAM,CAAAyb,cAAc,CAAG,EAAE,CACzB5b,YAAY,CAAChR,KAAK,CAACE,QAAQ,CAAC8O,KAAK,EAAI,CACnC,GAAIA,KAAK,CAAC8M,QAAQ,EAAI9M,KAAK,CAAC8M,QAAQ,CAAC+Q,OAAO,CAAE,CAC5CD,cAAc,CAACxb,IAAI,CAACpC,KAAK,CAAC,CAC5B,CACF,CAAC,CAAC,CAEF4d,cAAc,CAACnsB,OAAO,CAACwnB,KAAK,EAAI,CAC9BjX,YAAY,CAAChR,KAAK,CAACiC,MAAM,CAACgmB,KAAK,CAAC,CAClC,CAAC,CAAC,CAEF;AACA,GAAI,CAAAQ,UAAU,CACd,OAAOtX,SAAS,CAACH,YAAY,EAC3B,IAAK,GAAG,CACNyX,UAAU,CAAG,QAAQ,CAAE;AACvB,MACF,IAAK,GAAG,CACNA,UAAU,CAAG,QAAQ,CAAE;AACvB,MACF,IAAK,GAAG,CACR,QACEA,UAAU,CAAG,QAAQ,CAAE;AACvB,MACJ,CAEA;AACA,KAAM,CAAAhB,aAAa,CAAG,GAAI,CAAAlvB,KAAK,CAACmjB,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACzD,KAAM,CAAAgM,aAAa,CAAG,GAAI,CAAAnvB,KAAK,CAAC0iB,iBAAiB,CAAC,CAChDvT,KAAK,CAAE+gB,UAAU,CACjBvZ,QAAQ,CAAEuZ,UAAU,CACpBqE,iBAAiB,CAAE,CACrB,CAAC,CAAC,CACF,KAAM,CAAAnF,SAAS,CAAG,GAAI,CAAApvB,KAAK,CAAC2iB,IAAI,CAACuM,aAAa,CAAEC,aAAa,CAAC,CAC9DC,SAAS,CAACliB,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAAE;AAClCypB,SAAS,CAAC7L,QAAQ,CAAG,CACnB+Q,OAAO,CAAE,IAAI,CACb5f,IAAI,CAAE,cAAc,CACpBlG,OAAO,EAAA0lB,qBAAA,CAAEzb,YAAY,CAACxG,YAAY,UAAAiiB,qBAAA,iBAAzBA,qBAAA,CAA2B1lB,OAAO,CAC3C0J,OAAO,CAAEU,SAAS,CAACV,OAAO,CAC1BE,SAAS,CAAEQ,SAAS,CAACR,SAAS,CAC9BM,UAAU,CAAEE,SAAS,CAACF,UACxB,CAAC,CAED;AACA,KAAM,CAAAgX,KAAK,CAAG,GAAI,CAAA1vB,KAAK,CAACw0B,UAAU,CAACtE,UAAU,CAAE,CAAC,CAAE,EAAE,CAAC,CACrDR,KAAK,CAACxiB,QAAQ,CAACvH,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAC5B+pB,KAAK,CAACnM,QAAQ,CAAG,CAAE+Q,OAAO,CAAE,IAAK,CAAC,CAElC;AACA7b,YAAY,CAAChR,KAAK,CAACC,GAAG,CAAC0nB,SAAS,CAAC,CACjC3W,YAAY,CAAChR,KAAK,CAACC,GAAG,CAACgoB,KAAK,CAAC,CAE7BpsB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAA4wB,sBAAA,CAAA1b,YAAY,CAACxG,YAAY,UAAAkiB,sBAAA,iBAAzBA,sBAAA,CAA2BhiB,IAAI,KAAAiiB,sBAAA,CAAI3b,YAAY,CAACxG,YAAY,UAAAmiB,sBAAA,iBAAzBA,sBAAA,CAA2B5lB,OAAO,cAAaoK,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC,CACjK,CAAC,CAED,cAAe,CAAAhO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}