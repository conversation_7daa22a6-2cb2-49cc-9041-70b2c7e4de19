"filename", "language", "JavaScript", "Markdown", "Batch", "JSON", "CSS", "JSON with Comments", "HTML", "JavaScript JSX", "Properties", "XML", "comment", "blank", "total"
"g:\AI_tools\cursor\projects\education web\.env", "Properties", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5
"g:\AI_tools\cursor\projects\education web\.vs\VSWorkspaceState.json", "JSON", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 6
"g:\AI_tools\cursor\projects\education web\HOW_TO_RUN.md", "Markdown", 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 68
"g:\AI_tools\cursor\projects\education web\README.md", "Markdown", 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 152
"g:\AI_tools\cursor\projects\education web\build\asset-manifest.json", "JSON", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 13
"g:\AI_tools\cursor\projects\education web\build\index.html", "HTML", 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1
"g:\AI_tools\cursor\projects\education web\build\manifest.json", "JSON", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 15
"g:\AI_tools\cursor\projects\education web\build\mqtt-test.html", "HTML", 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 9, 72
"g:\AI_tools\cursor\projects\education web\build\static\css\main.beae109d.css", "CSS", 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 2
"g:\AI_tools\cursor\projects\education web\build\static\js\main.4c7fc37b.js", "JavaScript", 420, 0, 0, 0, 0, 0, 0, 0, 0, 0, 65, 32, 517
"g:\AI_tools\cursor\projects\education web\docs\requirements.md", "Markdown", 0, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 166
"g:\AI_tools\cursor\projects\education web\frontend-server.js", "JavaScript", 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 17
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\README.md", "Markdown", 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 71
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\package-lock.json", "JSON", 0, 0, 0, 20332, 0, 0, 0, 0, 0, 0, 0, 1, 20333
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\package.json", "JSON", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 1, 55
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\public\index.html", "HTML", 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 23, 1, 44
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\public\manifest.json", "JSON", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 1, 26
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\App.css", "CSS", 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 6, 39
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\App.js", "JavaScript", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 26
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\App.test.js", "JavaScript", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\index.css", "CSS", 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 2, 14
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\index.js", "JavaScript", 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 18
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\reportWebVitals.js", "JavaScript", 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\setupTests.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"g:\AI_tools\cursor\projects\education web\jsconfig.json", "JSON with Comments", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 8
"g:\AI_tools\cursor\projects\education web\package-lock.json", "JSON", 0, 0, 0, 21319, 0, 0, 0, 0, 0, 0, 0, 1, 21320
"g:\AI_tools\cursor\projects\education web\package.json", "JSON", 0, 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 1, 67
"g:\AI_tools\cursor\projects\education web\public\index.html", "HTML", 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 20
"g:\AI_tools\cursor\projects\education web\public\manifest.json", "JSON", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 15
"g:\AI_tools\cursor\projects\education web\public\mqtt-test.html", "HTML", 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 9, 72
"g:\AI_tools\cursor\projects\education web\server\models\Device.js", "JavaScript", 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 36
"g:\AI_tools\cursor\projects\education web\server\models\Event.js", "JavaScript", 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 28
"g:\AI_tools\cursor\projects\education web\server\models\Intersection.js", "JavaScript", 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 24
"g:\AI_tools\cursor\projects\education web\server\models\User.js", "JavaScript", 52, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6, 61
"g:\AI_tools\cursor\projects\education web\server\models\Vehicle.js", "JavaScript", 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 39
"g:\AI_tools\cursor\projects\education web\server\mqtt-ws-bridge.js", "JavaScript", 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 28, 237
"g:\AI_tools\cursor\projects\education web\server\routes\auth.js", "JavaScript", 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 16, 89
"g:\AI_tools\cursor\projects\education web\server\routes\coordinates.js", "JavaScript", 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 11, 85
"g:\AI_tools\cursor\projects\education web\server\routes\devices.js", "JavaScript", 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 21, 163
"g:\AI_tools\cursor\projects\education web\server\routes\users.js", "JavaScript", 174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 38, 238
"g:\AI_tools\cursor\projects\education web\server\routes\vehicles.js", "JavaScript", 187, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 39, 250
"g:\AI_tools\cursor\projects\education web\server\server.js", "JavaScript", 143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 27, 184
"g:\AI_tools\cursor\projects\education web\server\start-stream-server.js", "JavaScript", 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 35
"g:\AI_tools\cursor\projects\education web\server\stream-server.js", "JavaScript", 201, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 28, 250
"g:\AI_tools\cursor\projects\education web\server\streamServer.js", "JavaScript", 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 21, 159
"g:\AI_tools\cursor\projects\education web\src\App.css", "CSS", 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 7, 44
"g:\AI_tools\cursor\projects\education web\src\App.js", "JavaScript", 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 7, 70
"g:\AI_tools\cursor\projects\education web\src\assets\css\main.css", "CSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\components\CampusModel.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 2358, 0, 0, 730, 556, 3644
"g:\AI_tools\cursor\projects\education web\src\components\CoordinateSettings.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 124, 0, 0, 6, 20, 150
"g:\AI_tools\cursor\projects\education web\src\components\MainLayout.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 125, 0, 0, 1, 11, 137
"g:\AI_tools\cursor\projects\education web\src\components\RealTimeEvents.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 228, 0, 0, 19, 37, 284
"g:\AI_tools\cursor\projects\education web\src\components\VideoPlayer.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 286, 0, 0, 0, 24, 310
"g:\AI_tools\cursor\projects\education web\src\components\layout\CollapsibleSidebar.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 92, 0, 0, 3, 6, 101
"g:\AI_tools\cursor\projects\education web\src\components\layout\MainLayout.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 302, 0, 0, 14, 20, 336
"g:\AI_tools\cursor\projects\education web\src\config\coordinates.json", "JSON", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 14
"g:\AI_tools\cursor\projects\education web\src\data\devices.json", "JSON", 0, 0, 0, 211, 0, 0, 0, 0, 0, 0, 0, 0, 211
"g:\AI_tools\cursor\projects\education web\src\data\intersections.json", "JSON", 0, 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 44
"g:\AI_tools\cursor\projects\education web\src\data\users.json", "JSON", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 25
"g:\AI_tools\cursor\projects\education web\src\data\vehicles.json", "JSON", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 40
"g:\AI_tools\cursor\projects\education web\src\index.css", "CSS", 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 1, 13
"g:\AI_tools\cursor\projects\education web\src\index.js", "JavaScript", 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 11
"g:\AI_tools\cursor\projects\education web\src\js\road-monitor.js", "JavaScript", 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 81
"g:\AI_tools\cursor\projects\education web\src\main.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\models\User.js", "JavaScript", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6, 55
"g:\AI_tools\cursor\projects\education web\src\pages\DeviceManagement.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 343, 0, 0, 24, 34, 401
"g:\AI_tools\cursor\projects\education web\src\pages\DeviceStatus.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 729, 0, 0, 49, 59, 837
"g:\AI_tools\cursor\projects\education web\src\pages\Login.css", "CSS", 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 1, 9, 69
"g:\AI_tools\cursor\projects\education web\src\pages\Login.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 253, 0, 0, 65, 43, 361
"g:\AI_tools\cursor\projects\education web\src\pages\MonitoringPage.css", "CSS", 0, 0, 0, 0, 123, 0, 0, 0, 0, 0, 0, 23, 146
"g:\AI_tools\cursor\projects\education web\src\pages\MonitoringPage.js", "JavaScript", 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 20, 174
"g:\AI_tools\cursor\projects\education web\src\pages\RealTimeTraffic.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 937, 0, 0, 142, 136, 1215
"g:\AI_tools\cursor\projects\education web\src\pages\RoadMonitoring.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 318, 0, 0, 52, 36, 406
"g:\AI_tools\cursor\projects\education web\src\pages\SystemManagement.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 648, 0, 0, 45, 71, 764
"g:\AI_tools\cursor\projects\education web\src\pages\UserManagement.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 52, 0, 0, 0, 8, 60
"g:\AI_tools\cursor\projects\education web\src\pages\VehicleManagement.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 247, 0, 0, 23, 25, 295
"g:\AI_tools\cursor\projects\education web\src\routes\auth.js", "JavaScript", 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 13, 104
"g:\AI_tools\cursor\projects\education web\src\routes\vehicles.js", "JavaScript", 138, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 33, 193
"g:\AI_tools\cursor\projects\education web\src\scripts\migratePasswords.js", "JavaScript", 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 7, 32
"g:\AI_tools\cursor\projects\education web\src\server.js", "JavaScript", 699, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 111, 886
"g:\AI_tools\cursor\projects\education web\src\server\config\db.js", "JavaScript", 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 17
"g:\AI_tools\cursor\projects\education web\src\server\index.js", "JavaScript", 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 6, 28
"g:\AI_tools\cursor\projects\education web\src\server\middleware\admin.js", "JavaScript", 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 7
"g:\AI_tools\cursor\projects\education web\src\server\middleware\auth.js", "JavaScript", 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 13, 75
"g:\AI_tools\cursor\projects\education web\src\server\models\User.js", "JavaScript", 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 9, 101
"g:\AI_tools\cursor\projects\education web\src\server\models\Vehicle.js", "JavaScript", 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 34
"g:\AI_tools\cursor\projects\education web\src\server\reset-admin.js", "JavaScript", 78, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 16, 105
"g:\AI_tools\cursor\projects\education web\src\server\routes\auth.js", "JavaScript", 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 19, 152
"g:\AI_tools\cursor\projects\education web\src\server\routes\users.js", "JavaScript", 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 19, 137
"g:\AI_tools\cursor\projects\education web\src\server\routes\vehicles.js", "JavaScript", 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 24, 149
"g:\AI_tools\cursor\projects\education web\src\server\rtspServer.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\server\start.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\server\webrtcServer.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\services\auth.js", "JavaScript", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 7, 48
"g:\AI_tools\cursor\projects\education web\src\setupProxy.js", "JavaScript", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 51
"g:\AI_tools\cursor\projects\education web\src\utils\CoordinateConverter.js", "JavaScript", 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 23, 151
"g:\AI_tools\cursor\projects\education web\src\utils\axios.js", "JavaScript", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 4, 51
"g:\AI_tools\cursor\projects\education web\start-all.bat", "Batch", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 5, 25
"g:\AI_tools\cursor\projects\education web\start-simple.bat", "Batch", 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 1, 6, 45
"g:\AI_tools\cursor\projects\education web\start-stream-server.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"g:\AI_tools\cursor\projects\education web\start.bat", "Batch", 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 9, 9, 82
"g:\AI_tools\cursor\projects\education web\start_en.bat", "Batch", 0, 0, 142, 0, 0, 0, 0, 0, 0, 0, 11, 14, 167
"g:\AI_tools\cursor\projects\education web\vue.config.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\使用说明书.md", "Markdown", 0, 494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 600
"g:\AI_tools\cursor\projects\education web\消息协议文档说明.md", "Markdown", 0, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 163
"g:\AI_tools\cursor\projects\education web\软件著作权\软件著作权的样例.md", "Markdown", 0, 313, 0, 0, 0, 0, 0, 0, 0, 0, 0, 376, 689
"Total", "-", 4067, 1274, 264, 42179, 277, 8, 167, 7042, 5, 1, 1698, 2485, 59467