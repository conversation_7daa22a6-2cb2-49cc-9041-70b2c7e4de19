{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport * as THREE from 'three';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';\nimport mqtt from 'mqtt';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CampusModel = () => {\n  _s();\n  // 在组件顶部声明所有需要的变量\n  const containerRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mqttClientRef = useRef(null);\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const rendererRef = useRef(null);\n  const controlsRef = useRef(null);\n  const isSceneInitializedRef = useRef(false);\n  const vehicleModels = useRef(new Map());\n  const trafficLightsMap = useRef(new Map());\n  const trafficLightStates = useRef(new Map());\n\n  // 初始化场景\n  const initScene = () => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    sceneRef.current = new THREE.Scene();\n    sceneRef.current.background = new THREE.Color(0x87CEEB);\n\n    // 创建相机\n    cameraRef.current = new THREE.PerspectiveCamera(75, containerRef.current.clientWidth / containerRef.current.clientHeight, 0.1, 1000);\n    cameraRef.current.position.set(0, 50, 100);\n    cameraRef.current.lookAt(0, 0, 0);\n\n    // 创建渲染器\n    rendererRef.current = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);\n    rendererRef.current.shadowMap.enabled = true;\n    containerRef.current.appendChild(rendererRef.current.domElement);\n\n    // 创建控制器\n    controlsRef.current = new OrbitControls(cameraRef.current, rendererRef.current.domElement);\n    controlsRef.current.enableDamping = true;\n    controlsRef.current.dampingFactor = 0.05;\n    controlsRef.current.screenSpacePanning = false;\n    controlsRef.current.minDistance = 10;\n    controlsRef.current.maxDistance = 500;\n    controlsRef.current.maxPolarAngle = Math.PI / 2;\n\n    // 添加环境光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);\n    sceneRef.current.add(ambientLight);\n\n    // 添加平行光\n    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight.position.set(100, 100, 50);\n    directionalLight.castShadow = true;\n    directionalLight.shadow.mapSize.width = 2048;\n    directionalLight.shadow.mapSize.height = 2048;\n    sceneRef.current.add(directionalLight);\n\n    // 初始化完成标志\n    isSceneInitializedRef.current = true;\n\n    // 开始动画循环\n    animate();\n  };\n\n  // 添加模型到场景\n  const addModelToScene = (model, position) => {\n    if (!isSceneInitializedRef.current || !sceneRef.current) {\n      console.warn('场景未初始化，无法添加模型');\n      return;\n    }\n    sceneRef.current.add(model);\n    model.position.copy(position);\n  };\n\n  // 动画循环\n  const animate = () => {\n    if (!isSceneInitializedRef.current) return;\n    animationFrameRef.current = requestAnimationFrame(animate);\n    if (controlsRef.current) {\n      controlsRef.current.update();\n    }\n    if (rendererRef.current && sceneRef.current && cameraRef.current) {\n      rendererRef.current.render(sceneRef.current, cameraRef.current);\n    }\n  };\n\n  // 处理窗口大小变化\n  const handleResize = () => {\n    if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;\n    cameraRef.current.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight;\n    cameraRef.current.updateProjectionMatrix();\n    rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);\n  };\n\n  // 在组件挂载时初始化场景\n  useEffect(() => {\n    initScene();\n    window.addEventListener('resize', handleResize);\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (rendererRef.current && containerRef.current) {\n        containerRef.current.removeChild(rendererRef.current.domElement);\n        rendererRef.current.dispose();\n        rendererRef.current.forceContextLoss();\n        rendererRef.current.domElement = null;\n        rendererRef.current = null;\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels.current) {\n        vehicleModels.current.forEach((modelData, id) => {\n          if (modelData.model && sceneRef.current) {\n            sceneRef.current.remove(modelData.model);\n            if (modelData.model.geometry) {\n              modelData.model.geometry.dispose();\n            }\n            if (modelData.model.material) {\n              if (Array.isArray(modelData.model.material)) {\n                modelData.model.material.forEach(material => material.dispose());\n              } else {\n                modelData.model.material.dispose();\n              }\n            }\n          }\n        });\n        vehicleModels.current.clear();\n      }\n\n      // 7. 清理红绿灯模型\n      trafficLightsMap.current.forEach(lightObj => {\n        if (sceneRef.current && lightObj.model) {\n          sceneRef.current.remove(lightObj.model);\n          if (lightObj.model.geometry) {\n            lightObj.model.geometry.dispose();\n          }\n          if (lightObj.model.material) {\n            if (Array.isArray(lightObj.model.material)) {\n              lightObj.model.material.forEach(material => material.dispose());\n            } else {\n              lightObj.model.material.dispose();\n            }\n          }\n        }\n      });\n      trafficLightsMap.current.clear();\n      trafficLightStates.current.clear();\n\n      // 8. 清理场景\n      if (sceneRef.current) {\n        sceneRef.current.traverse(object => {\n          if (object.geometry) {\n            object.geometry.dispose();\n          }\n          if (object.material) {\n            if (Array.isArray(object.material)) {\n              object.material.forEach(material => material.dispose());\n            } else {\n              object.material.dispose();\n            }\n          }\n        });\n        sceneRef.current.clear();\n        sceneRef.current = null;\n      }\n\n      // 9. 清理控制器\n      if (controlsRef.current) {\n        controlsRef.current.dispose();\n        controlsRef.current = null;\n      }\n\n      // 10. 清理相机\n      if (cameraRef.current) {\n        cameraRef.current = null;\n      }\n      console.log('组件清理完成');\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    style: {\n      width: '100%',\n      height: '100%'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 219,\n    columnNumber: 5\n  }, this);\n};\n_s(CampusModel, \"F/57tAO3CyGBAI7lWXQAgUnMrhA=\");\n_c = CampusModel;\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "THREE", "OrbitControls", "GLTFLoader", "DRACOLoader", "mqtt", "jsxDEV", "_jsxDEV", "CampusModel", "_s", "containerRef", "animationFrameRef", "mqttClientRef", "sceneRef", "cameraRef", "rendererRef", "controlsRef", "isSceneInitializedRef", "vehicleModels", "Map", "trafficLightsMap", "trafficLightStates", "initScene", "current", "Scene", "background", "Color", "PerspectiveCamera", "clientWidth", "clientHeight", "position", "set", "lookAt", "WebGLRenderer", "antialias", "setSize", "shadowMap", "enabled", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "enableDamping", "dampingFactor", "screenSpacePanning", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "<PERSON><PERSON><PERSON><PERSON>", "shadow", "mapSize", "width", "height", "animate", "addModelToScene", "model", "console", "warn", "copy", "requestAnimationFrame", "update", "render", "handleResize", "aspect", "updateProjectionMatrix", "window", "addEventListener", "log", "cancelAnimationFrame", "globalUpdateInterval", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "forceContextLoss", "for<PERSON>ach", "modelData", "id", "remove", "geometry", "material", "Array", "isArray", "clear", "lightObj", "traverse", "object", "ref", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport * as THREE from 'three';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';\nimport mqtt from 'mqtt';\n\nconst CampusModel = () => {\n  // 在组件顶部声明所有需要的变量\n  const containerRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mqttClientRef = useRef(null);\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const rendererRef = useRef(null);\n  const controlsRef = useRef(null);\n  const isSceneInitializedRef = useRef(false);\n  const vehicleModels = useRef(new Map());\n  const trafficLightsMap = useRef(new Map());\n  const trafficLightStates = useRef(new Map());\n\n  // 初始化场景\n  const initScene = () => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    sceneRef.current = new THREE.Scene();\n    sceneRef.current.background = new THREE.Color(0x87CEEB);\n\n    // 创建相机\n    cameraRef.current = new THREE.PerspectiveCamera(\n      75,\n      containerRef.current.clientWidth / containerRef.current.clientHeight,\n      0.1,\n      1000\n    );\n    cameraRef.current.position.set(0, 50, 100);\n    cameraRef.current.lookAt(0, 0, 0);\n\n    // 创建渲染器\n    rendererRef.current = new THREE.WebGLRenderer({ antialias: true });\n    rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);\n    rendererRef.current.shadowMap.enabled = true;\n    containerRef.current.appendChild(rendererRef.current.domElement);\n\n    // 创建控制器\n    controlsRef.current = new OrbitControls(cameraRef.current, rendererRef.current.domElement);\n    controlsRef.current.enableDamping = true;\n    controlsRef.current.dampingFactor = 0.05;\n    controlsRef.current.screenSpacePanning = false;\n    controlsRef.current.minDistance = 10;\n    controlsRef.current.maxDistance = 500;\n    controlsRef.current.maxPolarAngle = Math.PI / 2;\n\n    // 添加环境光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);\n    sceneRef.current.add(ambientLight);\n\n    // 添加平行光\n    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight.position.set(100, 100, 50);\n    directionalLight.castShadow = true;\n    directionalLight.shadow.mapSize.width = 2048;\n    directionalLight.shadow.mapSize.height = 2048;\n    sceneRef.current.add(directionalLight);\n\n    // 初始化完成标志\n    isSceneInitializedRef.current = true;\n\n    // 开始动画循环\n    animate();\n  };\n\n  // 添加模型到场景\n  const addModelToScene = (model, position) => {\n    if (!isSceneInitializedRef.current || !sceneRef.current) {\n      console.warn('场景未初始化，无法添加模型');\n      return;\n    }\n    sceneRef.current.add(model);\n    model.position.copy(position);\n  };\n\n  // 动画循环\n  const animate = () => {\n    if (!isSceneInitializedRef.current) return;\n\n    animationFrameRef.current = requestAnimationFrame(animate);\n\n    if (controlsRef.current) {\n      controlsRef.current.update();\n    }\n\n    if (rendererRef.current && sceneRef.current && cameraRef.current) {\n      rendererRef.current.render(sceneRef.current, cameraRef.current);\n    }\n  };\n\n  // 处理窗口大小变化\n  const handleResize = () => {\n    if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;\n\n    cameraRef.current.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight;\n    cameraRef.current.updateProjectionMatrix();\n    rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);\n  };\n\n  // 在组件挂载时初始化场景\n  useEffect(() => {\n    initScene();\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (rendererRef.current && containerRef.current) {\n        containerRef.current.removeChild(rendererRef.current.domElement);\n        rendererRef.current.dispose();\n        rendererRef.current.forceContextLoss();\n        rendererRef.current.domElement = null;\n        rendererRef.current = null;\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels.current) {\n        vehicleModels.current.forEach((modelData, id) => {\n          if (modelData.model && sceneRef.current) {\n            sceneRef.current.remove(modelData.model);\n            if (modelData.model.geometry) {\n              modelData.model.geometry.dispose();\n            }\n            if (modelData.model.material) {\n              if (Array.isArray(modelData.model.material)) {\n                modelData.model.material.forEach(material => material.dispose());\n              } else {\n                modelData.model.material.dispose();\n              }\n            }\n          }\n        });\n        vehicleModels.current.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.current.forEach((lightObj) => {\n        if (sceneRef.current && lightObj.model) {\n          sceneRef.current.remove(lightObj.model);\n          if (lightObj.model.geometry) {\n            lightObj.model.geometry.dispose();\n          }\n          if (lightObj.model.material) {\n            if (Array.isArray(lightObj.model.material)) {\n              lightObj.model.material.forEach(material => material.dispose());\n            } else {\n              lightObj.model.material.dispose();\n            }\n          }\n        }\n      });\n      trafficLightsMap.current.clear();\n      trafficLightStates.current.clear();\n      \n      // 8. 清理场景\n      if (sceneRef.current) {\n        sceneRef.current.traverse((object) => {\n          if (object.geometry) {\n            object.geometry.dispose();\n          }\n          if (object.material) {\n            if (Array.isArray(object.material)) {\n              object.material.forEach(material => material.dispose());\n            } else {\n              object.material.dispose();\n            }\n          }\n        });\n        sceneRef.current.clear();\n        sceneRef.current = null;\n      }\n      \n      // 9. 清理控制器\n      if (controlsRef.current) {\n        controlsRef.current.dispose();\n        controlsRef.current = null;\n      }\n      \n      // 10. 清理相机\n      if (cameraRef.current) {\n        cameraRef.current = null;\n      }\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  return (\n    <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n  );\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,WAAW,QAAQ,wCAAwC;AACpE,OAAOC,IAAI,MAAM,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB;EACA,MAAMC,YAAY,GAAGV,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMW,iBAAiB,GAAGX,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMY,aAAa,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMa,QAAQ,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMc,SAAS,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMe,WAAW,GAAGf,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMgB,WAAW,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMiB,qBAAqB,GAAGjB,MAAM,CAAC,KAAK,CAAC;EAC3C,MAAMkB,aAAa,GAAGlB,MAAM,CAAC,IAAImB,GAAG,CAAC,CAAC,CAAC;EACvC,MAAMC,gBAAgB,GAAGpB,MAAM,CAAC,IAAImB,GAAG,CAAC,CAAC,CAAC;EAC1C,MAAME,kBAAkB,GAAGrB,MAAM,CAAC,IAAImB,GAAG,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMG,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACZ,YAAY,CAACa,OAAO,EAAE;;IAE3B;IACAV,QAAQ,CAACU,OAAO,GAAG,IAAItB,KAAK,CAACuB,KAAK,CAAC,CAAC;IACpCX,QAAQ,CAACU,OAAO,CAACE,UAAU,GAAG,IAAIxB,KAAK,CAACyB,KAAK,CAAC,QAAQ,CAAC;;IAEvD;IACAZ,SAAS,CAACS,OAAO,GAAG,IAAItB,KAAK,CAAC0B,iBAAiB,CAC7C,EAAE,EACFjB,YAAY,CAACa,OAAO,CAACK,WAAW,GAAGlB,YAAY,CAACa,OAAO,CAACM,YAAY,EACpE,GAAG,EACH,IACF,CAAC;IACDf,SAAS,CAACS,OAAO,CAACO,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC;IAC1CjB,SAAS,CAACS,OAAO,CAACS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAEjC;IACAjB,WAAW,CAACQ,OAAO,GAAG,IAAItB,KAAK,CAACgC,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAClEnB,WAAW,CAACQ,OAAO,CAACY,OAAO,CAACzB,YAAY,CAACa,OAAO,CAACK,WAAW,EAAElB,YAAY,CAACa,OAAO,CAACM,YAAY,CAAC;IAChGd,WAAW,CAACQ,OAAO,CAACa,SAAS,CAACC,OAAO,GAAG,IAAI;IAC5C3B,YAAY,CAACa,OAAO,CAACe,WAAW,CAACvB,WAAW,CAACQ,OAAO,CAACgB,UAAU,CAAC;;IAEhE;IACAvB,WAAW,CAACO,OAAO,GAAG,IAAIrB,aAAa,CAACY,SAAS,CAACS,OAAO,EAAER,WAAW,CAACQ,OAAO,CAACgB,UAAU,CAAC;IAC1FvB,WAAW,CAACO,OAAO,CAACiB,aAAa,GAAG,IAAI;IACxCxB,WAAW,CAACO,OAAO,CAACkB,aAAa,GAAG,IAAI;IACxCzB,WAAW,CAACO,OAAO,CAACmB,kBAAkB,GAAG,KAAK;IAC9C1B,WAAW,CAACO,OAAO,CAACoB,WAAW,GAAG,EAAE;IACpC3B,WAAW,CAACO,OAAO,CAACqB,WAAW,GAAG,GAAG;IACrC5B,WAAW,CAACO,OAAO,CAACsB,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;;IAE/C;IACA,MAAMC,YAAY,GAAG,IAAI/C,KAAK,CAACgD,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC1DpC,QAAQ,CAACU,OAAO,CAAC2B,GAAG,CAACF,YAAY,CAAC;;IAElC;IACA,MAAMG,gBAAgB,GAAG,IAAIlD,KAAK,CAACmD,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IAClED,gBAAgB,CAACrB,QAAQ,CAACC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;IAC3CoB,gBAAgB,CAACE,UAAU,GAAG,IAAI;IAClCF,gBAAgB,CAACG,MAAM,CAACC,OAAO,CAACC,KAAK,GAAG,IAAI;IAC5CL,gBAAgB,CAACG,MAAM,CAACC,OAAO,CAACE,MAAM,GAAG,IAAI;IAC7C5C,QAAQ,CAACU,OAAO,CAAC2B,GAAG,CAACC,gBAAgB,CAAC;;IAEtC;IACAlC,qBAAqB,CAACM,OAAO,GAAG,IAAI;;IAEpC;IACAmC,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAE9B,QAAQ,KAAK;IAC3C,IAAI,CAACb,qBAAqB,CAACM,OAAO,IAAI,CAACV,QAAQ,CAACU,OAAO,EAAE;MACvDsC,OAAO,CAACC,IAAI,CAAC,eAAe,CAAC;MAC7B;IACF;IACAjD,QAAQ,CAACU,OAAO,CAAC2B,GAAG,CAACU,KAAK,CAAC;IAC3BA,KAAK,CAAC9B,QAAQ,CAACiC,IAAI,CAACjC,QAAQ,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM4B,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI,CAACzC,qBAAqB,CAACM,OAAO,EAAE;IAEpCZ,iBAAiB,CAACY,OAAO,GAAGyC,qBAAqB,CAACN,OAAO,CAAC;IAE1D,IAAI1C,WAAW,CAACO,OAAO,EAAE;MACvBP,WAAW,CAACO,OAAO,CAAC0C,MAAM,CAAC,CAAC;IAC9B;IAEA,IAAIlD,WAAW,CAACQ,OAAO,IAAIV,QAAQ,CAACU,OAAO,IAAIT,SAAS,CAACS,OAAO,EAAE;MAChER,WAAW,CAACQ,OAAO,CAAC2C,MAAM,CAACrD,QAAQ,CAACU,OAAO,EAAET,SAAS,CAACS,OAAO,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAM4C,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACzD,YAAY,CAACa,OAAO,IAAI,CAACT,SAAS,CAACS,OAAO,IAAI,CAACR,WAAW,CAACQ,OAAO,EAAE;IAEzET,SAAS,CAACS,OAAO,CAAC6C,MAAM,GAAG1D,YAAY,CAACa,OAAO,CAACK,WAAW,GAAGlB,YAAY,CAACa,OAAO,CAACM,YAAY;IAC/Ff,SAAS,CAACS,OAAO,CAAC8C,sBAAsB,CAAC,CAAC;IAC1CtD,WAAW,CAACQ,OAAO,CAACY,OAAO,CAACzB,YAAY,CAACa,OAAO,CAACK,WAAW,EAAElB,YAAY,CAACa,OAAO,CAACM,YAAY,CAAC;EAClG,CAAC;;EAED;EACA9B,SAAS,CAAC,MAAM;IACduB,SAAS,CAAC,CAAC;IACXgD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXN,OAAO,CAACW,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAI7D,iBAAiB,CAACY,OAAO,EAAE;QAC7BkD,oBAAoB,CAAC9D,iBAAiB,CAACY,OAAO,CAAC;QAC/CZ,iBAAiB,CAACY,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAImD,oBAAoB,EAAE;QACxBC,aAAa,CAACD,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAI9D,aAAa,CAACW,OAAO,EAAE;QACzBX,aAAa,CAACW,OAAO,CAACqD,KAAK,CAAC,CAAC;QAC7BhE,aAAa,CAACW,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACA+C,MAAM,CAACO,mBAAmB,CAAC,QAAQ,EAAEV,YAAY,CAAC;;MAElD;MACA,IAAIpD,WAAW,CAACQ,OAAO,IAAIb,YAAY,CAACa,OAAO,EAAE;QAC/Cb,YAAY,CAACa,OAAO,CAACuD,WAAW,CAAC/D,WAAW,CAACQ,OAAO,CAACgB,UAAU,CAAC;QAChExB,WAAW,CAACQ,OAAO,CAACwD,OAAO,CAAC,CAAC;QAC7BhE,WAAW,CAACQ,OAAO,CAACyD,gBAAgB,CAAC,CAAC;QACtCjE,WAAW,CAACQ,OAAO,CAACgB,UAAU,GAAG,IAAI;QACrCxB,WAAW,CAACQ,OAAO,GAAG,IAAI;MAC5B;;MAEA;MACA,IAAIL,aAAa,CAACK,OAAO,EAAE;QACzBL,aAAa,CAACK,OAAO,CAAC0D,OAAO,CAAC,CAACC,SAAS,EAAEC,EAAE,KAAK;UAC/C,IAAID,SAAS,CAACtB,KAAK,IAAI/C,QAAQ,CAACU,OAAO,EAAE;YACvCV,QAAQ,CAACU,OAAO,CAAC6D,MAAM,CAACF,SAAS,CAACtB,KAAK,CAAC;YACxC,IAAIsB,SAAS,CAACtB,KAAK,CAACyB,QAAQ,EAAE;cAC5BH,SAAS,CAACtB,KAAK,CAACyB,QAAQ,CAACN,OAAO,CAAC,CAAC;YACpC;YACA,IAAIG,SAAS,CAACtB,KAAK,CAAC0B,QAAQ,EAAE;cAC5B,IAAIC,KAAK,CAACC,OAAO,CAACN,SAAS,CAACtB,KAAK,CAAC0B,QAAQ,CAAC,EAAE;gBAC3CJ,SAAS,CAACtB,KAAK,CAAC0B,QAAQ,CAACL,OAAO,CAACK,QAAQ,IAAIA,QAAQ,CAACP,OAAO,CAAC,CAAC,CAAC;cAClE,CAAC,MAAM;gBACLG,SAAS,CAACtB,KAAK,CAAC0B,QAAQ,CAACP,OAAO,CAAC,CAAC;cACpC;YACF;UACF;QACF,CAAC,CAAC;QACF7D,aAAa,CAACK,OAAO,CAACkE,KAAK,CAAC,CAAC;MAC/B;;MAEA;MACArE,gBAAgB,CAACG,OAAO,CAAC0D,OAAO,CAAES,QAAQ,IAAK;QAC7C,IAAI7E,QAAQ,CAACU,OAAO,IAAImE,QAAQ,CAAC9B,KAAK,EAAE;UACtC/C,QAAQ,CAACU,OAAO,CAAC6D,MAAM,CAACM,QAAQ,CAAC9B,KAAK,CAAC;UACvC,IAAI8B,QAAQ,CAAC9B,KAAK,CAACyB,QAAQ,EAAE;YAC3BK,QAAQ,CAAC9B,KAAK,CAACyB,QAAQ,CAACN,OAAO,CAAC,CAAC;UACnC;UACA,IAAIW,QAAQ,CAAC9B,KAAK,CAAC0B,QAAQ,EAAE;YAC3B,IAAIC,KAAK,CAACC,OAAO,CAACE,QAAQ,CAAC9B,KAAK,CAAC0B,QAAQ,CAAC,EAAE;cAC1CI,QAAQ,CAAC9B,KAAK,CAAC0B,QAAQ,CAACL,OAAO,CAACK,QAAQ,IAAIA,QAAQ,CAACP,OAAO,CAAC,CAAC,CAAC;YACjE,CAAC,MAAM;cACLW,QAAQ,CAAC9B,KAAK,CAAC0B,QAAQ,CAACP,OAAO,CAAC,CAAC;YACnC;UACF;QACF;MACF,CAAC,CAAC;MACF3D,gBAAgB,CAACG,OAAO,CAACkE,KAAK,CAAC,CAAC;MAChCpE,kBAAkB,CAACE,OAAO,CAACkE,KAAK,CAAC,CAAC;;MAElC;MACA,IAAI5E,QAAQ,CAACU,OAAO,EAAE;QACpBV,QAAQ,CAACU,OAAO,CAACoE,QAAQ,CAAEC,MAAM,IAAK;UACpC,IAAIA,MAAM,CAACP,QAAQ,EAAE;YACnBO,MAAM,CAACP,QAAQ,CAACN,OAAO,CAAC,CAAC;UAC3B;UACA,IAAIa,MAAM,CAACN,QAAQ,EAAE;YACnB,IAAIC,KAAK,CAACC,OAAO,CAACI,MAAM,CAACN,QAAQ,CAAC,EAAE;cAClCM,MAAM,CAACN,QAAQ,CAACL,OAAO,CAACK,QAAQ,IAAIA,QAAQ,CAACP,OAAO,CAAC,CAAC,CAAC;YACzD,CAAC,MAAM;cACLa,MAAM,CAACN,QAAQ,CAACP,OAAO,CAAC,CAAC;YAC3B;UACF;QACF,CAAC,CAAC;QACFlE,QAAQ,CAACU,OAAO,CAACkE,KAAK,CAAC,CAAC;QACxB5E,QAAQ,CAACU,OAAO,GAAG,IAAI;MACzB;;MAEA;MACA,IAAIP,WAAW,CAACO,OAAO,EAAE;QACvBP,WAAW,CAACO,OAAO,CAACwD,OAAO,CAAC,CAAC;QAC7B/D,WAAW,CAACO,OAAO,GAAG,IAAI;MAC5B;;MAEA;MACA,IAAIT,SAAS,CAACS,OAAO,EAAE;QACrBT,SAAS,CAACS,OAAO,GAAG,IAAI;MAC1B;MAEAsC,OAAO,CAACW,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEjE,OAAA;IAAKsF,GAAG,EAAEnF,YAAa;IAACoF,KAAK,EAAE;MAAEtC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EAAE;IAAAsC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAExE,CAAC;AAACzF,EAAA,CArNID,WAAW;AAAA2F,EAAA,GAAX3F,WAAW;AAuNjB,eAAeA,WAAW;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}