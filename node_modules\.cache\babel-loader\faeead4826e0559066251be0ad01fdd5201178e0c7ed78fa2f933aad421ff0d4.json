{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RoadMonitoring.jsx\",\n  _s = $RefreshSig$();\n// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport VideoPlayer from '../components/VideoPlayer';\n\n// 添加立即打印，检查导入是否成功\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\n_c3 = InfoCard;\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 视频占位符\n_c4 = VideoContainer;\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n_c5 = VideoPlaceholder;\nconst RoadMonitoring = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 修改 useEffect 部分\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n\n        // 确保我们访问的是 devices 数组\n        const devicesArray = devicesData.devices || [];\n        if (devicesArray.length === 0) {\n          throw new Error('No devices found in the data');\n        }\n        console.log('Processing devices array:', devicesArray);\n\n        // 根据 location 对设备进行分组\n        const groupedDevices = devicesArray.reduce((acc, device) => {\n          const location = device.location;\n          if (!location) {\n            console.warn('Device missing location:', device);\n            return acc;\n          }\n          if (!acc[location]) {\n            acc[location] = {\n              id: location,\n              name: location,\n              description: `${location}监控点`,\n              status: '正常',\n              lastUpdate: device.createdAt || new Date().toISOString(),\n              devices: []\n            };\n          }\n\n          // 使用设备原始名称而不是生成新的名称\n          acc[location].devices.push({\n            type: device.type,\n            id: device.id,\n            name: device.name,\n            status: device.status\n          });\n          return acc;\n        }, {});\n        const intersectionsData = Object.values(groupedDevices);\n        console.log('Final processed intersections:', intersectionsData);\n        if (intersectionsData.length === 0) {\n          throw new Error('No intersections processed from the data');\n        } else {\n          setIntersections(intersectionsData);\n          setSelectedIntersection(intersectionsData[0]);\n        }\n      } catch (error) {\n        console.error('Error loading devices data:', error);\n        setIntersections([{\n          id: 'error',\n          name: '数据加载错误',\n          description: `无法加载设备数据: ${error.message}`,\n          status: '错误',\n          lastUpdate: new Date().toLocaleString(),\n          devices: []\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n  }, []);\n\n  // 处理路口选择\n  const handleIntersectionSelect = intersection => {\n    setSelectedIntersection(intersection);\n  };\n\n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = devices => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n\n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = devices => {\n    return devices.filter(device => device.type === 'camera');\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8DEF\\u53E3\\u4FE1\\u606F\\u5217\\u8868\",\n          bordered: false,\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"vertical\",\n            dataSource: intersections,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              onClick: () => handleIntersectionSelect(item),\n              style: {\n                cursor: 'pointer',\n                background: (selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.id) === item.id ? '#e6f7ff' : 'transparent',\n                padding: '8px',\n                borderRadius: '4px',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '14px',\n                    fontWeight: 'bold'\n                  },\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 28\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: item.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 34\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  marginTop: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u8BBE\\u5907\\u914D\\u7F6E\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 26\n                  }, this), \" \", getDeviceSummary(item.devices)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '4px'\n                  },\n                  children: item.devices.map(device => /*#__PURE__*/_jsxDEV(Badge, {\n                    status: device.status === 'online' ? 'success' : 'error',\n                    text: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '12px',\n                        marginRight: '8px'\n                      },\n                      children: device.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 29\n                    }, this),\n                    style: {\n                      display: 'inline-block',\n                      marginRight: '8px'\n                    }\n                  }, device.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: `视频监控 - ${(selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.name) || ''}`,\n          bordered: false,\n          height: \"100%\",\n          children: selectedIntersection ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '12px',\n                fontSize: '13px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Descriptions, {\n                size: \"small\",\n                column: 1,\n                styles: {\n                  label: {\n                    fontSize: '13px'\n                  },\n                  content: {\n                    fontSize: '13px'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u8DEF\\u53E3\\u540D\\u79F0\",\n                  children: selectedIntersection.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u8DEF\\u53E3\\u63CF\\u8FF0\",\n                  children: selectedIntersection.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u6444\\u50CF\\u5934\\u6570\\u91CF\",\n                  children: getCameras(selectedIntersection.devices).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this), getCameras(selectedIntersection.devices).slice(0, 4).map(camera => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  marginBottom: '4px',\n                  fontWeight: 'bold'\n                },\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  status: camera.status === 'online' ? 'success' : 'error',\n                  text: camera.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(VideoContainer, {\n                children: /*#__PURE__*/_jsxDEV(VideoPlaceholder, {\n                  children: camera.status === 'online' ? /*#__PURE__*/_jsxDEV(VideoPlayer, {\n                    deviceId: camera.id,\n                    rtspUrl: camera.rtspUrl\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: \"\\u6444\\u50CF\\u5934\\u79BB\\u7EBF\"\n                  }, void 0, false)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this)]\n            }, camera.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this)), getCameras(selectedIntersection.devices).length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px 0'\n              },\n              children: \"\\u8BE5\\u8DEF\\u53E3\\u6CA1\\u6709\\u914D\\u7F6E\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px 0'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u4E00\\u4E2A\\u8DEF\\u53E3\\u67E5\\u770B\\u89C6\\u9891\\u76D1\\u63A7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(RoadMonitoring, \"mRMMpGGs4EM8Z7alHD7AKBWx1lg=\");\n_c6 = RoadMonitoring;\nexport default RoadMonitoring;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"VideoContainer\");\n$RefreshReg$(_c5, \"VideoPlaceholder\");\n$RefreshReg$(_c6, \"RoadMonitoring\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "List", "Descriptions", "Spin", "Badge", "Row", "Col", "styled", "CollapsibleSidebar", "devicesData", "VideoPlayer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "VideoContainer", "_c4", "VideoPlaceholder", "_c5", "RoadMonitoring", "_s", "loading", "setLoading", "intersections", "setIntersections", "selectedIntersection", "setSelectedIntersection", "setLeftCollapsed", "setRightCollapsed", "loadData", "devicesArray", "devices", "length", "Error", "groupedDevices", "reduce", "acc", "device", "location", "warn", "id", "name", "description", "status", "lastUpdate", "createdAt", "Date", "toISOString", "push", "type", "intersectionsData", "Object", "values", "error", "message", "toLocaleString", "handleIntersectionSelect", "intersection", "getDeviceSummary", "summary", "for<PERSON>ach", "entries", "map", "count", "typeNames", "join", "getCameras", "filter", "spinning", "tip", "children", "position", "collapsed", "onCollapse", "title", "bordered", "itemLayout", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "onClick", "style", "cursor", "background", "padding", "borderRadius", "marginBottom", "Meta", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "text", "marginRight", "display", "size", "column", "styles", "label", "content", "slice", "camera", "deviceId", "rtspUrl", "textAlign", "_c6", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RoadMonitoring.jsx"], "sourcesContent": ["// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport VideoPlayer from '../components/VideoPlayer';\n\n// 添加立即打印，检查导入是否成功\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 视频占位符\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n\nconst RoadMonitoring = () => {\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  // 修改 useEffect 部分\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n        \n        // 确保我们访问的是 devices 数组\n        const devicesArray = devicesData.devices || [];\n        \n        if (devicesArray.length === 0) {\n          throw new Error('No devices found in the data');\n        }\n        \n        console.log('Processing devices array:', devicesArray);\n        \n        // 根据 location 对设备进行分组\n        const groupedDevices = devicesArray.reduce((acc, device) => {\n          const location = device.location;\n          if (!location) {\n            console.warn('Device missing location:', device);\n            return acc;\n          }\n\n          if (!acc[location]) {\n            acc[location] = {\n              id: location,\n              name: location,\n              description: `${location}监控点`,\n              status: '正常',\n              lastUpdate: device.createdAt || new Date().toISOString(),\n              devices: []\n            };\n          }\n\n          // 使用设备原始名称而不是生成新的名称\n          acc[location].devices.push({\n            type: device.type,\n            id: device.id,\n            name: device.name,\n            status: device.status\n          });\n\n          return acc;\n        }, {});\n\n        const intersectionsData = Object.values(groupedDevices);\n        console.log('Final processed intersections:', intersectionsData);\n\n        if (intersectionsData.length === 0) {\n          throw new Error('No intersections processed from the data');\n        } else {\n          setIntersections(intersectionsData);\n          setSelectedIntersection(intersectionsData[0]);\n        }\n      } catch (error) {\n        console.error('Error loading devices data:', error);\n        setIntersections([{\n          id: 'error',\n          name: '数据加载错误',\n          description: `无法加载设备数据: ${error.message}`,\n          status: '错误',\n          lastUpdate: new Date().toLocaleString(),\n          devices: []\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n  \n  // 处理路口选择\n  const handleIntersectionSelect = (intersection) => {\n    setSelectedIntersection(intersection);\n  };\n  \n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = (devices) => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    \n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      \n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n  \n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = (devices) => {\n    return devices.filter(device => device.type === 'camera');\n  };\n  \n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          <InfoCard title=\"路口信息列表\" bordered={false} height=\"100%\">\n            <List\n              itemLayout=\"vertical\"\n              dataSource={intersections}\n              renderItem={item => (\n                <List.Item\n                  onClick={() => handleIntersectionSelect(item)}\n                  style={{ \n                    cursor: 'pointer',\n                    background: selectedIntersection?.id === item.id ? '#e6f7ff' : 'transparent',\n                    padding: '8px',\n                    borderRadius: '4px',\n                    marginBottom: '8px'\n                  }}\n                >\n                  <List.Item.Meta\n                    title={<span style={{ fontSize: '14px', fontWeight: 'bold' }}>{item.name}</span>}\n                    description={<span style={{ fontSize: '12px' }}>{item.description}</span>}\n                  />\n                  <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                    <div><strong>设备配置：</strong> {getDeviceSummary(item.devices)}</div>\n                    <div style={{ marginTop: '4px' }}>\n                      {item.devices.map(device => (\n                        <Badge \n                          key={device.id}\n                          status={device.status === 'online' ? 'success' : 'error'} \n                          text={\n                            <span style={{ fontSize: '12px', marginRight: '8px' }}>\n                              {device.name}\n                            </span>\n                          }\n                          style={{ display: 'inline-block', marginRight: '8px' }}\n                        />\n                      ))}\n                    </div>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          <InfoCard \n            title={`视频监控 - ${selectedIntersection?.name || ''}`} \n            bordered={false} \n            height=\"100%\"\n          >\n            {selectedIntersection ? (\n              <>\n                <div style={{ marginBottom: '12px', fontSize: '13px' }}>\n                  <Descriptions \n                    size=\"small\" \n                    column={1}\n                    styles={{\n                      label: { fontSize: '13px' },\n                      content: { fontSize: '13px' }\n                    }}\n                  >\n                    <Descriptions.Item label=\"路口名称\">{selectedIntersection.name}</Descriptions.Item>\n                    <Descriptions.Item label=\"路口描述\">{selectedIntersection.description}</Descriptions.Item>\n                    <Descriptions.Item label=\"摄像头数量\">{getCameras(selectedIntersection.devices).length}</Descriptions.Item>\n                  </Descriptions>\n                </div>\n                \n                {/* 修改为N行1列的布局 */}\n                {getCameras(selectedIntersection.devices).slice(0, 4).map(camera => (\n                  <div key={camera.id} style={{ marginBottom: '10px' }}>\n                    <div style={{ fontSize: '13px', marginBottom: '4px', fontWeight: 'bold' }}>\n                      <Badge \n                        status={camera.status === 'online' ? 'success' : 'error'} \n                        text={camera.name}\n                      />\n                    </div>\n                    <VideoContainer>\n                      <VideoPlaceholder>\n                        {camera.status === 'online' ? (\n                          <VideoPlayer \n                            deviceId={camera.id} \n                            rtspUrl={camera.rtspUrl} \n                          />\n                        ) : (\n                          <>摄像头离线</>\n                        )}\n                      </VideoPlaceholder>\n                    </VideoContainer>\n                  </div>\n                ))}\n                \n                {getCameras(selectedIntersection.devices).length === 0 && (\n                  <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                    该路口没有配置摄像头\n                  </div>\n                )}\n              </>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                请选择一个路口查看视频监控\n              </div>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RoadMonitoring;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AACtE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEP,WAAW,CAAC;;AAErD;AACA,MAAMQ,aAAa,GAAGV,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GANMF,aAAa;AAOnB,MAAMG,WAAW,GAAGb,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGd,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGf,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGpB,MAAM,CAACP,IAAI,CAAC;AAC7B;AACA,YAAYuB,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,cAAc,GAAGvB,MAAM,CAACW,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAa,GAAA,GAdMD,cAAc;AAepB,MAAME,gBAAgB,GAAGzB,MAAM,CAACW,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAXID,gBAAgB;AAatB,MAAME,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC0B,aAAa,EAAEkB,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2B,cAAc,EAAEkB,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6C,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF;QACA7B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEP,WAAW,CAAC;;QAEjD;QACA,MAAMoC,YAAY,GAAGpC,WAAW,CAACqC,OAAO,IAAI,EAAE;QAE9C,IAAID,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;UAC7B,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;QACjD;QAEAjC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6B,YAAY,CAAC;;QAEtD;QACA,MAAMI,cAAc,GAAGJ,YAAY,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;UAC1D,MAAMC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;UAChC,IAAI,CAACA,QAAQ,EAAE;YACbtC,OAAO,CAACuC,IAAI,CAAC,0BAA0B,EAAEF,MAAM,CAAC;YAChD,OAAOD,GAAG;UACZ;UAEA,IAAI,CAACA,GAAG,CAACE,QAAQ,CAAC,EAAE;YAClBF,GAAG,CAACE,QAAQ,CAAC,GAAG;cACdE,EAAE,EAAEF,QAAQ;cACZG,IAAI,EAAEH,QAAQ;cACdI,WAAW,EAAE,GAAGJ,QAAQ,KAAK;cAC7BK,MAAM,EAAE,IAAI;cACZC,UAAU,EAAEP,MAAM,CAACQ,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;cACxDhB,OAAO,EAAE;YACX,CAAC;UACH;;UAEA;UACAK,GAAG,CAACE,QAAQ,CAAC,CAACP,OAAO,CAACiB,IAAI,CAAC;YACzBC,IAAI,EAAEZ,MAAM,CAACY,IAAI;YACjBT,EAAE,EAAEH,MAAM,CAACG,EAAE;YACbC,IAAI,EAAEJ,MAAM,CAACI,IAAI;YACjBE,MAAM,EAAEN,MAAM,CAACM;UACjB,CAAC,CAAC;UAEF,OAAOP,GAAG;QACZ,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,MAAMc,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAAClB,cAAc,CAAC;QACvDlC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiD,iBAAiB,CAAC;QAEhE,IAAIA,iBAAiB,CAAClB,MAAM,KAAK,CAAC,EAAE;UAClC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;QAC7D,CAAC,MAAM;UACLT,gBAAgB,CAAC0B,iBAAiB,CAAC;UACnCxB,uBAAuB,CAACwB,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdrD,OAAO,CAACqD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD7B,gBAAgB,CAAC,CAAC;UAChBgB,EAAE,EAAE,OAAO;UACXC,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,aAAaW,KAAK,CAACC,OAAO,EAAE;UACzCX,MAAM,EAAE,IAAI;UACZC,UAAU,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACS,cAAc,CAAC,CAAC;UACvCxB,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;MACL,CAAC,SAAS;QACRT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2B,wBAAwB,GAAIC,YAAY,IAAK;IACjD/B,uBAAuB,CAAC+B,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAI3B,OAAO,IAAK;IACpC,MAAM4B,OAAO,GAAG,CAAC,CAAC;IAClB5B,OAAO,CAAC6B,OAAO,CAACvB,MAAM,IAAI;MACxB,MAAMY,IAAI,GAAGZ,MAAM,CAACY,IAAI;MACxBU,OAAO,CAACV,IAAI,CAAC,GAAG,CAACU,OAAO,CAACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAOE,MAAM,CAACU,OAAO,CAACF,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAACb,IAAI,EAAEc,KAAK,CAAC,KAAK;MACpD,MAAMC,SAAS,GAAG;QAChB,QAAQ,EAAE,KAAK;QACf,KAAK,EAAE,KAAK;QACZ,cAAc,EAAE,OAAO;QACvB,gBAAgB,EAAE,QAAQ;QAC1B,OAAO,EAAE;MACX,CAAC;MAED,OAAO,GAAGA,SAAS,CAACf,IAAI,CAAC,IAAIA,IAAI,KAAKc,KAAK,EAAE;IAC/C,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMC,UAAU,GAAInC,OAAO,IAAK;IAC9B,OAAOA,OAAO,CAACoC,MAAM,CAAC9B,MAAM,IAAIA,MAAM,CAACY,IAAI,KAAK,QAAQ,CAAC;EAC3D,CAAC;EAED,oBACEpD,OAAA,CAACT,IAAI;IAACgF,QAAQ,EAAE/C,OAAQ;IAACgD,GAAG,EAAC,uBAAQ;IAAAC,QAAA,eACnCzE,OAAA,CAACK,aAAa;MAAAoE,QAAA,gBAEZzE,OAAA,CAACJ,kBAAkB;QACjB8E,QAAQ,EAAC,MAAM;QACfC,SAAS,EAAE/D,aAAc;QACzBgE,UAAU,EAAEA,CAAA,KAAM9C,gBAAgB,CAAC,CAAClB,aAAa,CAAE;QAAA6D,QAAA,eAEnDzE,OAAA,CAACe,QAAQ;UAAC8D,KAAK,EAAC,sCAAQ;UAACC,QAAQ,EAAE,KAAM;UAAC9D,MAAM,EAAC,MAAM;UAAAyD,QAAA,eACrDzE,OAAA,CAACX,IAAI;YACH0F,UAAU,EAAC,UAAU;YACrBC,UAAU,EAAEtD,aAAc;YAC1BuD,UAAU,EAAEC,IAAI,iBACdlF,OAAA,CAACX,IAAI,CAAC8F,IAAI;cACRC,OAAO,EAAEA,CAAA,KAAMzB,wBAAwB,CAACuB,IAAI,CAAE;cAC9CG,KAAK,EAAE;gBACLC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAA3D,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEe,EAAE,MAAKuC,IAAI,CAACvC,EAAE,GAAG,SAAS,GAAG,aAAa;gBAC5E6C,OAAO,EAAE,KAAK;gBACdC,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE;cAChB,CAAE;cAAAjB,QAAA,gBAEFzE,OAAA,CAACX,IAAI,CAAC8F,IAAI,CAACQ,IAAI;gBACbd,KAAK,eAAE7E,OAAA;kBAAMqF,KAAK,EAAE;oBAAEO,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAO,CAAE;kBAAApB,QAAA,EAAES,IAAI,CAACtC;gBAAI;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAE;gBACjFpD,WAAW,eAAE7C,OAAA;kBAAMqF,KAAK,EAAE;oBAAEO,QAAQ,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,EAAES,IAAI,CAACrC;gBAAW;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACFjG,OAAA;gBAAKqF,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEM,SAAS,EAAE;gBAAM,CAAE;gBAAAzB,QAAA,gBACjDzE,OAAA;kBAAAyE,QAAA,gBAAKzE,OAAA;oBAAAyE,QAAA,EAAQ;kBAAK;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACpC,gBAAgB,CAACqB,IAAI,CAAChD,OAAO,CAAC;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClEjG,OAAA;kBAAKqF,KAAK,EAAE;oBAAEa,SAAS,EAAE;kBAAM,CAAE;kBAAAzB,QAAA,EAC9BS,IAAI,CAAChD,OAAO,CAAC+B,GAAG,CAACzB,MAAM,iBACtBxC,OAAA,CAACR,KAAK;oBAEJsD,MAAM,EAAEN,MAAM,CAACM,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;oBACzDqD,IAAI,eACFnG,OAAA;sBAAMqF,KAAK,EAAE;wBAAEO,QAAQ,EAAE,MAAM;wBAAEQ,WAAW,EAAE;sBAAM,CAAE;sBAAA3B,QAAA,EACnDjC,MAAM,CAACI;oBAAI;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACP;oBACDZ,KAAK,EAAE;sBAAEgB,OAAO,EAAE,cAAc;sBAAED,WAAW,EAAE;oBAAM;kBAAE,GAPlD5D,MAAM,CAACG,EAAE;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrBjG,OAAA,CAACU,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGdjG,OAAA,CAACJ,kBAAkB;QACjB8E,QAAQ,EAAC,OAAO;QAChBC,SAAS,EAAE9D,cAAe;QAC1B+D,UAAU,EAAEA,CAAA,KAAM7C,iBAAiB,CAAC,CAAClB,cAAc,CAAE;QAAA4D,QAAA,eAErDzE,OAAA,CAACe,QAAQ;UACP8D,KAAK,EAAE,UAAU,CAAAjD,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEgB,IAAI,KAAI,EAAE,EAAG;UACpDkC,QAAQ,EAAE,KAAM;UAChB9D,MAAM,EAAC,MAAM;UAAAyD,QAAA,EAEZ7C,oBAAoB,gBACnB5B,OAAA,CAAAE,SAAA;YAAAuE,QAAA,gBACEzE,OAAA;cAAKqF,KAAK,EAAE;gBAAEK,YAAY,EAAE,MAAM;gBAAEE,QAAQ,EAAE;cAAO,CAAE;cAAAnB,QAAA,eACrDzE,OAAA,CAACV,YAAY;gBACXgH,IAAI,EAAC,OAAO;gBACZC,MAAM,EAAE,CAAE;gBACVC,MAAM,EAAE;kBACNC,KAAK,EAAE;oBAAEb,QAAQ,EAAE;kBAAO,CAAC;kBAC3Bc,OAAO,EAAE;oBAAEd,QAAQ,EAAE;kBAAO;gBAC9B,CAAE;gBAAAnB,QAAA,gBAEFzE,OAAA,CAACV,YAAY,CAAC6F,IAAI;kBAACsB,KAAK,EAAC,0BAAM;kBAAAhC,QAAA,EAAE7C,oBAAoB,CAACgB;gBAAI;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eAC/EjG,OAAA,CAACV,YAAY,CAAC6F,IAAI;kBAACsB,KAAK,EAAC,0BAAM;kBAAAhC,QAAA,EAAE7C,oBAAoB,CAACiB;gBAAW;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACtFjG,OAAA,CAACV,YAAY,CAAC6F,IAAI;kBAACsB,KAAK,EAAC,gCAAO;kBAAAhC,QAAA,EAAEJ,UAAU,CAACzC,oBAAoB,CAACM,OAAO,CAAC,CAACC;gBAAM;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,EAGL5B,UAAU,CAACzC,oBAAoB,CAACM,OAAO,CAAC,CAACyE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC1C,GAAG,CAAC2C,MAAM,iBAC9D5G,OAAA;cAAqBqF,KAAK,EAAE;gBAAEK,YAAY,EAAE;cAAO,CAAE;cAAAjB,QAAA,gBACnDzE,OAAA;gBAAKqF,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEF,YAAY,EAAE,KAAK;kBAAEG,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,eACxEzE,OAAA,CAACR,KAAK;kBACJsD,MAAM,EAAE8D,MAAM,CAAC9D,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;kBACzDqD,IAAI,EAAES,MAAM,CAAChE;gBAAK;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjG,OAAA,CAACkB,cAAc;gBAAAuD,QAAA,eACbzE,OAAA,CAACoB,gBAAgB;kBAAAqD,QAAA,EACdmC,MAAM,CAAC9D,MAAM,KAAK,QAAQ,gBACzB9C,OAAA,CAACF,WAAW;oBACV+G,QAAQ,EAAED,MAAM,CAACjE,EAAG;oBACpBmE,OAAO,EAAEF,MAAM,CAACE;kBAAQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,gBAEFjG,OAAA,CAAAE,SAAA;oBAAAuE,QAAA,EAAE;kBAAK,gBAAE;gBACV;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GAlBTW,MAAM,CAACjE,EAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBd,CACN,CAAC,EAED5B,UAAU,CAACzC,oBAAoB,CAACM,OAAO,CAAC,CAACC,MAAM,KAAK,CAAC,iBACpDnC,OAAA;cAAKqF,KAAK,EAAE;gBAAE0B,SAAS,EAAE,QAAQ;gBAAEvB,OAAO,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAC;YAExD;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,eACD,CAAC,gBAEHjG,OAAA;YAAKqF,KAAK,EAAE;cAAE0B,SAAS,EAAE,QAAQ;cAAEvB,OAAO,EAAE;YAAS,CAAE;YAAAf,QAAA,EAAC;UAExD;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAAC1E,EAAA,CA7OID,cAAc;AAAA0F,GAAA,GAAd1F,cAAc;AA+OpB,eAAeA,cAAc;AAAC,IAAAf,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA2F,GAAA;AAAAC,YAAA,CAAA1G,EAAA;AAAA0G,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}