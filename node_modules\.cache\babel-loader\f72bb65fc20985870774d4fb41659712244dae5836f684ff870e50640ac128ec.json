{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel = null; // 存储原始模型数据\nlet skeleton = null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (!lastPosition) {\n      lastPosition = newPos.clone();\n      return newPos;\n    }\n    const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n    const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n    const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n    lastPosition.set(filteredX, filteredY, filteredZ);\n    return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (lastRotation === null) {\n      lastRotation = newRotation;\n      return newRotation;\n    }\n\n    // 处理角度跳变（从360度到0度或反之）\n    let diff = newRotation - lastRotation;\n    if (diff > Math.PI) diff -= 2 * Math.PI;\n    if (diff < -Math.PI) diff += 2 * Math.PI;\n    const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n    lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  cleanup() {\n    // 清理动作\n    this.actions.forEach((actions, mixer) => {\n      actions.forEach(action => {\n        action.stop();\n        action.unbind();\n      });\n      actions.clear();\n    });\n    this.actions.clear();\n\n    // 清理混合器\n    this.mixers.forEach(mixer => {\n      mixer.stopAllAction();\n      const root = mixer.getRoot();\n      if (root) {\n        root.traverse(object => {\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n        mixer.uncacheRoot(root);\n        mixer.uncacheAction(null, root);\n        mixer.uncacheClip(null);\n      }\n    });\n    this.mixers.clear();\n\n    // 清理骨骼\n    this.bones.forEach(bone => {\n      if (bone.parent) {\n        bone.parent.remove(bone);\n      }\n      if (bone.matrix) bone.matrix.identity();\n      if (bone.matrixWorld) bone.matrixWorld.identity();\n    });\n    this.bones.clear();\n\n    // 清理模型\n    this.models.forEach(model => {\n      if (model.parent) {\n        model.parent.remove(model);\n      }\n      model.traverse(object => {\n        if (object.isMesh) {\n          if (object.geometry) {\n            object.geometry.dispose();\n          }\n          if (object.material) {\n            if (Array.isArray(object.material)) {\n              object.material.forEach(material => {\n                if (material.map) material.map.dispose();\n                material.dispose();\n              });\n            } else {\n              if (object.material.map) object.material.map.dispose();\n              object.material.dispose();\n            }\n          }\n        }\n        if (object.animations) {\n          object.animations.length = 0;\n        }\n      });\n    });\n    this.models.clear();\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = model => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  var _window$intersections2;\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = useRef(new Map()); // 存储所有行人的动画混合器\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',\n    // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 添加性能统计切换按钮样式\n  const perfButtonStyle = {\n    position: 'fixed',\n    top: '10px',\n    right: '10px',\n    padding: '5px 10px',\n    background: 'rgba(0,0,0,0.5)',\n    color: 'white',\n    border: 'none',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    zIndex: 1000\n  };\n\n  // 添加性能统计显示面板样式\n  const perfPanelStyle = {\n    position: 'fixed',\n    top: '50px',\n    right: '10px',\n    padding: '10px',\n    background: 'rgba(0,0,0,0.7)',\n    color: 'white',\n    borderRadius: '4px',\n    zIndex: 1000,\n    fontFamily: 'monospace',\n    fontSize: '12px',\n    minWidth: '200px'\n  };\n\n  // 添加帧率和性能监控状态\n  const [showPerformanceStats, setShowPerformanceStats] = useState(false);\n  const [performanceStats, setPerformanceStats] = useState({\n    fps: 0,\n    memory: {\n      total: 0,\n      used: 0,\n      limit: 0,\n      percent: 0\n    },\n    resourceCounts: {\n      mixers: 0,\n      models: 0,\n      people: 0\n    }\n  });\n\n  // 添加帧率计算\n  const fpsCounterRef = useRef({\n    frames: 0,\n    lastTime: performance.now(),\n    fps: 0\n  });\n\n  // 添加帧率计算函数\n  const updateFPS = () => {\n    fpsCounterRef.current.frames++;\n    const now = performance.now();\n    const elapsed = now - fpsCounterRef.current.lastTime;\n    if (elapsed >= 1000) {\n      fpsCounterRef.current.fps = Math.round(fpsCounterRef.current.frames * 1000 / elapsed);\n      fpsCounterRef.current.frames = 0;\n      fpsCounterRef.current.lastTime = now;\n\n      // 更新性能统计信息\n      if (showPerformanceStats) {\n        const memory = window.performance && window.performance.memory ? {\n          total: Math.round(window.performance.memory.totalJSHeapSize / (1024 * 1024)),\n          used: Math.round(window.performance.memory.usedJSHeapSize / (1024 * 1024)),\n          limit: Math.round(window.performance.memory.jsHeapSizeLimit / (1024 * 1024)),\n          percent: Math.round(window.performance.memory.usedJSHeapSize / window.performance.memory.jsHeapSizeLimit * 100)\n        } : {\n          total: 0,\n          used: 0,\n          limit: 0,\n          percent: 0\n        };\n        setPerformanceStats({\n          fps: fpsCounterRef.current.fps,\n          memory,\n          resourceCounts: {\n            mixers: resourceManager ? resourceManager.mixers.size : 0,\n            models: resourceManager ? resourceManager.models.size : 0,\n            people: peopleAnimationMixers.current.size\n          }\n        });\n      }\n    }\n  };\n\n  // 添加一个全局方法用于显示所有红绿灯ID\n  window.listTrafficLights = () => {\n    console.log('红绿灯列表:');\n    if (!trafficLightsMap || trafficLightsMap.size === 0) {\n      console.log('当前没有红绿灯对象');\n      return [];\n    }\n    const list = [];\n    trafficLightsMap.forEach((light, id) => {\n      console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n      list.push({\n        id,\n        name: light.intersection.name,\n        position: light.position\n      });\n    });\n    return list;\n  };\n\n  // 添加全局调试方法\n  // window.debugScene = function() {\n  //   if (!scene) {\n  //     console.error('场景未初始化，无法调试红绿灯');\n  //     return;\n  //   }\n\n  //   console.log('开始调试红绿灯模型...');\n\n  //   // 检查红绿灯映射\n  //   console.log('红绿灯映射数量:', trafficLightsMap.size);\n\n  //   // 统计场景中的可互动对象\n  //   let interactiveObjects = 0;\n  //   let trafficLightObjects = 0;\n\n  //   // 遍历场景中的所有对象\n  //   scene.traverse((object) => {\n  //     // 检查是否有userData\n  //     if (object.userData && Object.keys(object.userData).length > 0) {\n  //       interactiveObjects++;\n\n  //       // 检查是否是红绿灯\n  //       if (object.userData.type === 'trafficLight') {\n  //         trafficLightObjects++;\n  //         console.log('找到红绿灯对象:', {\n  //           名称: object.name || '无名称',\n  //           类型: object.userData.type,\n  //           路口ID: object.userData.interId,\n  //           位置: object.position.toArray(),\n  //           可见性: object.visible,\n  //           是否是网格: object.isMesh,\n  //           userData: object.userData\n  //         });\n  //       }\n  //     }\n  //   });\n\n  //   console.log('场景中的可互动对象数量:', interactiveObjects);\n  //   console.log('红绿灯对象数量:', trafficLightObjects);\n\n  //   // 遍历红绿灯映射，创建高亮标记\n  //   trafficLightsMap.forEach((lightObj, interId) => {\n  //     if (lightObj.model) {\n  //       // 获取红绿灯位置\n  //       const position = lightObj.model.position.clone();\n\n  //       // 创建一个高亮球体\n  //       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  //       const highlightMaterial = new THREE.MeshBasicMaterial({ \n  //         color: 0xff00ff, \n  //         transparent: true,\n  //         opacity: 0.7\n  //       });\n  //       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n\n  //       // 设置位置，稍微偏移，避免遮挡\n  //       highlightMesh.position.set(position.x, position.y + 15, position.z);\n\n  //       // 添加到场景\n  //       scene.add(highlightMesh);\n\n  //       // 添加用户数据，方便调试\n  //       highlightMesh.userData = {\n  //         type: 'trafficLightHighlight',\n  //         interId: interId,\n  //         name: lightObj.intersection.name,\n  //         originalPosition: position.toArray()\n  //       };\n\n  //       // 5秒后自动移除高亮标记\n  //       setTimeout(() => {\n  //         scene.remove(highlightMesh);\n  //       }, 5000);\n\n  //       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n  //     }\n  //   });\n\n  //   // 将调试信息显示在控制台\n  //   console.log('红绿灯调试信息:', {\n  //     红绿灯映射数量: trafficLightsMap.size,\n  //     红绿灯状态数量: trafficLightStates.size,\n  //     场景中红绿灯对象数量: trafficLightObjects,\n  //     射线检测启用: true\n  //   });\n  // };\n\n  // 添加全局测试弹窗函数\n  window.showTrafficLightPopup = interId => {\n    try {\n      // 确保interId为字符串类型\n      interId = String(interId);\n      console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n      console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n\n      // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n      let trafficLight = trafficLightsMap.get(interId);\n      if (!trafficLight) {\n        // 尝试转换为数字查找\n        const numericId = parseInt(interId);\n        trafficLight = trafficLightsMap.get(numericId);\n        if (trafficLight) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n          interId = numericId; // 更新interId为找到的正确类型\n        }\n      }\n      if (!trafficLight) {\n        console.error('未找到指定ID的红绿灯:', interId);\n        return false;\n      }\n      const stateInfo = trafficLightStates.get(interId);\n      const intersection = trafficLight.intersection;\n\n      // 判断是否有相位数据\n      const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n      let content;\n\n      // 指北针样式\n      const compassStyle = {\n        position: 'absolute',\n        top: '5px',\n        right: '25px',\n        width: '30px',\n        height: '30px',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        borderRadius: '50%',\n        background: 'rgba(0,0,0,0.1)',\n        zIndex: 10\n      };\n\n      // 指北针组件\n      const CompassIcon = () => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: compassStyle,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            transform: 'rotate(0deg)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ff5252',\n              fontSize: '14px',\n              fontWeight: 'bold',\n              lineHeight: '14px'\n            },\n            children: \"N\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 0,\n              height: 0,\n              borderLeft: '6px solid transparent',\n              borderRight: '6px solid transparent',\n              borderBottom: '10px solid #ff5252',\n              marginTop: '-2px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 9\n      }, this);\n      if (hasPhaseData) {\n        // 相位ID与方向/方式的映射\n        const phaseMap = {\n          '1': {\n            dir: 'N',\n            type: 'left'\n          },\n          '2': {\n            dir: 'N',\n            type: 'straight'\n          },\n          '3': {\n            dir: 'N',\n            type: 'right'\n          },\n          '5': {\n            dir: 'E',\n            type: 'left'\n          },\n          '6': {\n            dir: 'E',\n            type: 'straight'\n          },\n          '7': {\n            dir: 'E',\n            type: 'right'\n          },\n          '9': {\n            dir: 'S',\n            type: 'left'\n          },\n          '10': {\n            dir: 'S',\n            type: 'straight'\n          },\n          '11': {\n            dir: 'S',\n            type: 'right'\n          },\n          '13': {\n            dir: 'W',\n            type: 'left'\n          },\n          '14': {\n            dir: 'W',\n            type: 'straight'\n          },\n          '15': {\n            dir: 'W',\n            type: 'right'\n          }\n        };\n        const typeOrder = ['left', 'straight', 'right'];\n        const colorMap = {\n          G: '#00ff00',\n          Y: '#ffff00',\n          R: '#ff0000'\n        };\n        const dirData = {\n          N: {},\n          E: {},\n          S: {},\n          W: {}\n        };\n        stateInfo.phases.forEach(phase => {\n          const map = phaseMap[phase.phaseId];\n          if (map) {\n            dirData[map.dir][map.type] = {\n              color: colorMap[phase.trafficLight] || '#888',\n              remainTime: phase.remainTime\n            };\n          }\n        });\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '260px',\n            background: 'rgba(0,0,0,0.05)',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '8px',\n              fontSize: '15px',\n              textAlign: 'center'\n            },\n            children: [intersection.name, \"\\u706F\\u6001\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateRows: '60px 60px 60px',\n              gridTemplateColumns: '60px 60px 60px',\n              justifyContent: 'center',\n              alignItems: 'center',\n              background: 'rgba(255,255,255,0.05)',\n              borderRadius: '8px',\n              margin: '0 auto',\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                gridRow: 1,\n                gridColumn: 2,\n                textAlign: 'center',\n                display: 'flex',\n                justifyContent: 'center',\n                width: '100%'\n              },\n              children: typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n                const currentType = typeOrder[displayIndex];\n\n                // 计算与南边对齐的样式\n                const marginStyle = {};\n                if (currentType === 'left') {\n                  // 左转箭头 (右侧显示)\n                  marginStyle.marginRight = '0px';\n                } else if (currentType === 'straight') {\n                  // 直行箭头 (中间显示)\n                  marginStyle.marginLeft = '10px';\n                  marginStyle.marginRight = '10px';\n                } else if (currentType === 'right') {\n                  // 右转箭头 (左侧显示)\n                  marginStyle.marginLeft = '0px';\n                }\n                return dirData.N[currentType] &&\n                /*#__PURE__*/\n                // <div key={currentType} style={{\n                //   display: 'flex', \n                //   flexDirection: 'column', \n                //   alignItems: 'center',\n                //   ...marginStyle\n                // }}>\n                _jsxDEV(\"div\", {\n                  style: {\n                    marginRight: currentType === 'left' ? 0 : '10px',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '14px',\n                      color: dirData.N[currentType].color,\n                      fontWeight: 'bold',\n                      marginBottom: '3px'\n                    },\n                    children: dirData.N[currentType].remainTime\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: dirData.N[currentType].color,\n                      fontSize: '20px',\n                      lineHeight: '20px'\n                    },\n                    children: currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 23\n                  }, this)]\n                }, currentType, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                gridRow: 3,\n                gridColumn: 2,\n                textAlign: 'center',\n                display: 'flex',\n                justifyContent: 'center',\n                width: '100%'\n              },\n              children: typeOrder.map(type => dirData.S[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginRight: type === 'right' ? 0 : '10px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.S[type].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.S[type].color,\n                    fontWeight: 'bold',\n                    marginTop: '3px'\n                  },\n                  children: dirData.S[type].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 21\n                }, this)]\n              }, type, true, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                gridRow: 2,\n                gridColumn: 3,\n                textAlign: 'center'\n              },\n              children: typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n                const currentType = typeOrder[displayIndex];\n                return dirData.E[currentType] && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'flex-start'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: dirData.E[currentType].color,\n                      fontSize: '20px',\n                      lineHeight: '20px'\n                    },\n                    children: currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '14px',\n                      color: dirData.E[currentType].color,\n                      fontWeight: 'bold',\n                      marginLeft: '5px'\n                    },\n                    children: dirData.E[currentType].remainTime\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 23\n                  }, this)]\n                }, currentType, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 816,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                gridRow: 2,\n                gridColumn: 1,\n                textAlign: 'center'\n              },\n              children: typeOrder.map(type => dirData.W[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'flex-end'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.W[type].color,\n                    fontWeight: 'bold',\n                    marginRight: '5px'\n                  },\n                  children: dirData.W[type].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.W[type].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 21\n                }, this)]\n              }, type, true, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '8px',\n              fontSize: '11px',\n              color: '#888',\n              textAlign: 'center'\n            },\n            children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 11\n        }, this);\n      } else {\n        // 没有相位数据时显示的内容\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '15px',\n            width: '260px',\n            background: 'rgba(0,0,0,0.05)',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '10px',\n              fontSize: '15px',\n              textAlign: 'center'\n            },\n            children: intersection.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px 0',\n              color: '#ff9800',\n              fontSize: '14px',\n              fontWeight: 'bold',\n              background: 'rgba(255,255,255,0.1)',\n              borderRadius: '8px',\n              marginBottom: '10px'\n            },\n            children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#888',\n              textAlign: 'center'\n            },\n            children: [\"\\u8DEF\\u53E3ID: \", interId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '8px',\n              fontSize: '11px',\n              color: '#888',\n              textAlign: 'center'\n            },\n            children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 11\n        }, this);\n      }\n\n      // 设置弹窗位置在左上角区域\n      const x = 445;\n      const y = 250;\n\n      // 更新当前显示的红绿灯ID引用\n      if (window.currentPopoverIdRef) {\n        window.currentPopoverIdRef.current = interId;\n      }\n\n      // 取消之前的更新定时器（如果存在）\n      if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n        clearInterval(window.trafficLightUpdateTimerRef.current);\n        window.trafficLightUpdateTimerRef.current = null;\n      }\n\n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: {\n            x,\n            y\n          },\n          content: content,\n          phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n        });\n\n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(() => {\n            window.showTrafficLightPopup(interId);\n          }, 1000);\n        }\n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    } catch (error) {\n      console.error('显示红绿灯弹窗失败:', error);\n      return false;\n    }\n  };\n\n  // // 添加全局模拟SPAT数据生成函数\n  // window.generateMockSpatData = () => {\n  //   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n  //     console.error('红绿灯映射为空，无法生成模拟数据');\n  //     return false;\n  //   }\n\n  //   console.log('开始生成模拟SPAT数据...');\n\n  //   // 可能的相位ID和对应方向\n  //   const phaseIds = [\n  //     '1', '2', '3',  // 北进口\n  //     '5', '6', '7',  // 东进口 \n  //     '9', '10', '11', // 南进口\n  //     '13', '14', '15' // 西进口\n  //   ];\n\n  //   // 准备SPAT数据结构\n  //   const spatMessage = {\n  //     data: {\n  //       intersections: []\n  //     },\n  //     tm: Date.now(),\n  //     source: 2,\n  //     type: 'SPAT',\n  //     mac: 'mock-device-id'\n  //   };\n\n  //   // 为每个红绿灯生成模拟数据\n  //   trafficLightsMap.forEach((light, interId) => {\n  //     // 为该路口生成3-6个随机相位\n  //     const phaseCount = Math.floor(Math.random() * 4) + 3;\n  //     const phases = [];\n\n  //     // 随机选择相位ID\n  //     const selectedPhaseIds = [];\n  //     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n  //       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n  //       const phaseId = phaseIds[randomIndex];\n\n  //       // 避免重复选择同一个ID\n  //       if (!selectedPhaseIds.includes(phaseId)) {\n  //         selectedPhaseIds.push(phaseId);\n  //       }\n  //     }\n\n  //     // 为每个选中的相位生成随机状态\n  //     selectedPhaseIds.forEach(phaseId => {\n  //       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n  //       const lightIndex = Math.floor(Math.random() * 3);\n  //       const stateLabels = ['red', 'yellow', 'green'];\n\n  //       // 随机生成剩余时间 (1-60秒)\n  //       const remainTime = Math.floor(Math.random() * 60) + 1;\n\n  //       // 添加相位信息 - 符合实际数据结构\n  //       phases.push({\n  //         phaseId: phaseId,\n  //         state: stateLabels[lightIndex],\n  //         remainTime: remainTime\n  //       });\n  //     });\n\n  //     // 添加交叉口数据到SPAT消息\n  //     spatMessage.data.intersections.push({\n  //       id: interId,\n  //       interId: interId, // 确保包含interId字段\n  //       phases: phases\n  //     });\n\n  //     // 同时更新本地状态方便测试\n  //     const phaseInfos = phases.map(phase => ({\n  //       phaseId: phase.phaseId,\n  //       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n  //       remainTime: phase.remainTime,\n  //       lastUpdate: Date.now()\n  //     }));\n\n  //     // 使用与trafficLightsMap相同的ID类型存储状态信息\n  //     trafficLightStates.set(interId, {\n  //       updateTime: Date.now(),\n  //       phases: phaseInfos\n  //     });\n\n  //     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n  //   });\n\n  //   // 模拟调用SPAT消息处理函数\n  //   try {\n  //     console.log('处理模拟SPAT消息:', spatMessage);\n  //     const message = JSON.stringify(spatMessage);\n  //     // 间接通过handleMqttMessage处理模拟数据\n  //     handleMqttMessage(MQTT_CONFIG.spat, message);\n  //   } catch (error) {\n  //     console.error('处理模拟SPAT消息失败:', error);\n  //   }\n\n  //   console.log('模拟SPAT数据生成完成');\n  //   return true;\n  // };\n\n  // // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n  // window.testTrafficLightWithMockData = (interId) => {\n  //   // 先生成模拟数据\n  //   window.generateMockSpatData();\n\n  //   // 延迟100ms确保数据已生成\n  //   setTimeout(() => {\n  //     // 显示红绿灯弹窗\n  //     window.showTrafficLightPopup(interId);\n  //   }, 100);\n\n  //   return '模拟数据已生成，正在显示红绿灯弹窗...';\n  // };\n\n  // 帮助函数：从对象或其父对象中找到红绿灯对象\n  const getTrafficLightFromObject = object => {\n    let current = object;\n\n    // 如果对象本身就有红绿灯数据，直接返回\n    if (current && current.userData && current.userData.type === 'trafficLight') {\n      console.log('直接找到红绿灯对象:', current.name || '无名称');\n      return current;\n    }\n\n    // 向上查找父对象，直到找到红绿灯或到达顶层\n    while (current && current.parent) {\n      current = current.parent;\n      if (current.userData && current.userData.type === 'trafficLight') {\n        console.log('从父对象找到红绿灯:', current.name || '无名称');\n        return current;\n      }\n    }\n    return null;\n  };\n\n  // 添加调试工具：强制进行点击测试\n  window.testClickDetection = (x, y) => {\n    try {\n      console.log('执行强制点击测试 @ 位置:', x, y);\n\n      // 找到渲染器的DOM元素\n      const canvas = document.querySelector('canvas');\n      if (!canvas) {\n        console.error('找不到THREE.js的canvas元素');\n        return false;\n      }\n\n      // 确保scene和camera已定义\n      if (!scene || !cameraRef.current) {\n        console.error('scene或camera未定义');\n        return false;\n      }\n\n      // 如果没有传入坐标，使用屏幕中心点\n      if (x === undefined || y === undefined) {\n        x = window.innerWidth / 2;\n        y = window.innerHeight / 2;\n      }\n\n      // 计算归一化设备坐标 (-1 到 +1)\n      const rect = canvas.getBoundingClientRect();\n      const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n      const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n      console.log('归一化坐标:', mouseX, mouseY);\n\n      // 创建一个射线\n      const raycaster = new THREE.Raycaster();\n      raycaster.params.Points.threshold = 5;\n      raycaster.params.Line.threshold = 5;\n      const mouseVector = new THREE.Vector2(mouseX, mouseY);\n      raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n      // 收集所有红绿灯对象\n      const trafficLightObjects = [];\n      trafficLightsMap.forEach((lightObj, interId) => {\n        if (lightObj.model) {\n          trafficLightObjects.push(lightObj.model);\n          console.log(`添加红绿灯 ${interId} 到检测列表`);\n        }\n      });\n\n      // 直接对红绿灯对象进行碰撞检测\n      console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n      const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n      if (tlIntersects.length > 0) {\n        console.log('成功点击到红绿灯对象!');\n        tlIntersects.forEach((intersect, i) => {\n          console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n          // 尝试获取红绿灯ID\n          const obj = getTrafficLightFromObject(intersect.object);\n          if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n            console.log('找到红绿灯ID:', obj.userData.interId);\n          }\n        });\n        return true;\n      }\n\n      // 对整个场景进行碰撞检测\n      console.log('对整个场景进行碰撞检测...');\n      const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n      console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n      sceneIntersects.forEach((intersect, i) => {\n        const obj = intersect.object;\n        console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n      });\n\n      // 测试红绿灯的可见性\n      console.log('检查红绿灯的可见性...');\n      let visibleCount = 0;\n      trafficLightsMap.forEach((lightObj, interId) => {\n        if (lightObj.model) {\n          var _lightObj$intersectio;\n          // 检查红绿灯模型是否可见\n          let isVisible = lightObj.model.visible;\n          let frustumVisible = true;\n\n          // 获取世界位置\n          const worldPos = new THREE.Vector3();\n          lightObj.model.getWorldPosition(worldPos);\n\n          // 计算到摄像机的距离\n          const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n          // 检查是否在视锥体内\n          const screenPos = worldPos.clone().project(cameraRef.current);\n          if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n            frustumVisible = false;\n          }\n          if (isVisible) {\n            visibleCount++;\n          }\n          console.log(`红绿灯 ${interId}:`, {\n            名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n            可见性: isVisible,\n            在视锥体内: frustumVisible,\n            世界位置: worldPos.toArray(),\n            屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n            与摄像机距离: distanceToCamera\n          });\n        }\n      });\n      console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n      // 如果未检测到任何交叉点\n      return sceneIntersects.length > 0;\n    } catch (error) {\n      console.error('点击测试失败:', error);\n      return false;\n    }\n  };\n\n  // 添加更新红绿灯视觉效果的函数\n  const updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n    var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n    if (!trafficLight || !trafficLight.model || !phaseInfo) {\n      return;\n    }\n\n    // 移除旧的灯光模型(如果存在)\n    const lightsToRemove = [];\n    trafficLight.model.traverse(child => {\n      if (child.userData && child.userData.isLight) {\n        lightsToRemove.push(child);\n      }\n    });\n    lightsToRemove.forEach(light => {\n      trafficLight.model.remove(light);\n    });\n\n    // 根据状态获取颜色\n    let lightColor;\n    switch (phaseInfo.trafficLight) {\n      case 'G':\n        lightColor = 0x00FF00; // 绿色\n        break;\n      case 'Y':\n        lightColor = 0xFFFF00; // 黄色\n        break;\n      case 'R':\n      default:\n        lightColor = 0xFF0000; // 红色\n        break;\n    }\n\n    // 创建一个球体作为灯光\n    const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n    const lightMaterial = new THREE.MeshBasicMaterial({\n      color: lightColor,\n      emissive: lightColor,\n      emissiveIntensity: 1\n    });\n    const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n    lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n    lightMesh.userData = {\n      isLight: true,\n      type: 'trafficLight',\n      interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n      phaseId: phaseInfo.phaseId,\n      direction: phaseInfo.direction,\n      remainTime: phaseInfo.remainTime\n    };\n\n    // 添加光源使灯光更明显\n    const light = new THREE.PointLight(lightColor, 1, 50);\n    light.position.set(0, 12, 0);\n    light.userData = {\n      isLight: true\n    };\n\n    // 将灯光添加到交通灯模型\n    trafficLight.model.add(lightMesh);\n    trafficLight.model.add(light);\n    console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: className,\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      style: perfButtonStyle,\n      onClick: () => setShowPerformanceStats(!showPerformanceStats),\n      children: showPerformanceStats ? '隐藏性能' : '显示性能'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1261,\n      columnNumber: 7\n    }, this), showPerformanceStats && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: perfPanelStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '5px',\n          fontWeight: 'bold'\n        },\n        children: \"\\u6027\\u80FD\\u76D1\\u63A7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1271,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: performanceStats.fps < 30 ? '#ff5555' : performanceStats.fps < 50 ? '#ffff55' : '#55ff55'\n        },\n        children: [\"FPS: \", performanceStats.fps]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1272,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u5185\\u5B58: \", performanceStats.memory.used, \"/\", performanceStats.memory.total, \" MB (\", performanceStats.memory.percent, \"%)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1275,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u52A8\\u753B\\u6DF7\\u5408\\u5668: \", performanceStats.resourceCounts.mixers]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1279,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u6A21\\u578B\\u6570\\u91CF: \", performanceStats.resourceCounts.models]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1282,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u884C\\u4EBA\\u6570\\u91CF: \", performanceStats.resourceCounts.people]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1285,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1270,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? 'rgba(70, 130, 180, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? '#fff' : '#333'\n        },\n        onClick: () => setViewMode('global'),\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'first-person' ? 'rgba(70, 130, 180, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'first-person' ? '#fff' : '#333'\n        },\n        onClick: () => setViewMode('first-person'),\n        children: \"\\u9A7E\\u9A76\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'third-person' ? 'rgba(70, 130, 180, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'third-person' ? '#fff' : '#333'\n        },\n        onClick: () => setViewMode('third-person'),\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u8DEF\\u53E3:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      style: intersectionSelectStyle,\n      value: selectedIntersection ? selectedIntersection.id : '',\n      onChange: e => {\n        var _window$intersections;\n        const id = e.target.value;\n        const intersection = (_window$intersections = window.intersections) === null || _window$intersections === void 0 ? void 0 : _window$intersections.find(i => i.id === id);\n        setSelectedIntersection(intersection || null);\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n        value: \"\",\n        children: \"\\u9009\\u62E9\\u8DEF\\u53E3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1336,\n        columnNumber: 9\n      }, this), (_window$intersections2 = window.intersections) === null || _window$intersections2 === void 0 ? void 0 : _window$intersections2.map(intersection => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: intersection.id,\n        children: intersection.name\n      }, intersection.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1338,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1327,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        zIndex: 1000,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n        minWidth: '230px',\n        pointerEvents: 'auto'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            borderBottom: '1px solid rgba(255, 255, 255, 0.2)',\n            marginBottom: '8px',\n            paddingBottom: '5px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            style: {\n              fontSize: '14px'\n            },\n            children: \"\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1369,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '16px',\n              cursor: 'pointer',\n              opacity: 0.7,\n              transition: 'opacity 0.2s'\n            },\n            onClick: () => setTrafficLightPopover({\n              ...trafficLightPopover,\n              visible: false\n            }),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1361,\n          columnNumber: 13\n        }, this), trafficLightPopover.content]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1360,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1346,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1259,\n    columnNumber: 5\n  }, this);\n};\n_s(CampusModel, \"ZuHTpSp4OlxfT0ub7LPG/VXzVrc=\");\n_c = CampusModel;\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "intersectionsData", "jsxDEV", "_jsxDEV", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "mixersToCleanup", "Set", "bonesMap", "resourceManager", "mixers", "bones", "actions", "models", "addMixer", "mixer", "model", "add", "traverse", "object", "isBone", "uuid", "addAction", "action", "cleanup", "for<PERSON>ach", "stop", "unbind", "clear", "stopAllAction", "root", "getRoot", "animations", "length", "uncacheRoot", "uncacheAction", "uncacheClip", "bone", "parent", "remove", "matrix", "identity", "matrixWorld", "<PERSON><PERSON><PERSON>", "geometry", "dispose", "material", "map", "createAnimationMixer", "AnimationMixer", "createAction", "clip", "clipAction", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "_window$intersections2", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "perfButtonStyle", "right", "background", "perfPanelStyle", "fontFamily", "min<PERSON><PERSON><PERSON>", "showPerformanceStats", "setShowPerformanceStats", "performanceStats", "setPerformanceStats", "fps", "memory", "total", "used", "limit", "percent", "resourceCounts", "people", "fpsCounterRef", "frames", "lastTime", "performance", "updateFPS", "current", "elapsed", "round", "totalJSHeapSize", "usedJSHeapSize", "jsHeapSizeLimit", "size", "listTrafficLights", "list", "light", "id", "intersection", "name", "push", "showTrafficLightPopup", "String", "trafficLight", "numericId", "parseInt", "stateInfo", "hasPhaseData", "compassStyle", "height", "justifyContent", "alignItems", "CompassIcon", "style", "children", "flexDirection", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderLeft", "borderRight", "borderBottom", "marginTop", "phaseMap", "dir", "type", "typeOrder", "colorMap", "G", "Y", "R", "dirD<PERSON>", "N", "E", "S", "W", "phase", "phaseId", "remainTime", "marginBottom", "textAlign", "gridTemplateRows", "gridTemplateColumns", "margin", "gridRow", "gridColumn", "index", "displayIndex", "currentType", "marginStyle", "marginRight", "marginLeft", "toLocaleTimeString", "clearInterval", "setInterval", "getTrafficLightFromObject", "userData", "testClickDetection", "canvas", "document", "querySelector", "undefined", "innerWidth", "innerHeight", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "params", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "lightObj", "tlIntersects", "intersectObjects", "intersect", "i", "toArray", "obj", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "worldPos", "getWorldPosition", "distanceToCamera", "screenPos", "project", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "updateTrafficLightVisual", "phaseInfo", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "child", "isLight", "lightColor", "lightGeometry", "SphereGeometry", "lightMaterial", "MeshBasicMaterial", "emissive", "emissiveIntensity", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "direction", "PointLight", "ref", "onClick", "value", "onChange", "e", "_window$intersections", "target", "intersections", "pointerEvents", "paddingBottom", "opacity", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\n\n\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  \n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  \n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  \n  cleanup() {\n    // 清理动作\n    this.actions.forEach((actions, mixer) => {\n      actions.forEach(action => {\n        action.stop();\n        action.unbind();\n      });\n      actions.clear();\n    });\n    this.actions.clear();\n    \n    // 清理混合器\n    this.mixers.forEach(mixer => {\n      mixer.stopAllAction();\n      const root = mixer.getRoot();\n      if (root) {\n        root.traverse(object => {\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n        mixer.uncacheRoot(root);\n        mixer.uncacheAction(null, root);\n        mixer.uncacheClip(null);\n      }\n    });\n    this.mixers.clear();\n    \n    // 清理骨骼\n    this.bones.forEach(bone => {\n      if (bone.parent) {\n        bone.parent.remove(bone);\n      }\n      if (bone.matrix) bone.matrix.identity();\n      if (bone.matrixWorld) bone.matrixWorld.identity();\n    });\n    this.bones.clear();\n    \n    // 清理模型\n    this.models.forEach(model => {\n      if (model.parent) {\n        model.parent.remove(model);\n      }\n      model.traverse(object => {\n        if (object.isMesh) {\n          if (object.geometry) {\n            object.geometry.dispose();\n          }\n          if (object.material) {\n            if (Array.isArray(object.material)) {\n              object.material.forEach(material => {\n                if (material.map) material.map.dispose();\n                material.dispose();\n              });\n            } else {\n              if (object.material.map) object.material.map.dispose();\n              object.material.dispose();\n            }\n          }\n        }\n        if (object.animations) {\n          object.animations.length = 0;\n        }\n      });\n    });\n    this.models.clear();\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = (model) => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = useRef(new Map()); // 存储所有行人的动画混合器\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  \n  \n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',  // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 添加性能统计切换按钮样式\n  const perfButtonStyle = {\n    position: 'fixed',\n    top: '10px',\n    right: '10px',\n    padding: '5px 10px',\n    background: 'rgba(0,0,0,0.5)',\n    color: 'white',\n    border: 'none',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    zIndex: 1000\n  };\n  \n  // 添加性能统计显示面板样式\n  const perfPanelStyle = {\n    position: 'fixed',\n    top: '50px',\n    right: '10px',\n    padding: '10px',\n    background: 'rgba(0,0,0,0.7)',\n    color: 'white',\n    borderRadius: '4px',\n    zIndex: 1000,\n    fontFamily: 'monospace',\n    fontSize: '12px',\n    minWidth: '200px'\n  };\n\n  // 添加帧率和性能监控状态\n  const [showPerformanceStats, setShowPerformanceStats] = useState(false);\n  const [performanceStats, setPerformanceStats] = useState({\n    fps: 0,\n    memory: {\n      total: 0,\n      used: 0,\n      limit: 0,\n      percent: 0\n    },\n    resourceCounts: {\n      mixers: 0,\n      models: 0,\n      people: 0\n    }\n  });\n  \n  // 添加帧率计算\n  const fpsCounterRef = useRef({\n    frames: 0,\n    lastTime: performance.now(),\n    fps: 0\n  });\n  \n  // 添加帧率计算函数\n  const updateFPS = () => {\n    fpsCounterRef.current.frames++;\n    \n    const now = performance.now();\n    const elapsed = now - fpsCounterRef.current.lastTime;\n    \n    if (elapsed >= 1000) {\n      fpsCounterRef.current.fps = Math.round(fpsCounterRef.current.frames * 1000 / elapsed);\n      fpsCounterRef.current.frames = 0;\n      fpsCounterRef.current.lastTime = now;\n      \n      // 更新性能统计信息\n      if (showPerformanceStats) {\n        const memory = window.performance && window.performance.memory ? {\n          total: Math.round(window.performance.memory.totalJSHeapSize / (1024 * 1024)),\n          used: Math.round(window.performance.memory.usedJSHeapSize / (1024 * 1024)),\n          limit: Math.round(window.performance.memory.jsHeapSizeLimit / (1024 * 1024)),\n          percent: Math.round((window.performance.memory.usedJSHeapSize / window.performance.memory.jsHeapSizeLimit) * 100)\n        } : { total: 0, used: 0, limit: 0, percent: 0 };\n        \n        setPerformanceStats({\n          fps: fpsCounterRef.current.fps,\n          memory,\n          resourceCounts: {\n            mixers: resourceManager ? resourceManager.mixers.size : 0,\n            models: resourceManager ? resourceManager.models.size : 0,\n            people: peopleAnimationMixers.current.size\n          }\n        });\n      }\n    }\n  };\n\n  // 添加一个全局方法用于显示所有红绿灯ID\n  window.listTrafficLights = () => {\n    console.log('红绿灯列表:');\n    \n    if (!trafficLightsMap || trafficLightsMap.size === 0) {\n      console.log('当前没有红绿灯对象');\n      return [];\n    }\n    \n    const list = [];\n    trafficLightsMap.forEach((light, id) => {\n      console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n      list.push({\n        id,\n        name: light.intersection.name,\n        position: light.position\n      });\n    });\n    \n    return list;\n  };\n\n  // 添加全局调试方法\n  // window.debugScene = function() {\n  //   if (!scene) {\n  //     console.error('场景未初始化，无法调试红绿灯');\n  //     return;\n  //   }\n\n  //   console.log('开始调试红绿灯模型...');\n    \n  //   // 检查红绿灯映射\n  //   console.log('红绿灯映射数量:', trafficLightsMap.size);\n    \n  //   // 统计场景中的可互动对象\n  //   let interactiveObjects = 0;\n  //   let trafficLightObjects = 0;\n    \n  //   // 遍历场景中的所有对象\n  //   scene.traverse((object) => {\n  //     // 检查是否有userData\n  //     if (object.userData && Object.keys(object.userData).length > 0) {\n  //       interactiveObjects++;\n        \n  //       // 检查是否是红绿灯\n  //       if (object.userData.type === 'trafficLight') {\n  //         trafficLightObjects++;\n  //         console.log('找到红绿灯对象:', {\n  //           名称: object.name || '无名称',\n  //           类型: object.userData.type,\n  //           路口ID: object.userData.interId,\n  //           位置: object.position.toArray(),\n  //           可见性: object.visible,\n  //           是否是网格: object.isMesh,\n  //           userData: object.userData\n  //         });\n  //       }\n  //     }\n  //   });\n    \n  //   console.log('场景中的可互动对象数量:', interactiveObjects);\n  //   console.log('红绿灯对象数量:', trafficLightObjects);\n    \n  //   // 遍历红绿灯映射，创建高亮标记\n  //   trafficLightsMap.forEach((lightObj, interId) => {\n  //     if (lightObj.model) {\n  //       // 获取红绿灯位置\n  //       const position = lightObj.model.position.clone();\n      \n  //       // 创建一个高亮球体\n  //       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  //       const highlightMaterial = new THREE.MeshBasicMaterial({ \n  //         color: 0xff00ff, \n  //         transparent: true,\n  //         opacity: 0.7\n  //       });\n  //       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n  //       // 设置位置，稍微偏移，避免遮挡\n  //       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n  //       // 添加到场景\n  //       scene.add(highlightMesh);\n      \n  //       // 添加用户数据，方便调试\n  //       highlightMesh.userData = {\n  //         type: 'trafficLightHighlight',\n  //         interId: interId,\n  //         name: lightObj.intersection.name,\n  //         originalPosition: position.toArray()\n  //       };\n      \n  //       // 5秒后自动移除高亮标记\n  //       setTimeout(() => {\n  //         scene.remove(highlightMesh);\n  //       }, 5000);\n      \n  //       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n  //     }\n  //   });\n    \n  //   // 将调试信息显示在控制台\n  //   console.log('红绿灯调试信息:', {\n  //     红绿灯映射数量: trafficLightsMap.size,\n  //     红绿灯状态数量: trafficLightStates.size,\n  //     场景中红绿灯对象数量: trafficLightObjects,\n  //     射线检测启用: true\n  //   });\n  // };\n\n  // 添加全局测试弹窗函数\n  window.showTrafficLightPopup = (interId) => {\n    try {\n      // 确保interId为字符串类型\n      interId = String(interId);\n      \n      console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n      console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n      \n      // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n      let trafficLight = trafficLightsMap.get(interId);\n      if (!trafficLight) {\n        // 尝试转换为数字查找\n        const numericId = parseInt(interId);\n        trafficLight = trafficLightsMap.get(numericId);\n        \n        if (trafficLight) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n          interId = numericId; // 更新interId为找到的正确类型\n        }\n      }\n      \n      if (!trafficLight) {\n        console.error('未找到指定ID的红绿灯:', interId);\n        return false;\n      }\n      \n      const stateInfo = trafficLightStates.get(interId);\n      const intersection = trafficLight.intersection;\n      \n      // 判断是否有相位数据\n      const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n      \n      let content;\n      \n      // 指北针样式\n      const compassStyle = {\n        position: 'absolute',\n        top: '5px',\n        right: '25px',\n        width: '30px',\n        height: '30px',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        borderRadius: '50%',\n        background: 'rgba(0,0,0,0.1)',\n        zIndex: 10\n      };\n      \n      // 指北针组件\n      const CompassIcon = () => (\n        <div style={compassStyle}>\n          <div style={{\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            transform: 'rotate(0deg)'\n          }}>\n            <span style={{\n              color: '#ff5252',\n              fontSize: '14px',\n              fontWeight: 'bold',\n              lineHeight: '14px'\n            }}>N</span>\n            <span style={{\n              width: 0,\n              height: 0,\n              borderLeft: '6px solid transparent',\n              borderRight: '6px solid transparent',\n              borderBottom: '10px solid #ff5252',\n              marginTop: '-2px'\n            }}></span>\n          </div>\n        </div>\n      );\n      \n      if (hasPhaseData) {\n        // 相位ID与方向/方式的映射\n        const phaseMap = {\n          '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },\n          '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },\n          '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },\n          '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }\n        };\n        \n        const typeOrder = ['left', 'straight', 'right'];\n        const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };\n        const dirData = { N: {}, E: {}, S: {}, W: {} };\n        \n        stateInfo.phases.forEach(phase => {\n          const map = phaseMap[phase.phaseId];\n          if (map) {\n            dirData[map.dir][map.type] = {\n              color: colorMap[phase.trafficLight] || '#888',\n              remainTime: phase.remainTime\n            };\n          }\n        });\n        \n        content = (\n          <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n            <CompassIcon />\n            <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>\n            <div style={{\n              display: 'grid',\n              gridTemplateRows: '60px 60px 60px',\n              gridTemplateColumns: '60px 60px 60px',\n              justifyContent: 'center',\n              alignItems: 'center',\n              background: 'rgba(255,255,255,0.05)',\n              borderRadius: '8px',\n              margin: '0 auto',\n              position: 'relative'\n            }}>\n              {/* 北 - 左转、直行、右转箭头水平对齐，倒计时在箭头上面 */}\n              {/* <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%', paddingLeft: '5px' }}> */}\n              <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}> \n                {typeOrder.map((type, index) => {\n                  // 反转左转和右转在数组中的顺序\n                  let displayIndex = index;\n                  if (index === 0) displayIndex = 2;\n                  else if (index === 2) displayIndex = 0;\n                  \n                  const currentType = typeOrder[displayIndex];\n                  \n                  // 计算与南边对齐的样式\n                  const marginStyle = {};\n                  if (currentType === 'left') { // 左转箭头 (右侧显示)\n                    marginStyle.marginRight = '0px';\n                  } else if (currentType === 'straight') { // 直行箭头 (中间显示)\n                    marginStyle.marginLeft = '10px';\n                    marginStyle.marginRight = '10px';\n                  } else if (currentType === 'right') { // 右转箭头 (左侧显示)\n                    marginStyle.marginLeft = '0px';\n                  }\n                  \n                  return dirData.N[currentType] && (\n                    // <div key={currentType} style={{\n                    //   display: 'flex', \n                    //   flexDirection: 'column', \n                    //   alignItems: 'center',\n                    //   ...marginStyle\n                    // }}>\n                    <div key={currentType} style={{marginRight: currentType === 'left' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                      <div style={{fontSize:'14px', color: dirData.N[currentType].color, fontWeight:'bold', marginBottom: '3px'}}>{dirData.N[currentType].remainTime}</div>\n                      <span style={{color: dirData.N[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                        {currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'}\n                      </span>\n                    </div>\n                  );\n                })}\n              </div>\n              \n              {/* 南 - 左转、直行、右转箭头水平对齐，倒计时在箭头下面 */}\n              <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n                {typeOrder.map(type => dirData.S[type] && (\n                  <div key={type} style={{marginRight: type === 'right' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                    <span style={{color: dirData.S[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'}\n                    </span>\n                    <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold', marginTop: '3px'}}>{dirData.S[type].remainTime}</div>\n                  </div>\n                ))} \n              </div>\n              \n              {/* 东 - 倒计时显示在箭头右边 */}\n\n              <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>\n                {typeOrder.map((type, index) => {\n                  // 反转左转和右转在数组中的顺序\n                  let displayIndex = index;\n                  if (index === 0) displayIndex = 2;\n                  else if (index === 2) displayIndex = 0;\n                  \n                  const currentType = typeOrder[displayIndex];\n                  \n                  return dirData.E[currentType] && (\n                    <div key={currentType} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>\n                      <span style={{color: dirData.E[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                        {currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'}\n                      </span>\n                      <div style={{\n                        fontSize:'14px', \n                        color: dirData.E[currentType].color, \n                        fontWeight:'bold', \n                        marginLeft: '5px'\n                      }}>{dirData.E[currentType].remainTime}</div>\n                    </div>\n                  );\n                })}\n              </div>\n              \n              {/* 西 - 倒计时显示在箭头左边 */}\n              <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>\n                {typeOrder.map(type => dirData.W[type] && (\n                  <div key={type} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>\n                    <div style={{\n                      fontSize:'14px', \n                      color: dirData.W[type].color, \n                      fontWeight:'bold', \n                      marginRight: '5px'\n                    }}>{dirData.W[type].remainTime}</div>\n                    <span style={{color: dirData.W[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n            <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n              更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n            </div>\n          </div>\n        );\n      } else {\n        // 没有相位数据时显示的内容\n        content = (\n          <div style={{ padding: '15px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n            <CompassIcon />\n            <div style={{ fontWeight: 'bold', marginBottom: '10px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}</div>\n            <div style={{ \n              textAlign: 'center', \n              padding: '20px 0',\n              color: '#ff9800', \n              fontSize: '14px', \n              fontWeight: 'bold',\n              background: 'rgba(255,255,255,0.1)',\n              borderRadius: '8px',\n              marginBottom: '10px'\n            }}>\n              当前无信号灯状态信息\n            </div>\n            <div style={{ fontSize: '12px', color: '#888', textAlign: 'center' }}>\n              路口ID: {interId}\n            </div>\n            <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n              更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n            </div>\n          </div>\n        );\n      }\n      \n      // 设置弹窗位置在左上角区域\n      const x = 445;\n      const y = 250;\n      \n      // 更新当前显示的红绿灯ID引用\n      if (window.currentPopoverIdRef) {\n        window.currentPopoverIdRef.current = interId;\n      }\n      \n      // 取消之前的更新定时器（如果存在）\n      if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n        clearInterval(window.trafficLightUpdateTimerRef.current);\n        window.trafficLightUpdateTimerRef.current = null;\n      }\n      \n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: { x, y },\n          content: content,\n          phases: stateInfo?.phases || []\n        });\n        \n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(() => {\n            window.showTrafficLightPopup(interId);\n          }, 1000);\n        }\n        \n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    } catch (error) {\n      console.error('显示红绿灯弹窗失败:', error);\n      return false;\n    }\n  };\n\n  // // 添加全局模拟SPAT数据生成函数\n  // window.generateMockSpatData = () => {\n  //   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n  //     console.error('红绿灯映射为空，无法生成模拟数据');\n  //     return false;\n  //   }\n    \n  //   console.log('开始生成模拟SPAT数据...');\n    \n  //   // 可能的相位ID和对应方向\n  //   const phaseIds = [\n  //     '1', '2', '3',  // 北进口\n  //     '5', '6', '7',  // 东进口 \n  //     '9', '10', '11', // 南进口\n  //     '13', '14', '15' // 西进口\n  //   ];\n    \n  //   // 准备SPAT数据结构\n  //   const spatMessage = {\n  //     data: {\n  //       intersections: []\n  //     },\n  //     tm: Date.now(),\n  //     source: 2,\n  //     type: 'SPAT',\n  //     mac: 'mock-device-id'\n  //   };\n    \n  //   // 为每个红绿灯生成模拟数据\n  //   trafficLightsMap.forEach((light, interId) => {\n  //     // 为该路口生成3-6个随机相位\n  //     const phaseCount = Math.floor(Math.random() * 4) + 3;\n  //     const phases = [];\n      \n  //     // 随机选择相位ID\n  //     const selectedPhaseIds = [];\n  //     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n  //       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n  //       const phaseId = phaseIds[randomIndex];\n        \n  //       // 避免重复选择同一个ID\n  //       if (!selectedPhaseIds.includes(phaseId)) {\n  //         selectedPhaseIds.push(phaseId);\n  //       }\n  //     }\n      \n  //     // 为每个选中的相位生成随机状态\n  //     selectedPhaseIds.forEach(phaseId => {\n  //       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n  //       const lightIndex = Math.floor(Math.random() * 3);\n  //       const stateLabels = ['red', 'yellow', 'green'];\n        \n  //       // 随机生成剩余时间 (1-60秒)\n  //       const remainTime = Math.floor(Math.random() * 60) + 1;\n        \n  //       // 添加相位信息 - 符合实际数据结构\n  //       phases.push({\n  //         phaseId: phaseId,\n  //         state: stateLabels[lightIndex],\n  //         remainTime: remainTime\n  //       });\n  //     });\n        \n  //     // 添加交叉口数据到SPAT消息\n  //     spatMessage.data.intersections.push({\n  //       id: interId,\n  //       interId: interId, // 确保包含interId字段\n  //       phases: phases\n  //     });\n        \n  //     // 同时更新本地状态方便测试\n  //     const phaseInfos = phases.map(phase => ({\n  //       phaseId: phase.phaseId,\n  //       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n  //       remainTime: phase.remainTime,\n  //       lastUpdate: Date.now()\n  //     }));\n        \n  //     // 使用与trafficLightsMap相同的ID类型存储状态信息\n  //     trafficLightStates.set(interId, {\n  //       updateTime: Date.now(),\n  //       phases: phaseInfos\n  //     });\n        \n  //     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n  //   });\n    \n  //   // 模拟调用SPAT消息处理函数\n  //   try {\n  //     console.log('处理模拟SPAT消息:', spatMessage);\n  //     const message = JSON.stringify(spatMessage);\n  //     // 间接通过handleMqttMessage处理模拟数据\n  //     handleMqttMessage(MQTT_CONFIG.spat, message);\n  //   } catch (error) {\n  //     console.error('处理模拟SPAT消息失败:', error);\n  //   }\n    \n  //   console.log('模拟SPAT数据生成完成');\n  //   return true;\n  // };\n\n  // // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n  // window.testTrafficLightWithMockData = (interId) => {\n  //   // 先生成模拟数据\n  //   window.generateMockSpatData();\n    \n  //   // 延迟100ms确保数据已生成\n  //   setTimeout(() => {\n  //     // 显示红绿灯弹窗\n  //     window.showTrafficLightPopup(interId);\n  //   }, 100);\n    \n  //   return '模拟数据已生成，正在显示红绿灯弹窗...';\n  // };\n\n  // 帮助函数：从对象或其父对象中找到红绿灯对象\n  const getTrafficLightFromObject = (object) => {\n    let current = object;\n    \n    // 如果对象本身就有红绿灯数据，直接返回\n    if (current && current.userData && current.userData.type === 'trafficLight') {\n      console.log('直接找到红绿灯对象:', current.name || '无名称');\n      return current;\n    }\n    \n    // 向上查找父对象，直到找到红绿灯或到达顶层\n    while (current && current.parent) {\n      current = current.parent;\n      if (current.userData && current.userData.type === 'trafficLight') {\n        console.log('从父对象找到红绿灯:', current.name || '无名称');\n        return current;\n      }\n    }\n    \n    return null;\n  };\n\n  // 添加调试工具：强制进行点击测试\n  window.testClickDetection = (x, y) => {\n    try {\n      console.log('执行强制点击测试 @ 位置:', x, y);\n      \n      // 找到渲染器的DOM元素\n      const canvas = document.querySelector('canvas');\n      if (!canvas) {\n        console.error('找不到THREE.js的canvas元素');\n        return false;\n      }\n      \n      // 确保scene和camera已定义\n      if (!scene || !cameraRef.current) {\n        console.error('scene或camera未定义');\n        return false;\n      }\n      \n      // 如果没有传入坐标，使用屏幕中心点\n      if (x === undefined || y === undefined) {\n        x = window.innerWidth / 2;\n        y = window.innerHeight / 2;\n      }\n      \n      // 计算归一化设备坐标 (-1 到 +1)\n      const rect = canvas.getBoundingClientRect();\n      const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n      const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n      \n      console.log('归一化坐标:', mouseX, mouseY);\n      \n      // 创建一个射线\n      const raycaster = new THREE.Raycaster();\n      raycaster.params.Points.threshold = 5;\n      raycaster.params.Line.threshold = 5;\n      \n      const mouseVector = new THREE.Vector2(mouseX, mouseY);\n      raycaster.setFromCamera(mouseVector, cameraRef.current);\n      \n      // 收集所有红绿灯对象\n      const trafficLightObjects = [];\n      trafficLightsMap.forEach((lightObj, interId) => {\n        if (lightObj.model) {\n          trafficLightObjects.push(lightObj.model);\n          console.log(`添加红绿灯 ${interId} 到检测列表`);\n        }\n      });\n      \n      // 直接对红绿灯对象进行碰撞检测\n      console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n      const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n      \n      if (tlIntersects.length > 0) {\n        console.log('成功点击到红绿灯对象!');\n        tlIntersects.forEach((intersect, i) => {\n          console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                      '距离:', intersect.distance,\n                      'position:', intersect.object.position.toArray(),\n                      'userData:', intersect.object.userData);\n          \n          // 尝试获取红绿灯ID\n          const obj = getTrafficLightFromObject(intersect.object);\n          if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n            console.log('找到红绿灯ID:', obj.userData.interId);\n          }\n        });\n        \n        return true;\n      }\n      \n      // 对整个场景进行碰撞检测\n      console.log('对整个场景进行碰撞检测...');\n      const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n      \n      console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n      sceneIntersects.forEach((intersect, i) => {\n        const obj = intersect.object;\n        console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                    '类型:', obj.type,\n                    '位置:', obj.position.toArray(),\n                    '距离:', intersect.distance,\n                    'userData:', obj.userData);\n      });\n      \n      // 测试红绿灯的可见性\n      console.log('检查红绿灯的可见性...');\n      let visibleCount = 0;\n      \n      trafficLightsMap.forEach((lightObj, interId) => {\n        if (lightObj.model) {\n          // 检查红绿灯模型是否可见\n          let isVisible = lightObj.model.visible;\n          let frustumVisible = true;\n          \n          // 获取世界位置\n          const worldPos = new THREE.Vector3();\n          lightObj.model.getWorldPosition(worldPos);\n          \n          // 计算到摄像机的距离\n          const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n          \n          // 检查是否在视锥体内\n          const screenPos = worldPos.clone().project(cameraRef.current);\n          if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n            frustumVisible = false;\n          }\n          \n          if (isVisible) {\n            visibleCount++;\n          }\n          \n          console.log(`红绿灯 ${interId}:`, {\n            名称: lightObj.intersection?.name || '未知',\n            可见性: isVisible,\n            在视锥体内: frustumVisible,\n            世界位置: worldPos.toArray(),\n            屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n            与摄像机距离: distanceToCamera\n          });\n        }\n      });\n      \n      console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n      \n      // 如果未检测到任何交叉点\n      return sceneIntersects.length > 0;\n    } catch (error) {\n      console.error('点击测试失败:', error);\n      return false;\n    }\n  };\n\n\n\n  // 添加更新红绿灯视觉效果的函数\n  const updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n    if (!trafficLight || !trafficLight.model || !phaseInfo) {\n      return;\n    }\n\n    // 移除旧的灯光模型(如果存在)\n    const lightsToRemove = [];\n    trafficLight.model.traverse(child => {\n      if (child.userData && child.userData.isLight) {\n        lightsToRemove.push(child);\n      }\n    });\n    \n    lightsToRemove.forEach(light => {\n      trafficLight.model.remove(light);\n    });\n\n    // 根据状态获取颜色\n    let lightColor;\n    switch(phaseInfo.trafficLight) {\n      case 'G':\n        lightColor = 0x00FF00; // 绿色\n        break;\n      case 'Y':\n        lightColor = 0xFFFF00; // 黄色\n        break;\n      case 'R':\n      default:\n        lightColor = 0xFF0000; // 红色\n        break;\n    }\n    \n    // 创建一个球体作为灯光\n    const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n    const lightMaterial = new THREE.MeshBasicMaterial({ \n      color: lightColor,\n      emissive: lightColor,\n      emissiveIntensity: 1\n    });\n    const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n    lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n    lightMesh.userData = {\n      isLight: true,\n      type: 'trafficLight',\n      interId: trafficLight.intersection?.interId,\n      phaseId: phaseInfo.phaseId,\n      direction: phaseInfo.direction,\n      remainTime: phaseInfo.remainTime\n    };\n    \n    // 添加光源使灯光更明显\n    const light = new THREE.PointLight(lightColor, 1, 50);\n    light.position.set(0, 12, 0);\n    light.userData = { isLight: true };\n    \n    // 将灯光添加到交通灯模型\n    trafficLight.model.add(lightMesh);\n    trafficLight.model.add(light);\n    \n    console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n  };\n\n  return (\n    <div ref={containerRef} className={className}>\n      {/* 性能监控按钮 */}\n      <button \n        style={perfButtonStyle} \n        onClick={() => setShowPerformanceStats(!showPerformanceStats)}\n      >\n        {showPerformanceStats ? '隐藏性能' : '显示性能'}\n      </button>\n      \n      {/* 性能监控面板 */}\n      {showPerformanceStats && (\n        <div style={perfPanelStyle}>\n          <div style={{ marginBottom: '5px', fontWeight: 'bold' }}>性能监控</div>\n          <div style={{ color: performanceStats.fps < 30 ? '#ff5555' : performanceStats.fps < 50 ? '#ffff55' : '#55ff55' }}>\n            FPS: {performanceStats.fps}\n          </div>\n          <div>\n            内存: {performanceStats.memory.used}/{performanceStats.memory.total} MB \n            ({performanceStats.memory.percent}%)\n          </div>\n          <div>\n            动画混合器: {performanceStats.resourceCounts.mixers}\n          </div>\n          <div>\n            模型数量: {performanceStats.resourceCounts.models}\n          </div>\n          <div>\n            行人数量: {performanceStats.resourceCounts.people}\n          </div>\n        </div>\n      )}\n      \n      {/* 视角切换按钮 */}\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? 'rgba(70, 130, 180, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? '#fff' : '#333'\n          }} \n          onClick={() => setViewMode('global')}\n        >\n          全局视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'first-person' ? 'rgba(70, 130, 180, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'first-person' ? '#fff' : '#333'\n          }} \n          onClick={() => setViewMode('first-person')}\n        >\n          驾驶视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'third-person' ? 'rgba(70, 130, 180, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'third-person' ? '#fff' : '#333'\n          }} \n          onClick={() => setViewMode('third-person')}\n        >\n          跟随视角\n        </button>\n      </div>\n      \n      {/* 路口选择器 */}\n      <span style={labelStyle}>路口:</span>\n      <select \n        style={intersectionSelectStyle}\n        value={selectedIntersection ? selectedIntersection.id : ''}\n        onChange={(e) => {\n          const id = e.target.value;\n          const intersection = window.intersections?.find(i => i.id === id);\n          setSelectedIntersection(intersection || null);\n        }}\n      >\n        <option value=\"\">选择路口</option>\n        {window.intersections?.map(intersection => (\n          <option key={intersection.id} value={intersection.id}>\n            {intersection.name}\n          </option>\n        ))}\n      </select>\n      \n      {/* 红绿灯弹窗 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            zIndex: 1000,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '8px',\n            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n            minWidth: '230px',\n            pointerEvents: 'auto'\n          }}\n        >\n          <div style={{ padding: '10px' }}>\n            <div style={{ \n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              borderBottom: '1px solid rgba(255, 255, 255, 0.2)',\n              marginBottom: '8px',\n              paddingBottom: '5px'\n            }}>\n              <strong style={{ fontSize: '14px' }}>\n                信号灯状态\n              </strong>\n              <button\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: 'white',\n                  fontSize: '16px',\n                  cursor: 'pointer',\n                  opacity: 0.7,\n                  transition: 'opacity 0.2s'\n                }}\n                onClick={() => setTrafficLightPopover({ ...trafficLightPopover, visible: false })}\n              >\n                ×\n              </button>\n            </div>\n            {trafficLightPopover.content}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAO,KAAKC,aAAa,MAAM,2CAA2C;AAG1E,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB,IAAIC,eAAe,GAAE,IAAI,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAE,IAAI;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAEpB;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,MAAMC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAExC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChChB,KAAK,EAAE,6BAA6B;EACtCiB,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAE1D;AACA,MAAMG,aAAa,GAAG,IAAIlB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAImB,gBAAgB,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIoB,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIrB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAIsB,kBAAkB,GAAG,IAAItB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC,MAAMuB,KAAK,GAAG,IAAIvD,KAAK,CAACwD,KAAK,CAAC,CAAC;;AAE/B;AACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC;IAC7D,MAAMgB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCjB,gBAAgB,GAAGa,WAAW,CAACI,KAAK;QACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCG,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC5C;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI,CAACjD,YAAY,EAAE;MACjBA,YAAY,GAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC;MAC7B,OAAOF,MAAM;IACf;IAEA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEpD,YAAY,CAACoD,CAAC,EAAElD,KAAK,CAAC;IAChE,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEtD,YAAY,CAACsD,CAAC,EAAEpD,KAAK,CAAC;IAChE,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAExD,YAAY,CAACwD,CAAC,EAAEtD,KAAK,CAAC;IAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;IACjD,OAAOvD,YAAY,CAACkD,KAAK,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;EAEA,MAAMW,OAAO,GAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,MAAMY,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC;EAC3C,MAAMe,sBAAsB,GAAG,EAAE,CAAC,CAAC;;EAEnC,IAAIF,QAAQ,GAAGE,sBAAsB,EAAE;IACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;;EAEA;EACA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEO,OAAO,CAACP,CAAC,EAAElD,KAAK,CAAC;EAC3D,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEK,OAAO,CAACL,CAAC,EAAEpD,KAAK,CAAC;EAC3D,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEG,OAAO,CAACH,CAAC,EAAEtD,KAAK,CAAC;EAE3D,MAAM+D,WAAW,GAAG,IAAI7F,KAAK,CAAC8F,OAAO,CAACf,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC;EAExD,OAAOe,WAAW;AACpB,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,WAAW,EAAEnB,SAAS,KAAK;EACjD;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAIhD,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGmE,WAAW;MAC1B,OAAOA,WAAW;IACpB;;IAEA;IACA,IAAIC,IAAI,GAAGD,WAAW,GAAGnE,YAAY;IACrC,IAAIoE,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IAExC,MAAMC,gBAAgB,GAAG7B,aAAa,CAAC1C,YAAY,GAAGoE,IAAI,EAAEpE,YAAY,EAAEC,KAAK,CAAC;IAChFD,YAAY,GAAGuE,gBAAgB;IAC7B,OAAOA,gBAAgB;EACzB;;EAEA;EACA,IAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMK,OAAO,GAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,IAAIoB,IAAI,GAAGD,WAAW,GAAGK,OAAO;EAChC,IAAIJ,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;;EAExC;EACA,MAAMG,mBAAmB,GAAGJ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,IAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,GAAGK,mBAAmB,EAAE;IACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMI,gBAAgB,GAAG7B,aAAa,CAAC8B,OAAO,GAAGJ,IAAI,EAAEI,OAAO,EAAEvE,KAAK,CAAC;EACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEuB,gBAAgB,CAAC;EAErD,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC,MAAMC,QAAQ,GAAG,IAAI3E,GAAG,CAAC,CAAC;;AAE1B;AACA,MAAM4E,eAAe,GAAG;EACtBC,MAAM,EAAE,IAAIH,GAAG,CAAC,CAAC;EACjBI,KAAK,EAAE,IAAI9E,GAAG,CAAC,CAAC;EAChB+E,OAAO,EAAE,IAAI/E,GAAG,CAAC,CAAC;EAClBgF,MAAM,EAAE,IAAIN,GAAG,CAAC,CAAC;EAEjBO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACN,MAAM,CAACO,GAAG,CAACF,KAAK,CAAC;IACtB,IAAIC,KAAK,EAAE;MACT,IAAI,CAACH,MAAM,CAACI,GAAG,CAACD,KAAK,CAAC;MACtB;MACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;QACvB,IAAIA,MAAM,CAACC,MAAM,EAAE;UACjB,IAAI,CAACT,KAAK,CAACzB,GAAG,CAACiC,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC;QACrC;MACF,CAAC,CAAC;IACJ;IACA,OAAOJ,KAAK;EACd,CAAC;EAEDO,SAASA,CAACC,MAAM,EAAER,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACH,OAAO,CAAC1B,GAAG,CAAC6B,KAAK,EAAE,IAAIR,GAAG,CAAC,CAAC,CAAC;IACpC;IACA,IAAI,CAACK,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACE,GAAG,CAACM,MAAM,CAAC;IACnC,OAAOA,MAAM;EACf,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAACZ,OAAO,CAACa,OAAO,CAAC,CAACb,OAAO,EAAEG,KAAK,KAAK;MACvCH,OAAO,CAACa,OAAO,CAACF,MAAM,IAAI;QACxBA,MAAM,CAACG,IAAI,CAAC,CAAC;QACbH,MAAM,CAACI,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC;MACFf,OAAO,CAACgB,KAAK,CAAC,CAAC;IACjB,CAAC,CAAC;IACF,IAAI,CAAChB,OAAO,CAACgB,KAAK,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAClB,MAAM,CAACe,OAAO,CAACV,KAAK,IAAI;MAC3BA,KAAK,CAACc,aAAa,CAAC,CAAC;MACrB,MAAMC,IAAI,GAAGf,KAAK,CAACgB,OAAO,CAAC,CAAC;MAC5B,IAAID,IAAI,EAAE;QACRA,IAAI,CAACZ,QAAQ,CAACC,MAAM,IAAI;UACtB,IAAIA,MAAM,CAACa,UAAU,EAAE;YACrBb,MAAM,CAACa,UAAU,CAACC,MAAM,GAAG,CAAC;UAC9B;QACF,CAAC,CAAC;QACFlB,KAAK,CAACmB,WAAW,CAACJ,IAAI,CAAC;QACvBf,KAAK,CAACoB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;QAC/Bf,KAAK,CAACqB,WAAW,CAAC,IAAI,CAAC;MACzB;IACF,CAAC,CAAC;IACF,IAAI,CAAC1B,MAAM,CAACkB,KAAK,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACjB,KAAK,CAACc,OAAO,CAACY,IAAI,IAAI;MACzB,IAAIA,IAAI,CAACC,MAAM,EAAE;QACfD,IAAI,CAACC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAC;MAC1B;MACA,IAAIA,IAAI,CAACG,MAAM,EAAEH,IAAI,CAACG,MAAM,CAACC,QAAQ,CAAC,CAAC;MACvC,IAAIJ,IAAI,CAACK,WAAW,EAAEL,IAAI,CAACK,WAAW,CAACD,QAAQ,CAAC,CAAC;IACnD,CAAC,CAAC;IACF,IAAI,CAAC9B,KAAK,CAACiB,KAAK,CAAC,CAAC;;IAElB;IACA,IAAI,CAACf,MAAM,CAACY,OAAO,CAACT,KAAK,IAAI;MAC3B,IAAIA,KAAK,CAACsB,MAAM,EAAE;QAChBtB,KAAK,CAACsB,MAAM,CAACC,MAAM,CAACvB,KAAK,CAAC;MAC5B;MACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;QACvB,IAAIA,MAAM,CAACwB,MAAM,EAAE;UACjB,IAAIxB,MAAM,CAACyB,QAAQ,EAAE;YACnBzB,MAAM,CAACyB,QAAQ,CAACC,OAAO,CAAC,CAAC;UAC3B;UACA,IAAI1B,MAAM,CAAC2B,QAAQ,EAAE;YACnB,IAAIlF,KAAK,CAACC,OAAO,CAACsD,MAAM,CAAC2B,QAAQ,CAAC,EAAE;cAClC3B,MAAM,CAAC2B,QAAQ,CAACrB,OAAO,CAACqB,QAAQ,IAAI;gBAClC,IAAIA,QAAQ,CAACC,GAAG,EAAED,QAAQ,CAACC,GAAG,CAACF,OAAO,CAAC,CAAC;gBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;cACpB,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,IAAI1B,MAAM,CAAC2B,QAAQ,CAACC,GAAG,EAAE5B,MAAM,CAAC2B,QAAQ,CAACC,GAAG,CAACF,OAAO,CAAC,CAAC;cACtD1B,MAAM,CAAC2B,QAAQ,CAACD,OAAO,CAAC,CAAC;YAC3B;UACF;QACF;QACA,IAAI1B,MAAM,CAACa,UAAU,EAAE;UACrBb,MAAM,CAACa,UAAU,CAACC,MAAM,GAAG,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACpB,MAAM,CAACe,KAAK,CAAC,CAAC;EACrB;AACF,CAAC;;AAED;AACA,MAAMoB,oBAAoB,GAAIhC,KAAK,IAAK;EACtC,MAAMD,KAAK,GAAG,IAAIlH,KAAK,CAACoJ,cAAc,CAACjC,KAAK,CAAC;EAC7C,OAAOP,eAAe,CAACK,QAAQ,CAACC,KAAK,EAAEC,KAAK,CAAC;AAC/C,CAAC;;AAED;AACA,MAAMkC,YAAY,GAAGA,CAACC,IAAI,EAAEpC,KAAK,EAAEC,KAAK,KAAK;EAC3C,MAAMO,MAAM,GAAGR,KAAK,CAACqC,UAAU,CAACD,IAAI,EAAEnC,KAAK,CAAC;EAC5C,OAAOP,eAAe,CAACa,SAAS,CAACC,MAAM,EAAER,KAAK,CAAC;AACjD,CAAC;AAED,MAAMsC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA;EACvE,MAAMC,YAAY,GAAGjK,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkK,UAAU,GAAGlK,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmK,SAAS,GAAGnK,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAM8J,aAAa,GAAGpK,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMqK,eAAe,GAAGrK,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMsK,aAAa,GAAGtK,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMuK,iBAAiB,GAAGvK,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMwK,MAAM,GAAGxK,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMyK,kBAAkB,GAAGzK,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM0K,gBAAgB,GAAG1K,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM2K,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMC,oBAAoB,GAAG5K,MAAM,CAAC6K,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAMC,qBAAqB,GAAG/K,MAAM,CAAC,IAAImC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,MAAM6I,gBAAgB,GAAGhL,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;;EAKrC;EACA,MAAM,CAACiL,YAAY,EAAEC,eAAe,CAAC,GAAGjL,QAAQ,CAAC;IAC/CkL,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvL,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMwL,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG1M,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC2M,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3M,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC4M,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7M,QAAQ,CAAC;IAC7D8M,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAEvG,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxB4H,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGnN,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMoN,0BAA0B,GAAGpN,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACAuC,MAAM,CAAC8K,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACAvK,MAAM,CAAC4K,mBAAmB,GAAGA,mBAAmB;EAChD5K,MAAM,CAAC6K,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IAAG;IACd3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACtI,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,IAAIkC,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC2L,UAAU,EAAEC,aAAa,CAAC,GAAG9N,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACqD,gBAAgB,EAAE0K,mBAAmB,CAAC,GAAG/N,QAAQ,CAAC,IAAIkC,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC8L,WAAW,EAAEC,cAAc,CAAC,GAAGjO,QAAQ,CAAC;IAAEkO,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMmB,eAAe,GAAG;IACtB1C,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACXc,KAAK,EAAE,MAAM;IACbnC,OAAO,EAAE,UAAU;IACnBoC,UAAU,EAAE,iBAAiB;IAC7BX,KAAK,EAAE,OAAO;IACdvB,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBR,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAMyC,cAAc,GAAG;IACrB7C,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACXc,KAAK,EAAE,MAAM;IACbnC,OAAO,EAAE,MAAM;IACfoC,UAAU,EAAE,iBAAiB;IAC7BX,KAAK,EAAE,OAAO;IACdtB,YAAY,EAAE,KAAK;IACnBP,MAAM,EAAE,IAAI;IACZ0C,UAAU,EAAE,WAAW;IACvBjC,QAAQ,EAAE,MAAM;IAChBkC,QAAQ,EAAE;EACZ,CAAC;;EAED;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1O,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC2O,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5O,QAAQ,CAAC;IACvD6O,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE;MACNC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE;IACX,CAAC;IACDC,cAAc,EAAE;MACdpI,MAAM,EAAE,CAAC;MACTG,MAAM,EAAE,CAAC;MACTkI,MAAM,EAAE;IACV;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAGtP,MAAM,CAAC;IAC3BuP,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAEC,WAAW,CAAC3E,GAAG,CAAC,CAAC;IAC3BgE,GAAG,EAAE;EACP,CAAC,CAAC;;EAEF;EACA,MAAMY,SAAS,GAAGA,CAAA,KAAM;IACtBJ,aAAa,CAACK,OAAO,CAACJ,MAAM,EAAE;IAE9B,MAAMzE,GAAG,GAAG2E,WAAW,CAAC3E,GAAG,CAAC,CAAC;IAC7B,MAAM8E,OAAO,GAAG9E,GAAG,GAAGwE,aAAa,CAACK,OAAO,CAACH,QAAQ;IAEpD,IAAII,OAAO,IAAI,IAAI,EAAE;MACnBN,aAAa,CAACK,OAAO,CAACb,GAAG,GAAGzI,IAAI,CAACwJ,KAAK,CAACP,aAAa,CAACK,OAAO,CAACJ,MAAM,GAAG,IAAI,GAAGK,OAAO,CAAC;MACrFN,aAAa,CAACK,OAAO,CAACJ,MAAM,GAAG,CAAC;MAChCD,aAAa,CAACK,OAAO,CAACH,QAAQ,GAAG1E,GAAG;;MAEpC;MACA,IAAI4D,oBAAoB,EAAE;QACxB,MAAMK,MAAM,GAAGxM,MAAM,CAACkN,WAAW,IAAIlN,MAAM,CAACkN,WAAW,CAACV,MAAM,GAAG;UAC/DC,KAAK,EAAE3I,IAAI,CAACwJ,KAAK,CAACtN,MAAM,CAACkN,WAAW,CAACV,MAAM,CAACe,eAAe,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;UAC5Eb,IAAI,EAAE5I,IAAI,CAACwJ,KAAK,CAACtN,MAAM,CAACkN,WAAW,CAACV,MAAM,CAACgB,cAAc,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;UAC1Eb,KAAK,EAAE7I,IAAI,CAACwJ,KAAK,CAACtN,MAAM,CAACkN,WAAW,CAACV,MAAM,CAACiB,eAAe,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;UAC5Eb,OAAO,EAAE9I,IAAI,CAACwJ,KAAK,CAAEtN,MAAM,CAACkN,WAAW,CAACV,MAAM,CAACgB,cAAc,GAAGxN,MAAM,CAACkN,WAAW,CAACV,MAAM,CAACiB,eAAe,GAAI,GAAG;QAClH,CAAC,GAAG;UAAEhB,KAAK,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAC;QAE/CN,mBAAmB,CAAC;UAClBC,GAAG,EAAEQ,aAAa,CAACK,OAAO,CAACb,GAAG;UAC9BC,MAAM;UACNK,cAAc,EAAE;YACdpI,MAAM,EAAED,eAAe,GAAGA,eAAe,CAACC,MAAM,CAACiJ,IAAI,GAAG,CAAC;YACzD9I,MAAM,EAAEJ,eAAe,GAAGA,eAAe,CAACI,MAAM,CAAC8I,IAAI,GAAG,CAAC;YACzDZ,MAAM,EAAEtE,qBAAqB,CAAC4E,OAAO,CAACM;UACxC;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA1N,MAAM,CAAC2N,iBAAiB,GAAG,MAAM;IAC/B/M,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IAErB,IAAI,CAACI,gBAAgB,IAAIA,gBAAgB,CAACyM,IAAI,KAAK,CAAC,EAAE;MACpD9M,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB,OAAO,EAAE;IACX;IAEA,MAAM+M,IAAI,GAAG,EAAE;IACf3M,gBAAgB,CAACuE,OAAO,CAAC,CAACqI,KAAK,EAAEC,EAAE,KAAK;MACtClN,OAAO,CAACC,GAAG,CAAC,SAASiN,EAAE,SAASD,KAAK,CAACE,YAAY,CAACC,IAAI,EAAE,CAAC;MAC1DJ,IAAI,CAACK,IAAI,CAAC;QACRH,EAAE;QACFE,IAAI,EAAEH,KAAK,CAACE,YAAY,CAACC,IAAI;QAC7B7E,QAAQ,EAAE0E,KAAK,CAAC1E;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOyE,IAAI;EACb,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA5N,MAAM,CAACkO,qBAAqB,GAAIzD,OAAO,IAAK;IAC1C,IAAI;MACF;MACAA,OAAO,GAAG0D,MAAM,CAAC1D,OAAO,CAAC;MAEzB7J,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE4J,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;MAC/E7J,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,gBAAgB,CAACyM,IAAI,CAAC;;MAE3D;MACA,IAAIU,YAAY,GAAGnN,gBAAgB,CAACmC,GAAG,CAACqH,OAAO,CAAC;MAChD,IAAI,CAAC2D,YAAY,EAAE;QACjB;QACA,MAAMC,SAAS,GAAGC,QAAQ,CAAC7D,OAAO,CAAC;QACnC2D,YAAY,GAAGnN,gBAAgB,CAACmC,GAAG,CAACiL,SAAS,CAAC;QAE9C,IAAID,YAAY,EAAE;UAChBxN,OAAO,CAACC,GAAG,CAAC,UAAUwN,SAAS,SAAS,CAAC;UACzC5D,OAAO,GAAG4D,SAAS,CAAC,CAAC;QACvB;MACF;MAEA,IAAI,CAACD,YAAY,EAAE;QACjBxN,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEuI,OAAO,CAAC;QACtC,OAAO,KAAK;MACd;MAEA,MAAM8D,SAAS,GAAGrN,kBAAkB,CAACkC,GAAG,CAACqH,OAAO,CAAC;MACjD,MAAMsD,YAAY,GAAGK,YAAY,CAACL,YAAY;;MAE9C;MACA,MAAMS,YAAY,GAAGD,SAAS,IAAIA,SAAS,CAAC5D,MAAM,IAAI4D,SAAS,CAAC5D,MAAM,CAAC3E,MAAM,GAAG,CAAC;MAEjF,IAAI0E,OAAO;;MAEX;MACA,MAAM+D,YAAY,GAAG;QACnBtF,QAAQ,EAAE,UAAU;QACpB6B,GAAG,EAAE,KAAK;QACVc,KAAK,EAAE,MAAM;QACbb,KAAK,EAAE,MAAM;QACbyD,MAAM,EAAE,MAAM;QACdlF,OAAO,EAAE,MAAM;QACfmF,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpB9E,YAAY,EAAE,KAAK;QACnBiC,UAAU,EAAE,iBAAiB;QAC7BxC,MAAM,EAAE;MACV,CAAC;;MAED;MACA,MAAMsF,WAAW,GAAGA,CAAA,kBAClBtQ,OAAA;QAAKuQ,KAAK,EAAEL,YAAa;QAAAM,QAAA,eACvBxQ,OAAA;UAAKuQ,KAAK,EAAE;YACVtF,OAAO,EAAE,MAAM;YACfwF,aAAa,EAAE,QAAQ;YACvBJ,UAAU,EAAE,QAAQ;YACpBtF,SAAS,EAAE;UACb,CAAE;UAAAyF,QAAA,gBACAxQ,OAAA;YAAMuQ,KAAK,EAAE;cACX1D,KAAK,EAAE,SAAS;cAChBpB,QAAQ,EAAE,MAAM;cAChBqB,UAAU,EAAE,MAAM;cAClBF,UAAU,EAAE;YACd,CAAE;YAAA4D,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACX7Q,OAAA;YAAMuQ,KAAK,EAAE;cACX7D,KAAK,EAAE,CAAC;cACRyD,MAAM,EAAE,CAAC;cACTW,UAAU,EAAE,uBAAuB;cACnCC,WAAW,EAAE,uBAAuB;cACpCC,YAAY,EAAE,oBAAoB;cAClCC,SAAS,EAAE;YACb;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;MAED,IAAIZ,YAAY,EAAE;QAChB;QACA,MAAMiB,QAAQ,GAAG;UACf,GAAG,EAAE;YAAEC,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAO,CAAC;UAAE,GAAG,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAW,CAAC;UAAE,GAAG,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAQ,CAAC;UACtG,GAAG,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAO,CAAC;UAAE,GAAG,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAW,CAAC;UAAE,GAAG,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAQ,CAAC;UACtG,GAAG,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAO,CAAC;UAAE,IAAI,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAW,CAAC;UAAE,IAAI,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAQ,CAAC;UACxG,IAAI,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAO,CAAC;UAAE,IAAI,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAW,CAAC;UAAE,IAAI,EAAE;YAAED,GAAG,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAQ;QAC1G,CAAC;QAED,MAAMC,SAAS,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;QAC/C,MAAMC,QAAQ,GAAG;UAAEC,CAAC,EAAE,SAAS;UAAEC,CAAC,EAAE,SAAS;UAAEC,CAAC,EAAE;QAAU,CAAC;QAC7D,MAAMC,OAAO,GAAG;UAAEC,CAAC,EAAE,CAAC,CAAC;UAAEC,CAAC,EAAE,CAAC,CAAC;UAAEC,CAAC,EAAE,CAAC,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAE,CAAC;QAE9C9B,SAAS,CAAC5D,MAAM,CAACnF,OAAO,CAAC8K,KAAK,IAAI;UAChC,MAAMxJ,GAAG,GAAG2I,QAAQ,CAACa,KAAK,CAACC,OAAO,CAAC;UACnC,IAAIzJ,GAAG,EAAE;YACPmJ,OAAO,CAACnJ,GAAG,CAAC4I,GAAG,CAAC,CAAC5I,GAAG,CAAC6I,IAAI,CAAC,GAAG;cAC3BvE,KAAK,EAAEyE,QAAQ,CAACS,KAAK,CAAClC,YAAY,CAAC,IAAI,MAAM;cAC7CoC,UAAU,EAAEF,KAAK,CAACE;YACpB,CAAC;UACH;QACF,CAAC,CAAC;QAEF9F,OAAO,gBACLnM,OAAA;UAAKuQ,KAAK,EAAE;YAAEnF,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE,OAAO;YAAEc,UAAU,EAAE,kBAAkB;YAAE5C,QAAQ,EAAE;UAAW,CAAE;UAAA4F,QAAA,gBACnGxQ,OAAA,CAACsQ,WAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACf7Q,OAAA;YAAKuQ,KAAK,EAAE;cAAEzD,UAAU,EAAE,MAAM;cAAEoF,YAAY,EAAE,KAAK;cAAEzG,QAAQ,EAAE,MAAM;cAAE0G,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,GAAEhB,YAAY,CAACC,IAAI,EAAC,cAAE;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3H7Q,OAAA;YAAKuQ,KAAK,EAAE;cACVtF,OAAO,EAAE,MAAM;cACfmH,gBAAgB,EAAE,gBAAgB;cAClCC,mBAAmB,EAAE,gBAAgB;cACrCjC,cAAc,EAAE,QAAQ;cACxBC,UAAU,EAAE,QAAQ;cACpB7C,UAAU,EAAE,wBAAwB;cACpCjC,YAAY,EAAE,KAAK;cACnB+G,MAAM,EAAE,QAAQ;cAChB1H,QAAQ,EAAE;YACZ,CAAE;YAAA4F,QAAA,gBAGAxQ,OAAA;cAAKuQ,KAAK,EAAE;gBAAEgC,OAAO,EAAE,CAAC;gBAAEC,UAAU,EAAE,CAAC;gBAAEL,SAAS,EAAE,QAAQ;gBAAElH,OAAO,EAAE,MAAM;gBAAEmF,cAAc,EAAE,QAAQ;gBAAE1D,KAAK,EAAE;cAAO,CAAE;cAAA8D,QAAA,EACtHa,SAAS,CAAC9I,GAAG,CAAC,CAAC6I,IAAI,EAAEqB,KAAK,KAAK;gBAC9B;gBACA,IAAIC,YAAY,GAAGD,KAAK;gBACxB,IAAIA,KAAK,KAAK,CAAC,EAAEC,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAID,KAAK,KAAK,CAAC,EAAEC,YAAY,GAAG,CAAC;gBAEtC,MAAMC,WAAW,GAAGtB,SAAS,CAACqB,YAAY,CAAC;;gBAE3C;gBACA,MAAME,WAAW,GAAG,CAAC,CAAC;gBACtB,IAAID,WAAW,KAAK,MAAM,EAAE;kBAAE;kBAC5BC,WAAW,CAACC,WAAW,GAAG,KAAK;gBACjC,CAAC,MAAM,IAAIF,WAAW,KAAK,UAAU,EAAE;kBAAE;kBACvCC,WAAW,CAACE,UAAU,GAAG,MAAM;kBAC/BF,WAAW,CAACC,WAAW,GAAG,MAAM;gBAClC,CAAC,MAAM,IAAIF,WAAW,KAAK,OAAO,EAAE;kBAAE;kBACpCC,WAAW,CAACE,UAAU,GAAG,KAAK;gBAChC;gBAEA,OAAOpB,OAAO,CAACC,CAAC,CAACgB,WAAW,CAAC;gBAAA;gBAC3B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA3S,OAAA;kBAAuBuQ,KAAK,EAAE;oBAACsC,WAAW,EAAEF,WAAW,KAAK,MAAM,GAAG,CAAC,GAAG,MAAM;oBAAE1H,OAAO,EAAE,MAAM;oBAAEwF,aAAa,EAAE,QAAQ;oBAAEJ,UAAU,EAAE;kBAAQ,CAAE;kBAAAG,QAAA,gBAC/IxQ,OAAA;oBAAKuQ,KAAK,EAAE;sBAAC9E,QAAQ,EAAC,MAAM;sBAAEoB,KAAK,EAAE6E,OAAO,CAACC,CAAC,CAACgB,WAAW,CAAC,CAAC9F,KAAK;sBAAEC,UAAU,EAAC,MAAM;sBAAEoF,YAAY,EAAE;oBAAK,CAAE;oBAAA1B,QAAA,EAAEkB,OAAO,CAACC,CAAC,CAACgB,WAAW,CAAC,CAACV;kBAAU;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrJ7Q,OAAA;oBAAMuQ,KAAK,EAAE;sBAAC1D,KAAK,EAAE6E,OAAO,CAACC,CAAC,CAACgB,WAAW,CAAC,CAAC9F,KAAK;sBAAEpB,QAAQ,EAAC,MAAM;sBAAEmB,UAAU,EAAE;oBAAM,CAAE;oBAAA4D,QAAA,EACrFmC,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;kBAAW;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC;gBAAA,GAJC8B,WAAW;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKhB,CACN;cACH,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN7Q,OAAA;cAAKuQ,KAAK,EAAE;gBAAEgC,OAAO,EAAE,CAAC;gBAAEC,UAAU,EAAE,CAAC;gBAAEL,SAAS,EAAE,QAAQ;gBAAElH,OAAO,EAAE,MAAM;gBAAEmF,cAAc,EAAE,QAAQ;gBAAE1D,KAAK,EAAE;cAAO,CAAE;cAAA8D,QAAA,EACtHa,SAAS,CAAC9I,GAAG,CAAC6I,IAAI,IAAIM,OAAO,CAACG,CAAC,CAACT,IAAI,CAAC,iBACpCpR,OAAA;gBAAgBuQ,KAAK,EAAE;kBAACsC,WAAW,EAAEzB,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,MAAM;kBAAEnG,OAAO,EAAE,MAAM;kBAAEwF,aAAa,EAAE,QAAQ;kBAAEJ,UAAU,EAAE;gBAAQ,CAAE;gBAAAG,QAAA,gBAClIxQ,OAAA;kBAAMuQ,KAAK,EAAE;oBAAC1D,KAAK,EAAE6E,OAAO,CAACG,CAAC,CAACT,IAAI,CAAC,CAACvE,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAA4D,QAAA,EAC9EY,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACP7Q,OAAA;kBAAKuQ,KAAK,EAAE;oBAAC9E,QAAQ,EAAC,MAAM;oBAAEoB,KAAK,EAAE6E,OAAO,CAACG,CAAC,CAACT,IAAI,CAAC,CAACvE,KAAK;oBAAEC,UAAU,EAAC,MAAM;oBAAEmE,SAAS,EAAE;kBAAK,CAAE;kBAAAT,QAAA,EAAEkB,OAAO,CAACG,CAAC,CAACT,IAAI,CAAC,CAACa;gBAAU;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAJ5HO,IAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKT,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAIN7Q,OAAA;cAAKuQ,KAAK,EAAE;gBAAEgC,OAAO,EAAE,CAAC;gBAAEC,UAAU,EAAE,CAAC;gBAAEL,SAAS,EAAE;cAAS,CAAE;cAAA3B,QAAA,EAC5Da,SAAS,CAAC9I,GAAG,CAAC,CAAC6I,IAAI,EAAEqB,KAAK,KAAK;gBAC9B;gBACA,IAAIC,YAAY,GAAGD,KAAK;gBACxB,IAAIA,KAAK,KAAK,CAAC,EAAEC,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAID,KAAK,KAAK,CAAC,EAAEC,YAAY,GAAG,CAAC;gBAEtC,MAAMC,WAAW,GAAGtB,SAAS,CAACqB,YAAY,CAAC;gBAE3C,OAAOhB,OAAO,CAACE,CAAC,CAACe,WAAW,CAAC,iBAC3B3S,OAAA;kBAAuBuQ,KAAK,EAAE;oBAAC2B,YAAY,EAAC,KAAK;oBAAEjH,OAAO,EAAE,MAAM;oBAAEoF,UAAU,EAAE,QAAQ;oBAAED,cAAc,EAAE;kBAAY,CAAE;kBAAAI,QAAA,gBACtHxQ,OAAA;oBAAMuQ,KAAK,EAAE;sBAAC1D,KAAK,EAAE6E,OAAO,CAACE,CAAC,CAACe,WAAW,CAAC,CAAC9F,KAAK;sBAAEpB,QAAQ,EAAC,MAAM;sBAAEmB,UAAU,EAAE;oBAAM,CAAE;oBAAA4D,QAAA,EACrFmC,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;kBAAW;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC,eACP7Q,OAAA;oBAAKuQ,KAAK,EAAE;sBACV9E,QAAQ,EAAC,MAAM;sBACfoB,KAAK,EAAE6E,OAAO,CAACE,CAAC,CAACe,WAAW,CAAC,CAAC9F,KAAK;sBACnCC,UAAU,EAAC,MAAM;sBACjBgG,UAAU,EAAE;oBACd,CAAE;oBAAAtC,QAAA,EAAEkB,OAAO,CAACE,CAAC,CAACe,WAAW,CAAC,CAACV;kBAAU;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GATpC8B,WAAW;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUhB,CACN;cACH,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN7Q,OAAA;cAAKuQ,KAAK,EAAE;gBAAEgC,OAAO,EAAE,CAAC;gBAAEC,UAAU,EAAE,CAAC;gBAAEL,SAAS,EAAE;cAAS,CAAE;cAAA3B,QAAA,EAC5Da,SAAS,CAAC9I,GAAG,CAAC6I,IAAI,IAAIM,OAAO,CAACI,CAAC,CAACV,IAAI,CAAC,iBACpCpR,OAAA;gBAAgBuQ,KAAK,EAAE;kBAAC2B,YAAY,EAAC,KAAK;kBAAEjH,OAAO,EAAE,MAAM;kBAAEoF,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAU,CAAE;gBAAAI,QAAA,gBAC7GxQ,OAAA;kBAAKuQ,KAAK,EAAE;oBACV9E,QAAQ,EAAC,MAAM;oBACfoB,KAAK,EAAE6E,OAAO,CAACI,CAAC,CAACV,IAAI,CAAC,CAACvE,KAAK;oBAC5BC,UAAU,EAAC,MAAM;oBACjB+F,WAAW,EAAE;kBACf,CAAE;kBAAArC,QAAA,EAAEkB,OAAO,CAACI,CAAC,CAACV,IAAI,CAAC,CAACa;gBAAU;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrC7Q,OAAA;kBAAMuQ,KAAK,EAAE;oBAAC1D,KAAK,EAAE6E,OAAO,CAACI,CAAC,CAACV,IAAI,CAAC,CAACvE,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAA4D,QAAA,EAC9EY,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA,GATCO,IAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUT,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7Q,OAAA;YAAKuQ,KAAK,EAAE;cAAEU,SAAS,EAAE,KAAK;cAAExF,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE,MAAM;cAAEsF,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,GAAC,4BAChF,EAAC,IAAIzG,IAAI,CAAC,CAAC,CAACgJ,kBAAkB,CAAC,CAAC,EAAC,6BACzC;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH,CAAC,MAAM;QACL;QACA1E,OAAO,gBACLnM,OAAA;UAAKuQ,KAAK,EAAE;YAAEnF,OAAO,EAAE,MAAM;YAAEsB,KAAK,EAAE,OAAO;YAAEc,UAAU,EAAE,kBAAkB;YAAE5C,QAAQ,EAAE;UAAW,CAAE;UAAA4F,QAAA,gBACpGxQ,OAAA,CAACsQ,WAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACf7Q,OAAA;YAAKuQ,KAAK,EAAE;cAAEzD,UAAU,EAAE,MAAM;cAAEoF,YAAY,EAAE,MAAM;cAAEzG,QAAQ,EAAE,MAAM;cAAE0G,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,EAAEhB,YAAY,CAACC;UAAI;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1H7Q,OAAA;YAAKuQ,KAAK,EAAE;cACV4B,SAAS,EAAE,QAAQ;cACnB/G,OAAO,EAAE,QAAQ;cACjByB,KAAK,EAAE,SAAS;cAChBpB,QAAQ,EAAE,MAAM;cAChBqB,UAAU,EAAE,MAAM;cAClBU,UAAU,EAAE,uBAAuB;cACnCjC,YAAY,EAAE,KAAK;cACnB2G,YAAY,EAAE;YAChB,CAAE;YAAA1B,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN7Q,OAAA;YAAKuQ,KAAK,EAAE;cAAE9E,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE,MAAM;cAAEsF,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,GAAC,kBAC9D,EAACtE,OAAO;UAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACN7Q,OAAA;YAAKuQ,KAAK,EAAE;cAAEU,SAAS,EAAE,KAAK;cAAExF,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE,MAAM;cAAEsF,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,GAAC,4BAChF,EAAC,IAAIzG,IAAI,CAAC,CAAC,CAACgJ,kBAAkB,CAAC,CAAC,EAAC,6BACzC;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH;;MAEA;MACA,MAAMxM,CAAC,GAAG,GAAG;MACb,MAAME,CAAC,GAAG,GAAG;;MAEb;MACA,IAAI9C,MAAM,CAAC4K,mBAAmB,EAAE;QAC9B5K,MAAM,CAAC4K,mBAAmB,CAACwC,OAAO,GAAG3C,OAAO;MAC9C;;MAEA;MACA,IAAIzK,MAAM,CAAC6K,0BAA0B,IAAI7K,MAAM,CAAC6K,0BAA0B,CAACuC,OAAO,EAAE;QAClFmE,aAAa,CAACvR,MAAM,CAAC6K,0BAA0B,CAACuC,OAAO,CAAC;QACxDpN,MAAM,CAAC6K,0BAA0B,CAACuC,OAAO,GAAG,IAAI;MAClD;;MAEA;MACA,IAAIpN,MAAM,CAAC8K,uBAAuB,EAAE;QAClC9K,MAAM,CAAC8K,uBAAuB,CAAC;UAC7BN,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEA,OAAO;UAChBtB,QAAQ,EAAE;YAAEvG,CAAC;YAAEE;UAAE,CAAC;UAClB4H,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAE,CAAA4D,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE5D,MAAM,KAAI;QAC/B,CAAC,CAAC;;QAEF;QACA,IAAI3K,MAAM,CAAC6K,0BAA0B,EAAE;UACrC7K,MAAM,CAAC6K,0BAA0B,CAACuC,OAAO,GAAGoE,WAAW,CAAC,MAAM;YAC5DxR,MAAM,CAACkO,qBAAqB,CAACzD,OAAO,CAAC;UACvC,CAAC,EAAE,IAAI,CAAC;QACV;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACL7J,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC;QAC7C,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA,MAAMuP,yBAAyB,GAAIvM,MAAM,IAAK;IAC5C,IAAIkI,OAAO,GAAGlI,MAAM;;IAEpB;IACA,IAAIkI,OAAO,IAAIA,OAAO,CAACsE,QAAQ,IAAItE,OAAO,CAACsE,QAAQ,CAAC/B,IAAI,KAAK,cAAc,EAAE;MAC3E/O,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEuM,OAAO,CAACY,IAAI,IAAI,KAAK,CAAC;MAChD,OAAOZ,OAAO;IAChB;;IAEA;IACA,OAAOA,OAAO,IAAIA,OAAO,CAAC/G,MAAM,EAAE;MAChC+G,OAAO,GAAGA,OAAO,CAAC/G,MAAM;MACxB,IAAI+G,OAAO,CAACsE,QAAQ,IAAItE,OAAO,CAACsE,QAAQ,CAAC/B,IAAI,KAAK,cAAc,EAAE;QAChE/O,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEuM,OAAO,CAACY,IAAI,IAAI,KAAK,CAAC;QAChD,OAAOZ,OAAO;MAChB;IACF;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACApN,MAAM,CAAC2R,kBAAkB,GAAG,CAAC/O,CAAC,EAAEE,CAAC,KAAK;IACpC,IAAI;MACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+B,CAAC,EAAEE,CAAC,CAAC;;MAEnC;MACA,MAAM8O,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,IAAI,CAACF,MAAM,EAAE;QACXhR,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC;QACrC,OAAO,KAAK;MACd;;MAEA;MACA,IAAI,CAAC7C,KAAK,IAAI,CAAC8K,SAAS,CAACiD,OAAO,EAAE;QAChCxM,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC;QAChC,OAAO,KAAK;MACd;;MAEA;MACA,IAAIU,CAAC,KAAKmP,SAAS,IAAIjP,CAAC,KAAKiP,SAAS,EAAE;QACtCnP,CAAC,GAAG5C,MAAM,CAACgS,UAAU,GAAG,CAAC;QACzBlP,CAAC,GAAG9C,MAAM,CAACiS,WAAW,GAAG,CAAC;MAC5B;;MAEA;MACA,MAAMC,IAAI,GAAGN,MAAM,CAACO,qBAAqB,CAAC,CAAC;MAC3C,MAAMC,MAAM,GAAI,CAACxP,CAAC,GAAGsP,IAAI,CAAC7I,IAAI,IAAIuI,MAAM,CAACS,WAAW,GAAI,CAAC,GAAG,CAAC;MAC7D,MAAMC,MAAM,GAAG,EAAE,CAACxP,CAAC,GAAGoP,IAAI,CAAClH,GAAG,IAAI4G,MAAM,CAACW,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;MAE9D3R,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuR,MAAM,EAAEE,MAAM,CAAC;;MAErC;MACA,MAAME,SAAS,GAAG,IAAI5U,KAAK,CAAC6U,SAAS,CAAC,CAAC;MACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;MACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;MAEnC,MAAME,WAAW,GAAG,IAAIlV,KAAK,CAACmV,OAAO,CAACX,MAAM,EAAEE,MAAM,CAAC;MACrDE,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAE3I,SAAS,CAACiD,OAAO,CAAC;;MAEvD;MACA,MAAM6F,mBAAmB,GAAG,EAAE;MAC9BhS,gBAAgB,CAACuE,OAAO,CAAC,CAAC0N,QAAQ,EAAEzI,OAAO,KAAK;QAC9C,IAAIyI,QAAQ,CAACnO,KAAK,EAAE;UAClBkO,mBAAmB,CAAChF,IAAI,CAACiF,QAAQ,CAACnO,KAAK,CAAC;UACxCnE,OAAO,CAACC,GAAG,CAAC,SAAS4J,OAAO,QAAQ,CAAC;QACvC;MACF,CAAC,CAAC;;MAEF;MACA7J,OAAO,CAACC,GAAG,CAAC,QAAQoS,mBAAmB,CAACjN,MAAM,YAAY,CAAC;MAC3D,MAAMmN,YAAY,GAAGX,SAAS,CAACY,gBAAgB,CAACH,mBAAmB,EAAE,IAAI,CAAC;MAE1E,IAAIE,YAAY,CAACnN,MAAM,GAAG,CAAC,EAAE;QAC3BpF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1BsS,YAAY,CAAC3N,OAAO,CAAC,CAAC6N,SAAS,EAAEC,CAAC,KAAK;UACrC1S,OAAO,CAACC,GAAG,CAAC,MAAMyS,CAAC,GAAG,EAAED,SAAS,CAACnO,MAAM,CAAC8I,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAEqF,SAAS,CAAChQ,QAAQ,EACzB,WAAW,EAAEgQ,SAAS,CAACnO,MAAM,CAACiE,QAAQ,CAACoK,OAAO,CAAC,CAAC,EAChD,WAAW,EAAEF,SAAS,CAACnO,MAAM,CAACwM,QAAQ,CAAC;;UAEnD;UACA,MAAM8B,GAAG,GAAG/B,yBAAyB,CAAC4B,SAAS,CAACnO,MAAM,CAAC;UACvD,IAAIsO,GAAG,IAAIA,GAAG,CAAC9B,QAAQ,IAAI8B,GAAG,CAAC9B,QAAQ,CAAC/B,IAAI,KAAK,cAAc,EAAE;YAC/D/O,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2S,GAAG,CAAC9B,QAAQ,CAACjH,OAAO,CAAC;UAC/C;QACF,CAAC,CAAC;QAEF,OAAO,IAAI;MACb;;MAEA;MACA7J,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B,MAAM4S,eAAe,GAAGjB,SAAS,CAACY,gBAAgB,CAAC/T,KAAK,CAAC0P,QAAQ,EAAE,IAAI,CAAC;MAExEnO,OAAO,CAACC,GAAG,CAAC,WAAW4S,eAAe,CAACzN,MAAM,MAAM,CAAC;MACpDyN,eAAe,CAACjO,OAAO,CAAC,CAAC6N,SAAS,EAAEC,CAAC,KAAK;QACxC,MAAME,GAAG,GAAGH,SAAS,CAACnO,MAAM;QAC5BtE,OAAO,CAACC,GAAG,CAAC,QAAQyS,CAAC,GAAG,EAAEE,GAAG,CAACxF,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAEwF,GAAG,CAAC7D,IAAI,EACf,KAAK,EAAE6D,GAAG,CAACrK,QAAQ,CAACoK,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAEF,SAAS,CAAChQ,QAAQ,EACzB,WAAW,EAAEmQ,GAAG,CAAC9B,QAAQ,CAAC;MACxC,CAAC,CAAC;;MAEF;MACA9Q,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC3B,IAAI6S,YAAY,GAAG,CAAC;MAEpBzS,gBAAgB,CAACuE,OAAO,CAAC,CAAC0N,QAAQ,EAAEzI,OAAO,KAAK;QAC9C,IAAIyI,QAAQ,CAACnO,KAAK,EAAE;UAAA,IAAA4O,qBAAA;UAClB;UACA,IAAIC,SAAS,GAAGV,QAAQ,CAACnO,KAAK,CAACyF,OAAO;UACtC,IAAIqJ,cAAc,GAAG,IAAI;;UAEzB;UACA,MAAMC,QAAQ,GAAG,IAAIlW,KAAK,CAAC8F,OAAO,CAAC,CAAC;UACpCwP,QAAQ,CAACnO,KAAK,CAACgP,gBAAgB,CAACD,QAAQ,CAAC;;UAEzC;UACA,MAAME,gBAAgB,GAAGF,QAAQ,CAACxQ,UAAU,CAAC6G,SAAS,CAACiD,OAAO,CAACjE,QAAQ,CAAC;;UAExE;UACA,MAAM8K,SAAS,GAAGH,QAAQ,CAACpR,KAAK,CAAC,CAAC,CAACwR,OAAO,CAAC/J,SAAS,CAACiD,OAAO,CAAC;UAC7D,IAAItJ,IAAI,CAACK,GAAG,CAAC8P,SAAS,CAACrR,CAAC,CAAC,GAAG,CAAC,IAAIkB,IAAI,CAACK,GAAG,CAAC8P,SAAS,CAACnR,CAAC,CAAC,GAAG,CAAC,IAAImR,SAAS,CAACjR,CAAC,GAAG,CAAC,CAAC,IAAIiR,SAAS,CAACjR,CAAC,GAAG,CAAC,EAAE;YACjG6Q,cAAc,GAAG,KAAK;UACxB;UAEA,IAAID,SAAS,EAAE;YACbF,YAAY,EAAE;UAChB;UAEA9S,OAAO,CAACC,GAAG,CAAC,OAAO4J,OAAO,GAAG,EAAE;YAC7B0J,EAAE,EAAE,EAAAR,qBAAA,GAAAT,QAAQ,CAACnF,YAAY,cAAA4F,qBAAA,uBAArBA,qBAAA,CAAuB3F,IAAI,KAAI,IAAI;YACvCoG,GAAG,EAAER,SAAS;YACdS,KAAK,EAAER,cAAc;YACrBS,IAAI,EAAER,QAAQ,CAACP,OAAO,CAAC,CAAC;YACxBgB,IAAI,EAAE,CAACN,SAAS,CAACrR,CAAC,EAAEqR,SAAS,CAACnR,CAAC,EAAEmR,SAAS,CAACjR,CAAC,CAAC;YAC7CwR,MAAM,EAAER;UACV,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEFpT,OAAO,CAACC,GAAG,CAAC,MAAM6S,YAAY,IAAIzS,gBAAgB,CAACyM,IAAI,WAAW,CAAC;;MAEnE;MACA,OAAO+F,eAAe,CAACzN,MAAM,GAAG,CAAC;IACnC,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,OAAO,KAAK;IACd;EACF,CAAC;;EAID;EACA,MAAMuS,wBAAwB,GAAGA,CAACrG,YAAY,EAAEsG,SAAS,KAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC5D,IAAI,CAACzG,YAAY,IAAI,CAACA,YAAY,CAACrJ,KAAK,IAAI,CAAC2P,SAAS,EAAE;MACtD;IACF;;IAEA;IACA,MAAMI,cAAc,GAAG,EAAE;IACzB1G,YAAY,CAACrJ,KAAK,CAACE,QAAQ,CAAC8P,KAAK,IAAI;MACnC,IAAIA,KAAK,CAACrD,QAAQ,IAAIqD,KAAK,CAACrD,QAAQ,CAACsD,OAAO,EAAE;QAC5CF,cAAc,CAAC7G,IAAI,CAAC8G,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;IAEFD,cAAc,CAACtP,OAAO,CAACqI,KAAK,IAAI;MAC9BO,YAAY,CAACrJ,KAAK,CAACuB,MAAM,CAACuH,KAAK,CAAC;IAClC,CAAC,CAAC;;IAEF;IACA,IAAIoH,UAAU;IACd,QAAOP,SAAS,CAACtG,YAAY;MAC3B,KAAK,GAAG;QACN6G,UAAU,GAAG,QAAQ,CAAC,CAAC;QACvB;MACF,KAAK,GAAG;QACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;QACvB;MACF,KAAK,GAAG;MACR;QACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;QACvB;IACJ;;IAEA;IACA,MAAMC,aAAa,GAAG,IAAItX,KAAK,CAACuX,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IACzD,MAAMC,aAAa,GAAG,IAAIxX,KAAK,CAACyX,iBAAiB,CAAC;MAChDjK,KAAK,EAAE6J,UAAU;MACjBK,QAAQ,EAAEL,UAAU;MACpBM,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACF,MAAMC,SAAS,GAAG,IAAI5X,KAAK,CAAC6X,IAAI,CAACP,aAAa,EAAEE,aAAa,CAAC;IAC9DI,SAAS,CAACrM,QAAQ,CAAClG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAClCuS,SAAS,CAAC9D,QAAQ,GAAG;MACnBsD,OAAO,EAAE,IAAI;MACbrF,IAAI,EAAE,cAAc;MACpBlF,OAAO,GAAAkK,qBAAA,GAAEvG,YAAY,CAACL,YAAY,cAAA4G,qBAAA,uBAAzBA,qBAAA,CAA2BlK,OAAO;MAC3C8F,OAAO,EAAEmE,SAAS,CAACnE,OAAO;MAC1BmF,SAAS,EAAEhB,SAAS,CAACgB,SAAS;MAC9BlF,UAAU,EAAEkE,SAAS,CAAClE;IACxB,CAAC;;IAED;IACA,MAAM3C,KAAK,GAAG,IAAIjQ,KAAK,CAAC+X,UAAU,CAACV,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;IACrDpH,KAAK,CAAC1E,QAAQ,CAAClG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC5B4K,KAAK,CAAC6D,QAAQ,GAAG;MAAEsD,OAAO,EAAE;IAAK,CAAC;;IAElC;IACA5G,YAAY,CAACrJ,KAAK,CAACC,GAAG,CAACwQ,SAAS,CAAC;IACjCpH,YAAY,CAACrJ,KAAK,CAACC,GAAG,CAAC6I,KAAK,CAAC;IAE7BjN,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAA+T,sBAAA,GAAAxG,YAAY,CAACL,YAAY,cAAA6G,sBAAA,uBAAzBA,sBAAA,CAA2B5G,IAAI,OAAA6G,sBAAA,GAAIzG,YAAY,CAACL,YAAY,cAAA8G,sBAAA,uBAAzBA,sBAAA,CAA2BpK,OAAO,cAAaiK,SAAS,CAACtG,YAAY,WAAWsG,SAAS,CAAClE,UAAU,GAAG,CAAC;EACjK,CAAC;EAED,oBACEjS,OAAA;IAAKqX,GAAG,EAAElO,YAAa;IAACL,SAAS,EAAEA,SAAU;IAAA0H,QAAA,gBAE3CxQ,OAAA;MACEuQ,KAAK,EAAEjD,eAAgB;MACvBgK,OAAO,EAAEA,CAAA,KAAMzJ,uBAAuB,CAAC,CAACD,oBAAoB,CAAE;MAAA4C,QAAA,EAE7D5C,oBAAoB,GAAG,MAAM,GAAG;IAAM;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,EAGRjD,oBAAoB,iBACnB5N,OAAA;MAAKuQ,KAAK,EAAE9C,cAAe;MAAA+C,QAAA,gBACzBxQ,OAAA;QAAKuQ,KAAK,EAAE;UAAE2B,YAAY,EAAE,KAAK;UAAEpF,UAAU,EAAE;QAAO,CAAE;QAAA0D,QAAA,EAAC;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnE7Q,OAAA;QAAKuQ,KAAK,EAAE;UAAE1D,KAAK,EAAEiB,gBAAgB,CAACE,GAAG,GAAG,EAAE,GAAG,SAAS,GAAGF,gBAAgB,CAACE,GAAG,GAAG,EAAE,GAAG,SAAS,GAAG;QAAU,CAAE;QAAAwC,QAAA,GAAC,OAC3G,EAAC1C,gBAAgB,CAACE,GAAG;MAAA;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACN7Q,OAAA;QAAAwQ,QAAA,GAAK,gBACC,EAAC1C,gBAAgB,CAACG,MAAM,CAACE,IAAI,EAAC,GAAC,EAACL,gBAAgB,CAACG,MAAM,CAACC,KAAK,EAAC,OACjE,EAACJ,gBAAgB,CAACG,MAAM,CAACI,OAAO,EAAC,IACpC;MAAA;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN7Q,OAAA;QAAAwQ,QAAA,GAAK,kCACI,EAAC1C,gBAAgB,CAACQ,cAAc,CAACpI,MAAM;MAAA;QAAAwK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACN7Q,OAAA;QAAAwQ,QAAA,GAAK,4BACG,EAAC1C,gBAAgB,CAACQ,cAAc,CAACjI,MAAM;MAAA;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACN7Q,OAAA;QAAAwQ,QAAA,GAAK,4BACG,EAAC1C,gBAAgB,CAACQ,cAAc,CAACC,MAAM;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7Q,OAAA;MAAKuQ,KAAK,EAAE5F,oBAAqB;MAAA6F,QAAA,gBAC/BxQ,OAAA;QACEuQ,KAAK,EAAE;UACL,GAAGpF,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B;UAC/FoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG;QAC1C,CAAE;QACF6M,OAAO,EAAEA,CAAA,KAAM5M,WAAW,CAAC,QAAQ,CAAE;QAAA8F,QAAA,EACtC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7Q,OAAA;QACEuQ,KAAK,EAAE;UACL,GAAGpF,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,cAAc,GAAG,yBAAyB,GAAG,0BAA0B;UACrGoC,KAAK,EAAEpC,QAAQ,KAAK,cAAc,GAAG,MAAM,GAAG;QAChD,CAAE;QACF6M,OAAO,EAAEA,CAAA,KAAM5M,WAAW,CAAC,cAAc,CAAE;QAAA8F,QAAA,EAC5C;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7Q,OAAA;QACEuQ,KAAK,EAAE;UACL,GAAGpF,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,cAAc,GAAG,yBAAyB,GAAG,0BAA0B;UACrGoC,KAAK,EAAEpC,QAAQ,KAAK,cAAc,GAAG,MAAM,GAAG;QAChD,CAAE;QACF6M,OAAO,EAAEA,CAAA,KAAM5M,WAAW,CAAC,cAAc,CAAE;QAAA8F,QAAA,EAC5C;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7Q,OAAA;MAAMuQ,KAAK,EAAE5D,UAAW;MAAA6D,QAAA,EAAC;IAAG;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnC7Q,OAAA;MACEuQ,KAAK,EAAE/D,uBAAwB;MAC/B+K,KAAK,EAAE1L,oBAAoB,GAAGA,oBAAoB,CAAC0D,EAAE,GAAG,EAAG;MAC3DiI,QAAQ,EAAGC,CAAC,IAAK;QAAA,IAAAC,qBAAA;QACf,MAAMnI,EAAE,GAAGkI,CAAC,CAACE,MAAM,CAACJ,KAAK;QACzB,MAAM/H,YAAY,IAAAkI,qBAAA,GAAGjW,MAAM,CAACmW,aAAa,cAAAF,qBAAA,uBAApBA,qBAAA,CAAsBnU,IAAI,CAACwR,CAAC,IAAIA,CAAC,CAACxF,EAAE,KAAKA,EAAE,CAAC;QACjEzD,uBAAuB,CAAC0D,YAAY,IAAI,IAAI,CAAC;MAC/C,CAAE;MAAAgB,QAAA,gBAEFxQ,OAAA;QAAQuX,KAAK,EAAC,EAAE;QAAA/G,QAAA,EAAC;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,GAAA3H,sBAAA,GAC7BzH,MAAM,CAACmW,aAAa,cAAA1O,sBAAA,uBAApBA,sBAAA,CAAsBX,GAAG,CAACiH,YAAY,iBACrCxP,OAAA;QAA8BuX,KAAK,EAAE/H,YAAY,CAACD,EAAG;QAAAiB,QAAA,EAClDhB,YAAY,CAACC;MAAI,GADPD,YAAY,CAACD,EAAE;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEpB,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGR9E,mBAAmB,CAACE,OAAO,iBAC1BjM,OAAA;MACEuQ,KAAK,EAAE;QACL3F,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACvG,CAAC,IAAI;QAC3CoI,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAACrG,CAAC,IAAI;QAC1CyG,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,+BAA+B;QAC1CiC,QAAQ,EAAE,OAAO;QACjBkK,aAAa,EAAE;MACjB,CAAE;MAAArH,QAAA,eAEFxQ,OAAA;QAAKuQ,KAAK,EAAE;UAAEnF,OAAO,EAAE;QAAO,CAAE;QAAAoF,QAAA,gBAC9BxQ,OAAA;UAAKuQ,KAAK,EAAE;YACVtF,OAAO,EAAE,MAAM;YACfmF,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,QAAQ;YACpBW,YAAY,EAAE,oCAAoC;YAClDkB,YAAY,EAAE,KAAK;YACnB4F,aAAa,EAAE;UACjB,CAAE;UAAAtH,QAAA,gBACAxQ,OAAA;YAAQuQ,KAAK,EAAE;cAAE9E,QAAQ,EAAE;YAAO,CAAE;YAAA+E,QAAA,EAAC;UAErC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7Q,OAAA;YACEuQ,KAAK,EAAE;cACL/C,UAAU,EAAE,MAAM;cAClBlC,MAAM,EAAE,MAAM;cACduB,KAAK,EAAE,OAAO;cACdpB,QAAQ,EAAE,MAAM;cAChBD,MAAM,EAAE,SAAS;cACjBuM,OAAO,EAAE,GAAG;cACZpM,UAAU,EAAE;YACd,CAAE;YACF2L,OAAO,EAAEA,CAAA,KAAMtL,sBAAsB,CAAC;cAAE,GAAGD,mBAAmB;cAAEE,OAAO,EAAE;YAAM,CAAC,CAAE;YAAAuE,QAAA,EACnF;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL9E,mBAAmB,CAACI,OAAO;MAAA;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5H,EAAA,CApjCIJ,WAAW;AAAAmP,EAAA,GAAXnP,WAAW;AAsjCjB,eAAeA,WAAW;AAAC,IAAAmP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}