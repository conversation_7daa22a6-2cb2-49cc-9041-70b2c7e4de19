{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { responsiveArray } from '../_util/responsiveObserver';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport AvatarContext from './AvatarContext';\nimport useStyle from './style';\nconst InternalAvatar = (props, ref) => {\n  const [scale, setScale] = React.useState(1);\n  const [mounted, setMounted] = React.useState(false);\n  const [isImgExist, setIsImgExist] = React.useState(true);\n  const avatarNodeRef = React.useRef(null);\n  const avatarChildrenRef = React.useRef(null);\n  const avatarNodeMergedRef = composeRef(ref, avatarNodeRef);\n  const {\n    getPrefixCls,\n    avatar\n  } = React.useContext(ConfigContext);\n  const avatarCtx = React.useContext(AvatarContext);\n  const setScaleParam = () => {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n    const childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n    const nodeWidth = avatarNodeRef.current.offsetWidth;\n    // denominator is 0 is no meaning\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      const {\n        gap = 4\n      } = props;\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    setIsImgExist(true);\n    setScale(1);\n  }, [props.src]);\n  React.useEffect(setScaleParam, [props.gap]);\n  const handleImgLoadError = () => {\n    const {\n      onError\n    } = props;\n    const errorFlag = onError === null || onError === void 0 ? void 0 : onError();\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n  const {\n      prefixCls: customizePrefixCls,\n      shape,\n      size: customSize,\n      src,\n      srcSet,\n      icon,\n      className,\n      rootClassName,\n      alt,\n      draggable,\n      children,\n      crossOrigin\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"rootClassName\", \"alt\", \"draggable\", \"children\", \"crossOrigin\"]);\n  const size = useSize(ctxSize => {\n    var _a, _b;\n    return (_b = (_a = customSize !== null && customSize !== void 0 ? customSize : avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.size) !== null && _a !== void 0 ? _a : ctxSize) !== null && _b !== void 0 ? _b : 'default';\n  });\n  const needResponsive = Object.keys(typeof size === 'object' ? size || {} : {}).some(key => ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key));\n  const screens = useBreakpoint(needResponsive);\n  const responsiveSizeStyle = React.useMemo(() => {\n    if (typeof size !== 'object') {\n      return {};\n    }\n    const currentBreakpoint = responsiveArray.find(screen => screens[screen]);\n    const currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      fontSize: currentSize && (icon || children) ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Avatar');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'breaking', `\\`icon\\` is using ReactNode instead of string naming in v4. Please check \\`${icon}\\` at https://ant.design/components/icon`) : void 0;\n  }\n  const prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  const mergedShape = shape || (avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.shape) || 'circle';\n  const classString = classNames(prefixCls, sizeCls, avatar === null || avatar === void 0 ? void 0 : avatar.className, `${prefixCls}-${mergedShape}`, {\n    [`${prefixCls}-image`]: hasImageElement || src && isImgExist,\n    [`${prefixCls}-icon`]: !!icon\n  }, cssVarCls, rootCls, className, rootClassName, hashId);\n  const sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  let childrenToRender;\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    const transformString = `scale(${scale})`;\n    const childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      ref: avatarChildrenRef,\n      style: Object.assign({}, childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      style: {\n        opacity: 0\n      },\n      ref: avatarChildrenRef\n    }, children);\n  }\n  // The event is triggered twice from bubbling up the DOM tree.\n  // see https://codesandbox.io/s/kind-snow-9lidz\n  delete others.onError;\n  delete others.gap;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, others, {\n    style: Object.assign(Object.assign(Object.assign(Object.assign({}, sizeStyle), responsiveSizeStyle), avatar === null || avatar === void 0 ? void 0 : avatar.style), others.style),\n    className: classString,\n    ref: avatarNodeMergedRef\n  }), childrenToRender));\n};\nconst Avatar = /*#__PURE__*/React.forwardRef(InternalAvatar);\nif (process.env.NODE_ENV !== 'production') {\n  Avatar.displayName = 'Avatar';\n}\nexport default Avatar;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ResizeObserver", "composeRef", "responsiveArray", "devUseW<PERSON>ning", "ConfigContext", "useCSSVarCls", "useSize", "useBreakpoint", "AvatarContext", "useStyle", "InternalAvatar", "props", "ref", "scale", "setScale", "useState", "mounted", "setMounted", "isImgExist", "setIsImgExist", "avatarNodeRef", "useRef", "avatar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatarNodeMergedRef", "getPrefixCls", "avatar", "useContext", "avatarCtx", "setScaleParam", "current", "children<PERSON><PERSON>th", "offsetWidth", "nodeWidth", "gap", "useEffect", "src", "handleImgLoadError", "onError", "errorFlag", "prefixCls", "customizePrefixCls", "shape", "size", "customSize", "srcSet", "icon", "className", "rootClassName", "alt", "draggable", "children", "crossOrigin", "others", "ctxSize", "_a", "_b", "needResponsive", "keys", "some", "key", "includes", "screens", "responsiveSizeStyle", "useMemo", "currentBreakpoint", "find", "screen", "currentSize", "width", "height", "fontSize", "process", "env", "NODE_ENV", "warning", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "sizeCls", "hasImageElement", "isValidElement", "mergedShape", "classString", "sizeStyle", "children<PERSON><PERSON><PERSON><PERSON>", "createElement", "transformString", "childrenStyle", "msTransform", "WebkitTransform", "transform", "onResize", "style", "assign", "opacity", "Avatar", "forwardRef", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/avatar/Avatar.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { responsiveArray } from '../_util/responsiveObserver';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport AvatarContext from './AvatarContext';\nimport useStyle from './style';\nconst InternalAvatar = (props, ref) => {\n  const [scale, setScale] = React.useState(1);\n  const [mounted, setMounted] = React.useState(false);\n  const [isImgExist, setIsImgExist] = React.useState(true);\n  const avatarNodeRef = React.useRef(null);\n  const avatarChildrenRef = React.useRef(null);\n  const avatarNodeMergedRef = composeRef(ref, avatarNodeRef);\n  const {\n    getPrefixCls,\n    avatar\n  } = React.useContext(ConfigContext);\n  const avatarCtx = React.useContext(AvatarContext);\n  const setScaleParam = () => {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n    const childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n    const nodeWidth = avatarNodeRef.current.offsetWidth;\n    // denominator is 0 is no meaning\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      const {\n        gap = 4\n      } = props;\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    setIsImgExist(true);\n    setScale(1);\n  }, [props.src]);\n  React.useEffect(setScaleParam, [props.gap]);\n  const handleImgLoadError = () => {\n    const {\n      onError\n    } = props;\n    const errorFlag = onError === null || onError === void 0 ? void 0 : onError();\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n  const {\n      prefixCls: customizePrefixCls,\n      shape,\n      size: customSize,\n      src,\n      srcSet,\n      icon,\n      className,\n      rootClassName,\n      alt,\n      draggable,\n      children,\n      crossOrigin\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"rootClassName\", \"alt\", \"draggable\", \"children\", \"crossOrigin\"]);\n  const size = useSize(ctxSize => {\n    var _a, _b;\n    return (_b = (_a = customSize !== null && customSize !== void 0 ? customSize : avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.size) !== null && _a !== void 0 ? _a : ctxSize) !== null && _b !== void 0 ? _b : 'default';\n  });\n  const needResponsive = Object.keys(typeof size === 'object' ? size || {} : {}).some(key => ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key));\n  const screens = useBreakpoint(needResponsive);\n  const responsiveSizeStyle = React.useMemo(() => {\n    if (typeof size !== 'object') {\n      return {};\n    }\n    const currentBreakpoint = responsiveArray.find(screen => screens[screen]);\n    const currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      fontSize: currentSize && (icon || children) ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Avatar');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'breaking', `\\`icon\\` is using ReactNode instead of string naming in v4. Please check \\`${icon}\\` at https://ant.design/components/icon`) : void 0;\n  }\n  const prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  const mergedShape = shape || (avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.shape) || 'circle';\n  const classString = classNames(prefixCls, sizeCls, avatar === null || avatar === void 0 ? void 0 : avatar.className, `${prefixCls}-${mergedShape}`, {\n    [`${prefixCls}-image`]: hasImageElement || src && isImgExist,\n    [`${prefixCls}-icon`]: !!icon\n  }, cssVarCls, rootCls, className, rootClassName, hashId);\n  const sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  let childrenToRender;\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    const transformString = `scale(${scale})`;\n    const childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      ref: avatarChildrenRef,\n      style: Object.assign({}, childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      style: {\n        opacity: 0\n      },\n      ref: avatarChildrenRef\n    }, children);\n  }\n  // The event is triggered twice from bubbling up the DOM tree.\n  // see https://codesandbox.io/s/kind-snow-9lidz\n  delete others.onError;\n  delete others.gap;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, others, {\n    style: Object.assign(Object.assign(Object.assign(Object.assign({}, sizeStyle), responsiveSizeStyle), avatar === null || avatar === void 0 ? void 0 : avatar.style), others.style),\n    className: classString,\n    ref: avatarNodeMergedRef\n  }), childrenToRender));\n};\nconst Avatar = /*#__PURE__*/React.forwardRef(InternalAvatar);\nif (process.env.NODE_ENV !== 'production') {\n  Avatar.displayName = 'Avatar';\n}\nexport default Avatar;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EACrC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,KAAK,CAACiB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGrB,KAAK,CAACiB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMK,aAAa,GAAGtB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMC,iBAAiB,GAAGxB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAME,mBAAmB,GAAGtB,UAAU,CAACW,GAAG,EAAEQ,aAAa,CAAC;EAC1D,MAAM;IACJI,YAAY;IACZC;EACF,CAAC,GAAG3B,KAAK,CAAC4B,UAAU,CAACtB,aAAa,CAAC;EACnC,MAAMuB,SAAS,GAAG7B,KAAK,CAAC4B,UAAU,CAAClB,aAAa,CAAC;EACjD,MAAMoB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACN,iBAAiB,CAACO,OAAO,IAAI,CAACT,aAAa,CAACS,OAAO,EAAE;MACxD;IACF;IACA,MAAMC,aAAa,GAAGR,iBAAiB,CAACO,OAAO,CAACE,WAAW,CAAC,CAAC;IAC7D,MAAMC,SAAS,GAAGZ,aAAa,CAACS,OAAO,CAACE,WAAW;IACnD;IACA,IAAID,aAAa,KAAK,CAAC,IAAIE,SAAS,KAAK,CAAC,EAAE;MAC1C,MAAM;QACJC,GAAG,GAAG;MACR,CAAC,GAAGtB,KAAK;MACT,IAAIsB,GAAG,GAAG,CAAC,GAAGD,SAAS,EAAE;QACvBlB,QAAQ,CAACkB,SAAS,GAAGC,GAAG,GAAG,CAAC,GAAGH,aAAa,GAAG,CAACE,SAAS,GAAGC,GAAG,GAAG,CAAC,IAAIH,aAAa,GAAG,CAAC,CAAC;MAC3F;IACF;EACF,CAAC;EACDhC,KAAK,CAACoC,SAAS,CAAC,MAAM;IACpBjB,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACNnB,KAAK,CAACoC,SAAS,CAAC,MAAM;IACpBf,aAAa,CAAC,IAAI,CAAC;IACnBL,QAAQ,CAAC,CAAC,CAAC;EACb,CAAC,EAAE,CAACH,KAAK,CAACwB,GAAG,CAAC,CAAC;EACfrC,KAAK,CAACoC,SAAS,CAACN,aAAa,EAAE,CAACjB,KAAK,CAACsB,GAAG,CAAC,CAAC;EAC3C,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAM;MACJC;IACF,CAAC,GAAG1B,KAAK;IACT,MAAM2B,SAAS,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IAC7E,IAAIC,SAAS,KAAK,KAAK,EAAE;MACvBnB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EACD,MAAM;MACFoB,SAAS,EAAEC,kBAAkB;MAC7BC,KAAK;MACLC,IAAI,EAAEC,UAAU;MAChBR,GAAG;MACHS,MAAM;MACNC,IAAI;MACJC,SAAS;MACTC,aAAa;MACbC,GAAG;MACHC,SAAS;MACTC,QAAQ;MACRC;IACF,CAAC,GAAGxC,KAAK;IACTyC,MAAM,GAAGpE,MAAM,CAAC2B,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;EAC9J,MAAM+B,IAAI,GAAGpC,OAAO,CAAC+C,OAAO,IAAI;IAC9B,IAAIC,EAAE,EAAEC,EAAE;IACV,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGX,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGhB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACe,IAAI,MAAM,IAAI,IAAIY,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,SAAS;EAC3O,CAAC,CAAC;EACF,MAAMC,cAAc,GAAGnE,MAAM,CAACoE,IAAI,CAAC,OAAOf,IAAI,KAAK,QAAQ,GAAGA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAACgB,IAAI,CAACC,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC;EAC/I,MAAME,OAAO,GAAGtD,aAAa,CAACiD,cAAc,CAAC;EAC7C,MAAMM,mBAAmB,GAAGhE,KAAK,CAACiE,OAAO,CAAC,MAAM;IAC9C,IAAI,OAAOrB,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAO,CAAC,CAAC;IACX;IACA,MAAMsB,iBAAiB,GAAG9D,eAAe,CAAC+D,IAAI,CAACC,MAAM,IAAIL,OAAO,CAACK,MAAM,CAAC,CAAC;IACzE,MAAMC,WAAW,GAAGzB,IAAI,CAACsB,iBAAiB,CAAC;IAC3C,OAAOG,WAAW,GAAG;MACnBC,KAAK,EAAED,WAAW;MAClBE,MAAM,EAAEF,WAAW;MACnBG,QAAQ,EAAEH,WAAW,KAAKtB,IAAI,IAAIK,QAAQ,CAAC,GAAGiB,WAAW,GAAG,CAAC,GAAG;IAClE,CAAC,GAAG,CAAC,CAAC;EACR,CAAC,EAAE,CAACN,OAAO,EAAEnB,IAAI,CAAC,CAAC;EACnB,IAAI6B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGvE,aAAa,CAAC,QAAQ,CAAC;IACvCoE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,EAAE,OAAO7B,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACjD,MAAM,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,8EAA8EiD,IAAI,0CAA0C,CAAC,GAAG,KAAK,CAAC;EACpP;EACA,MAAMN,SAAS,GAAGf,YAAY,CAAC,QAAQ,EAAEgB,kBAAkB,CAAC;EAC5D,MAAMmC,OAAO,GAAGtE,YAAY,CAACkC,SAAS,CAAC;EACvC,MAAM,CAACqC,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC8B,SAAS,EAAEoC,OAAO,CAAC;EACpE,MAAMI,OAAO,GAAGhF,UAAU,CAAC;IACzB,CAAC,GAAGwC,SAAS,KAAK,GAAGG,IAAI,KAAK,OAAO;IACrC,CAAC,GAAGH,SAAS,KAAK,GAAGG,IAAI,KAAK;EAChC,CAAC,CAAC;EACF,MAAMsC,eAAe,GAAG,aAAalF,KAAK,CAACmF,cAAc,CAAC9C,GAAG,CAAC;EAC9D,MAAM+C,WAAW,GAAGzC,KAAK,KAAKd,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACc,KAAK,CAAC,IAAI,QAAQ;EAChH,MAAM0C,WAAW,GAAGpF,UAAU,CAACwC,SAAS,EAAEwC,OAAO,EAAEtD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACqB,SAAS,EAAE,GAAGP,SAAS,IAAI2C,WAAW,EAAE,EAAE;IAClJ,CAAC,GAAG3C,SAAS,QAAQ,GAAGyC,eAAe,IAAI7C,GAAG,IAAIjB,UAAU;IAC5D,CAAC,GAAGqB,SAAS,OAAO,GAAG,CAAC,CAACM;EAC3B,CAAC,EAAEiC,SAAS,EAAEH,OAAO,EAAE7B,SAAS,EAAEC,aAAa,EAAE8B,MAAM,CAAC;EACxD,MAAMO,SAAS,GAAG,OAAO1C,IAAI,KAAK,QAAQ,GAAG;IAC3C0B,KAAK,EAAE1B,IAAI;IACX2B,MAAM,EAAE3B,IAAI;IACZ4B,QAAQ,EAAEzB,IAAI,GAAGH,IAAI,GAAG,CAAC,GAAG;EAC9B,CAAC,GAAG,CAAC,CAAC;EACN,IAAI2C,gBAAgB;EACpB,IAAI,OAAOlD,GAAG,KAAK,QAAQ,IAAIjB,UAAU,EAAE;IACzCmE,gBAAgB,GAAG,aAAavF,KAAK,CAACwF,aAAa,CAAC,KAAK,EAAE;MACzDnD,GAAG,EAAEA,GAAG;MACRc,SAAS,EAAEA,SAAS;MACpBL,MAAM,EAAEA,MAAM;MACdP,OAAO,EAAED,kBAAkB;MAC3BY,GAAG,EAAEA,GAAG;MACRG,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI6B,eAAe,EAAE;IAC1BK,gBAAgB,GAAGlD,GAAG;EACxB,CAAC,MAAM,IAAIU,IAAI,EAAE;IACfwC,gBAAgB,GAAGxC,IAAI;EACzB,CAAC,MAAM,IAAI7B,OAAO,IAAIH,KAAK,KAAK,CAAC,EAAE;IACjC,MAAM0E,eAAe,GAAG,SAAS1E,KAAK,GAAG;IACzC,MAAM2E,aAAa,GAAG;MACpBC,WAAW,EAAEF,eAAe;MAC5BG,eAAe,EAAEH,eAAe;MAChCI,SAAS,EAAEJ;IACb,CAAC;IACDF,gBAAgB,GAAG,aAAavF,KAAK,CAACwF,aAAa,CAACtF,cAAc,EAAE;MAClE4F,QAAQ,EAAEhE;IACZ,CAAC,EAAE,aAAa9B,KAAK,CAACwF,aAAa,CAAC,MAAM,EAAE;MAC1CxC,SAAS,EAAE,GAAGP,SAAS,SAAS;MAChC3B,GAAG,EAAEU,iBAAiB;MACtBuE,KAAK,EAAExG,MAAM,CAACyG,MAAM,CAAC,CAAC,CAAC,EAAEN,aAAa;IACxC,CAAC,EAAEtC,QAAQ,CAAC,CAAC;EACf,CAAC,MAAM;IACLmC,gBAAgB,GAAG,aAAavF,KAAK,CAACwF,aAAa,CAAC,MAAM,EAAE;MAC1DxC,SAAS,EAAE,GAAGP,SAAS,SAAS;MAChCsD,KAAK,EAAE;QACLE,OAAO,EAAE;MACX,CAAC;MACDnF,GAAG,EAAEU;IACP,CAAC,EAAE4B,QAAQ,CAAC;EACd;EACA;EACA;EACA,OAAOE,MAAM,CAACf,OAAO;EACrB,OAAOe,MAAM,CAACnB,GAAG;EACjB,OAAO2C,UAAU,CAAC,aAAa9E,KAAK,CAACwF,aAAa,CAAC,MAAM,EAAEjG,MAAM,CAACyG,MAAM,CAAC,CAAC,CAAC,EAAE1C,MAAM,EAAE;IACnFyC,KAAK,EAAExG,MAAM,CAACyG,MAAM,CAACzG,MAAM,CAACyG,MAAM,CAACzG,MAAM,CAACyG,MAAM,CAACzG,MAAM,CAACyG,MAAM,CAAC,CAAC,CAAC,EAAEV,SAAS,CAAC,EAAEtB,mBAAmB,CAAC,EAAErC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACoE,KAAK,CAAC,EAAEzC,MAAM,CAACyC,KAAK,CAAC;IACjL/C,SAAS,EAAEqC,WAAW;IACtBvE,GAAG,EAAEW;EACP,CAAC,CAAC,EAAE8D,gBAAgB,CAAC,CAAC;AACxB,CAAC;AACD,MAAMW,MAAM,GAAG,aAAalG,KAAK,CAACmG,UAAU,CAACvF,cAAc,CAAC;AAC5D,IAAI6D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCuB,MAAM,CAACE,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}