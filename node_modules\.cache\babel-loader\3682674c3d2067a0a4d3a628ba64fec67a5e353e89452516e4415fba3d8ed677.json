{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      // 解析消息\n      const messageData = JSON.parse(message.toString());\n\n      // 直接处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n\n        // 获取交通参与者列表\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n\n        // 只处理机动车信息 (partPtcType === '1')\n        const vehicles = participants.filter(p => p.partPtcType === '1');\n        if (vehicles.length > 0) {\n          console.log('RSM消息中的机动车信息:', vehicles);\n\n          // 处理每个机动车\n          vehicles.forEach(async vehicle => {\n            const vehicleId = vehicle.partPtcId;\n\n            // 解析位置和状态信息\n            const vehicleState = {\n              longitude: parseFloat(vehicle.partPosLong),\n              latitude: parseFloat(vehicle.partPosLat),\n              speed: parseFloat(vehicle.partSpeed),\n              heading: parseFloat(vehicle.partHeading)\n            };\n\n            // 转换为模型坐标\n            const modelPos = converter.current.wgs84ToModel(vehicleState.longitude, vehicleState.latitude);\n\n            // 检查这个ID的车辆是否已经存在\n            if (!vehicleModels.has(vehicleId)) {\n              try {\n                if (!preloadedVehicleModel) {\n                  console.error('预加载的车辆模型不存在');\n                  return;\n                }\n\n                // 克隆预加载的模型\n                const vehicleModel = preloadedVehicleModel.clone();\n\n                // 设置位置和方向\n                vehicleModel.position.set(modelPos.x, 0.5, -modelPos.y);\n                vehicleModel.rotation.y = Math.PI - vehicleState.heading * Math.PI / 180;\n\n                // 添加到场景\n                scene.add(vehicleModel);\n\n                // 存储到Map中\n                vehicleModels.set(vehicleId, vehicleModel);\n\n                // 添加标签\n                const label = createTextSprite(`车辆ID: ${vehicleId}\\n速度: ${vehicleState.speed.toFixed(2)} m/s`);\n                label.position.set(0, 2, 0);\n                vehicleModel.add(label);\n                console.log(`创建新车辆实例: ID ${vehicleId}`, {\n                  位置: modelPos,\n                  朝向: vehicleState.heading\n                });\n              } catch (error) {\n                console.error(`创建车辆实例失败: ID ${vehicleId}`, error);\n              }\n            } else {\n              // 更新现有车辆的位置和朝向\n              const vehicleModel = vehicleModels.get(vehicleId);\n\n              // 创建位置补间动画\n              new TWEEN.Tween(vehicleModel.position).to({\n                x: modelPos.x,\n                y: 0.5,\n                z: -modelPos.y\n              }, 100).easing(TWEEN.Easing.Linear.None).start();\n\n              // 创建旋转补间动画\n              const targetRotation = Math.PI - vehicleState.heading * Math.PI / 180;\n              new TWEEN.Tween(vehicleModel.rotation).to({\n                y: targetRotation\n              }, 100).easing(TWEEN.Easing.Linear.None).start();\n\n              // 更新标签\n              vehicleModel.children.forEach(child => {\n                if (child.isSprite) {\n                  vehicleModel.remove(child);\n                }\n              });\n              const label = createTextSprite(`车辆ID: ${vehicleId}\\n速度: ${vehicleState.speed.toFixed(2)} m/s`);\n              label.position.set(0, 2, 0);\n              vehicleModel.add(label);\n            }\n          });\n        }\n\n        // 清理不再存在的车辆\n        const currentVehicleIds = new Set(vehicles.map(v => v.partPtcId));\n        vehicleModels.forEach((model, id) => {\n          if (!currentVehicleIds.has(id)) {\n            scene.remove(model);\n            vehicleModels.delete(id);\n            console.log(`移除离开区域的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载车辆模型\n    preloadVehicleModel();\n\n    // 创建场景\n    const scene = new THREE.Scene();\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 728,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"pok6xuZ9wkLpUfjRdkdxrFsX29M=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadVehicleModel = async () => {\n  try {\n    console.log('开始预加载车辆模型...');\n    const vehicleLoader = new GLTFLoader();\n    const gltf = await vehicleLoader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n    const vehicleModel = gltf.scene;\n\n    // 调整模型材质\n    vehicleModel.traverse(child => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n      }\n    });\n\n    // 存储预加载的模型\n    preloadedVehicleModel = vehicleModel;\n    console.log('车辆模型预加载成功');\n  } catch (error) {\n    console.error('车辆模型预加载失败:', error);\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "BASE_URL", "vehicleModels", "Map", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleMqttMessage", "topic", "message", "toString", "messageData", "JSON", "parse", "type", "_messageData$data", "participants", "data", "vehicles", "filter", "p", "partPtcType", "length", "for<PERSON>ach", "vehicle", "vehicleId", "partPtcId", "parseFloat", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "has", "error", "vehicleModel", "set", "rotation", "scene", "add", "label", "createTextSprite", "toFixed", "位置", "朝向", "get", "Linear", "None", "targetRotation", "children", "child", "isSprite", "remove", "currentVehicleIds", "Set", "map", "v", "model", "id", "delete", "bsmData", "newState", "partLong", "partLat", "newPosition", "Vector3", "updateMatrix", "updateMatrixWorld", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "event", "payload", "stringify", "onerror", "onclose", "setTimeout", "preloadVehicleModel", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleContainer", "Group", "traverse", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "name", "xhr", "loaded", "total", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "toArray", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "clearInterval", "end", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "ref", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "loadAsync", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      // 解析消息\n      const messageData = JSON.parse(message.toString());\n      \n      // 直接处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        // 获取交通参与者列表\n        const participants = messageData.data?.participants || [];\n        \n        // 只处理机动车信息 (partPtcType === '1')\n        const vehicles = participants.filter(p => p.partPtcType === '1');\n        \n        if (vehicles.length > 0) {\n          console.log('RSM消息中的机动车信息:', vehicles);\n          \n          // 处理每个机动车\n          vehicles.forEach(async (vehicle) => {\n            const vehicleId = vehicle.partPtcId;\n            \n            // 解析位置和状态信息\n            const vehicleState = {\n              longitude: parseFloat(vehicle.partPosLong),\n              latitude: parseFloat(vehicle.partPosLat),\n              speed: parseFloat(vehicle.partSpeed),\n              heading: parseFloat(vehicle.partHeading)\n            };\n            \n            // 转换为模型坐标\n            const modelPos = converter.current.wgs84ToModel(\n              vehicleState.longitude, \n              vehicleState.latitude\n            );\n            \n            // 检查这个ID的车辆是否已经存在\n            if (!vehicleModels.has(vehicleId)) {\n              try {\n                if (!preloadedVehicleModel) {\n                  console.error('预加载的车辆模型不存在');\n                  return;\n                }\n                \n                // 克隆预加载的模型\n                const vehicleModel = preloadedVehicleModel.clone();\n                \n                // 设置位置和方向\n                vehicleModel.position.set(modelPos.x, 0.5, -modelPos.y);\n                vehicleModel.rotation.y = Math.PI - vehicleState.heading * Math.PI / 180;\n                \n                // 添加到场景\n                scene.add(vehicleModel);\n                \n                // 存储到Map中\n                vehicleModels.set(vehicleId, vehicleModel);\n                \n                // 添加标签\n                const label = createTextSprite(`车辆ID: ${vehicleId}\\n速度: ${vehicleState.speed.toFixed(2)} m/s`);\n                label.position.set(0, 2, 0);\n                vehicleModel.add(label);\n                \n                console.log(`创建新车辆实例: ID ${vehicleId}`, {\n                  位置: modelPos,\n                  朝向: vehicleState.heading\n                });\n              } catch (error) {\n                console.error(`创建车辆实例失败: ID ${vehicleId}`, error);\n              }\n            } else {\n              // 更新现有车辆的位置和朝向\n              const vehicleModel = vehicleModels.get(vehicleId);\n              \n              // 创建位置补间动画\n              new TWEEN.Tween(vehicleModel.position)\n                .to({\n                  x: modelPos.x,\n                  y: 0.5,\n                  z: -modelPos.y\n                }, 100)\n                .easing(TWEEN.Easing.Linear.None)\n                .start();\n              \n              // 创建旋转补间动画\n              const targetRotation = Math.PI - vehicleState.heading * Math.PI / 180;\n              new TWEEN.Tween(vehicleModel.rotation)\n                .to({\n                  y: targetRotation\n                }, 100)\n                .easing(TWEEN.Easing.Linear.None)\n                .start();\n              \n              // 更新标签\n              vehicleModel.children.forEach(child => {\n                if (child.isSprite) {\n                  vehicleModel.remove(child);\n                }\n              });\n              const label = createTextSprite(`车辆ID: ${vehicleId}\\n速度: ${vehicleState.speed.toFixed(2)} m/s`);\n              label.position.set(0, 2, 0);\n              vehicleModel.add(label);\n            }\n          });\n        }\n        \n        // 清理不再存在的车辆\n        const currentVehicleIds = new Set(vehicles.map(v => v.partPtcId));\n        vehicleModels.forEach((model, id) => {\n          if (!currentVehicleIds.has(id)) {\n            scene.remove(model);\n            vehicleModels.delete(id);\n            console.log(`移除离开区域的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n      \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载车辆模型\n    preloadVehicleModel();\n    \n    // 创建场景\n    const scene = new THREE.Scene();\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n    };\n  }, []);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadVehicleModel = async () => {\n  try {\n    console.log('开始预加载车辆模型...');\n    const vehicleLoader = new GLTFLoader();\n    const gltf = await vehicleLoader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n    const vehicleModel = gltf.scene;\n    \n    // 调整模型材质\n    vehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n      }\n    });\n    \n    // 存储预加载的模型\n    preloadedVehicleModel = vehicleModel;\n    console.log('车辆模型预加载成功');\n  } catch (error) {\n    console.error('车辆模型预加载失败:', error);\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;;AAEhC;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE;EACP;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMqC,UAAU,GAAGrC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMsC,SAAS,GAAGtC,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMkC,aAAa,GAAGvC,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMwC,eAAe,GAAGxC,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMyC,aAAa,GAAGzC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC;IAC/C6C,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMmD,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGrE,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMsE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,WAAW,CAAC,QAAQ,CAAC;IACrBhC,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACmD,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,WAAW,CAAC,QAAQ,CAAC;IACrBhC,UAAU,GAAG,QAAQ;IAErB,IAAIkD,SAAS,CAACI,OAAO,IAAIrD,QAAQ,EAAE;MACjC;MACA,MAAMsD,UAAU,GAAGL,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACsB,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGP,SAAS,CAACI,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIrE,KAAK,CAACwE,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAAC7E,KAAK,CAAC8E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACmC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAInF,KAAK,CAACwE,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC7E,KAAK,CAAC8E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGtE,QAAQ,CAACuE,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIrE,KAAK,CAACwE,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC7E,KAAK,CAAC8E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdnE,QAAQ,CAACuE,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACArB,SAAS,CAACI,OAAO,CAACmB,MAAM,CAACxE,QAAQ,CAACuE,MAAM,CAAC;QACzCvE,QAAQ,CAACyE,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACArE,QAAQ,CAACmD,OAAO,GAAG,IAAI;;MAEvB;MACAnD,QAAQ,CAAC0E,WAAW,GAAG,EAAE;MACzB1E,QAAQ,CAAC2E,WAAW,GAAG,GAAG;MAC1B3E,QAAQ,CAAC4E,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC9E,QAAQ,CAAC+E,aAAa,GAAG,CAAC;MAC1B/E,QAAQ,CAACyE,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC5CP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBK,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACC,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF;MACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIF,KAAK,KAAKpF,WAAW,CAACM,MAAM,CAACE,GAAG,IAAI+E,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAAA,IAAAC,iBAAA;QAClEb,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEQ,WAAW,CAAC;;QAEpC;QACA,MAAMK,YAAY,GAAG,EAAAD,iBAAA,GAAAJ,WAAW,CAACM,IAAI,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBC,YAAY,KAAI,EAAE;;QAEzD;QACA,MAAME,QAAQ,GAAGF,YAAY,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,GAAG,CAAC;QAEhE,IAAIH,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;UACvBpB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEe,QAAQ,CAAC;;UAEtC;UACAA,QAAQ,CAACK,OAAO,CAAC,MAAOC,OAAO,IAAK;YAClC,MAAMC,SAAS,GAAGD,OAAO,CAACE,SAAS;;YAEnC;YACA,MAAMhF,YAAY,GAAG;cACnBE,SAAS,EAAE+E,UAAU,CAACH,OAAO,CAACI,WAAW,CAAC;cAC1C/E,QAAQ,EAAE8E,UAAU,CAACH,OAAO,CAACK,UAAU,CAAC;cACxC/E,KAAK,EAAE6E,UAAU,CAACH,OAAO,CAACM,SAAS,CAAC;cACpC/E,OAAO,EAAE4E,UAAU,CAACH,OAAO,CAACO,WAAW;YACzC,CAAC;;YAED;YACA,MAAMC,QAAQ,GAAG5F,SAAS,CAACmC,OAAO,CAAC0D,YAAY,CAC7CvF,YAAY,CAACE,SAAS,EACtBF,YAAY,CAACG,QACf,CAAC;;YAED;YACA,IAAI,CAACf,aAAa,CAACoG,GAAG,CAACT,SAAS,CAAC,EAAE;cACjC,IAAI;gBACF,IAAI,CAACtG,qBAAqB,EAAE;kBAC1B+E,OAAO,CAACiC,KAAK,CAAC,aAAa,CAAC;kBAC5B;gBACF;;gBAEA;gBACA,MAAMC,YAAY,GAAGjH,qBAAqB,CAACsD,KAAK,CAAC,CAAC;;gBAElD;gBACA2D,YAAY,CAACjF,QAAQ,CAACkF,GAAG,CAACL,QAAQ,CAAClD,CAAC,EAAE,GAAG,EAAE,CAACkD,QAAQ,CAACjD,CAAC,CAAC;gBACvDqD,YAAY,CAACE,QAAQ,CAACvD,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGtD,YAAY,CAACK,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;;gBAExE;gBACAuC,KAAK,CAACC,GAAG,CAACJ,YAAY,CAAC;;gBAEvB;gBACAtG,aAAa,CAACuG,GAAG,CAACZ,SAAS,EAAEW,YAAY,CAAC;;gBAE1C;gBACA,MAAMK,KAAK,GAAGC,gBAAgB,CAAC,SAASjB,SAAS,SAAS/E,YAAY,CAACI,KAAK,CAAC6F,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC9FF,KAAK,CAACtF,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC3BD,YAAY,CAACI,GAAG,CAACC,KAAK,CAAC;gBAEvBvC,OAAO,CAACC,GAAG,CAAC,eAAesB,SAAS,EAAE,EAAE;kBACtCmB,EAAE,EAAEZ,QAAQ;kBACZa,EAAE,EAAEnG,YAAY,CAACK;gBACnB,CAAC,CAAC;cACJ,CAAC,CAAC,OAAOoF,KAAK,EAAE;gBACdjC,OAAO,CAACiC,KAAK,CAAC,gBAAgBV,SAAS,EAAE,EAAEU,KAAK,CAAC;cACnD;YACF,CAAC,MAAM;cACL;cACA,MAAMC,YAAY,GAAGtG,aAAa,CAACgH,GAAG,CAACrB,SAAS,CAAC;;cAEjD;cACA,IAAIrH,KAAK,CAACwE,KAAK,CAACwD,YAAY,CAACjF,QAAQ,CAAC,CACnC0B,EAAE,CAAC;gBACFC,CAAC,EAAEkD,QAAQ,CAAClD,CAAC;gBACbC,CAAC,EAAE,GAAG;gBACNC,CAAC,EAAE,CAACgD,QAAQ,CAACjD;cACf,CAAC,EAAE,GAAG,CAAC,CACNE,MAAM,CAAC7E,KAAK,CAAC8E,MAAM,CAAC6D,MAAM,CAACC,IAAI,CAAC,CAChCzD,KAAK,CAAC,CAAC;;cAEV;cACA,MAAM0D,cAAc,GAAGlD,IAAI,CAACC,EAAE,GAAGtD,YAAY,CAACK,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;cACrE,IAAI5F,KAAK,CAACwE,KAAK,CAACwD,YAAY,CAACE,QAAQ,CAAC,CACnCzD,EAAE,CAAC;gBACFE,CAAC,EAAEkE;cACL,CAAC,EAAE,GAAG,CAAC,CACNhE,MAAM,CAAC7E,KAAK,CAAC8E,MAAM,CAAC6D,MAAM,CAACC,IAAI,CAAC,CAChCzD,KAAK,CAAC,CAAC;;cAEV;cACA6C,YAAY,CAACc,QAAQ,CAAC3B,OAAO,CAAC4B,KAAK,IAAI;gBACrC,IAAIA,KAAK,CAACC,QAAQ,EAAE;kBAClBhB,YAAY,CAACiB,MAAM,CAACF,KAAK,CAAC;gBAC5B;cACF,CAAC,CAAC;cACF,MAAMV,KAAK,GAAGC,gBAAgB,CAAC,SAASjB,SAAS,SAAS/E,YAAY,CAACI,KAAK,CAAC6F,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;cAC9FF,KAAK,CAACtF,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;cAC3BD,YAAY,CAACI,GAAG,CAACC,KAAK,CAAC;YACzB;UACF,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMa,iBAAiB,GAAG,IAAIC,GAAG,CAACrC,QAAQ,CAACsC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC/B,SAAS,CAAC,CAAC;QACjE5F,aAAa,CAACyF,OAAO,CAAC,CAACmC,KAAK,EAAEC,EAAE,KAAK;UACnC,IAAI,CAACL,iBAAiB,CAACpB,GAAG,CAACyB,EAAE,CAAC,EAAE;YAC9BpB,KAAK,CAACc,MAAM,CAACK,KAAK,CAAC;YACnB5H,aAAa,CAAC8H,MAAM,CAACD,EAAE,CAAC;YACxBzD,OAAO,CAACC,GAAG,CAAC,iBAAiBwD,EAAE,EAAE,CAAC;UACpC;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAInD,KAAK,KAAKpF,WAAW,CAACM,MAAM,CAACC,GAAG,IAAIgF,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAClEZ,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEQ,WAAW,CAAC;QAEpC,MAAMkD,OAAO,GAAGlD,WAAW,CAACM,IAAI;QAChC,MAAM6C,QAAQ,GAAG;UACflH,SAAS,EAAE+E,UAAU,CAACkC,OAAO,CAACE,QAAQ,CAAC;UACvClH,QAAQ,EAAE8E,UAAU,CAACkC,OAAO,CAACG,OAAO,CAAC;UACrClH,KAAK,EAAE6E,UAAU,CAACkC,OAAO,CAAC/B,SAAS,CAAC;UACpC/E,OAAO,EAAE4E,UAAU,CAACkC,OAAO,CAAC9B,WAAW;QACzC,CAAC;QAED7B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2D,QAAQ,CAAC;;QAElC;QACA,IAAIpJ,gBAAgB,EAAE;UACpB,MAAMsH,QAAQ,GAAG5F,SAAS,CAACmC,OAAO,CAAC0D,YAAY,CAAC6B,QAAQ,CAAClH,SAAS,EAAEkH,QAAQ,CAACjH,QAAQ,CAAC;UACtF,MAAMoH,WAAW,GAAG,IAAIjK,KAAK,CAACkK,OAAO,CAAClC,QAAQ,CAAClD,CAAC,EAAE,GAAG,EAAE,CAACkD,QAAQ,CAACjD,CAAC,CAAC;;UAEnE;UACArE,gBAAgB,CAACyC,QAAQ,CAACmC,IAAI,CAAC2E,WAAW,CAAC;UAC3CvJ,gBAAgB,CAAC4H,QAAQ,CAACvD,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG8D,QAAQ,CAAC/G,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;UACxEtF,gBAAgB,CAACyJ,YAAY,CAAC,CAAC;UAC/BzJ,gBAAgB,CAAC0J,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAzH,eAAe,CAACmH,QAAQ,CAAC;UACzB5D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE8D,WAAW,CAAC;QACtC;QACA;MACF;;MAEA;MACA/D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBK,KAAK;QACLM,IAAI,EAAEH,WAAW,CAACG,IAAI;QACtBG,IAAI,EAAEN;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCjC,OAAO,CAACiC,KAAK,CAAC,SAAS,EAAE1B,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAM2D,cAAc,GAAGA,CAAA,KAAM;IAC3BnE,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMmE,KAAK,GAAG,QAAQlJ,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEyE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmE,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBvE,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDoE,EAAE,CAACG,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMlE,OAAO,GAAGG,IAAI,CAACC,KAAK,CAAC8D,KAAK,CAAC1D,IAAI,CAAC;;QAEtC;QACA,IAAIR,OAAO,CAACK,IAAI,KAAK,SAAS,EAAE;UAC9BZ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEM,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACK,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIL,OAAO,CAACK,IAAI,KAAK,SAAS,IAAIL,OAAO,CAACD,KAAK,IAAIC,OAAO,CAACmE,OAAO,EAAE;UAClE;UACArE,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEI,IAAI,CAACiE,SAAS,CAACpE,OAAO,CAACmE,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOzC,KAAK,EAAE;QACdjC,OAAO,CAACiC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDoC,EAAE,CAACO,OAAO,GAAI3C,KAAK,IAAK;MACtBjC,OAAO,CAACiC,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDoC,EAAE,CAACQ,OAAO,GAAG,MAAM;MACjB7E,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA6E,UAAU,CAACX,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACA9H,aAAa,CAACgC,OAAO,GAAGgG,EAAE;EAC5B,CAAC;EAED1K,SAAS,CAAC,MAAM;IACd,IAAI,CAACqC,YAAY,CAACqC,OAAO,EAAE;;IAE3B;IACA0G,mBAAmB,CAAC,CAAC;;IAErB;IACA,MAAM1C,KAAK,GAAG,IAAIvI,KAAK,CAACkL,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,MAAM,GAAG,IAAInL,KAAK,CAACoL,iBAAiB,CACxC,EAAE,EACF9J,MAAM,CAAC+J,UAAU,GAAG/J,MAAM,CAACgK,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACDH,MAAM,CAAChI,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChC8C,MAAM,CAACzF,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBvB,SAAS,CAACI,OAAO,GAAG4G,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIvL,KAAK,CAACwL,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACpK,MAAM,CAAC+J,UAAU,EAAE/J,MAAM,CAACgK,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACtK,MAAM,CAACuK,gBAAgB,CAAC;IAC/C3J,YAAY,CAACqC,OAAO,CAACuH,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIhM,KAAK,CAACiM,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D1D,KAAK,CAACC,GAAG,CAACwD,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIlM,KAAK,CAACmM,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC/I,QAAQ,CAACkF,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CE,KAAK,CAACC,GAAG,CAAC0D,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIpM,KAAK,CAACmM,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACjJ,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CE,KAAK,CAACC,GAAG,CAAC4D,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIrM,KAAK,CAACsM,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAClJ,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCgE,SAAS,CAACE,KAAK,GAAGxG,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BqG,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBnE,KAAK,CAACC,GAAG,CAAC6D,SAAS,CAAC;;IAEpB;IACAnL,QAAQ,GAAG,IAAIhB,aAAa,CAACiL,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD7K,QAAQ,CAACyL,aAAa,GAAG,IAAI;IAC7BzL,QAAQ,CAAC0L,aAAa,GAAG,IAAI;IAC7B1L,QAAQ,CAAC2L,kBAAkB,GAAG,KAAK;IACnC3L,QAAQ,CAAC0E,WAAW,GAAG,EAAE;IACzB1E,QAAQ,CAAC2E,WAAW,GAAG,GAAG;IAC1B3E,QAAQ,CAAC4E,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC9E,QAAQ,CAAC+E,aAAa,GAAG,CAAC;IAC1B/E,QAAQ,CAACuE,MAAM,CAAC4C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BnH,QAAQ,CAACyE,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBgF,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBjK,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBiD,SAAS,EAAE,CAAC,CAACA,SAAS,CAACI;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMuI,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIjN,UAAU,CAAC,CAAC;QACtCiN,aAAa,CAACC,IAAI,CAChB,GAAGtL,QAAQ,uBAAuB,EACjCuL,IAAI,IAAK;UACR,MAAMhF,YAAY,GAAGgF,IAAI,CAAC7E,KAAK;;UAE/B;UACA,MAAM8E,gBAAgB,GAAG,IAAIrN,KAAK,CAACsN,KAAK,CAAC,CAAC;;UAE1C;UACAlF,YAAY,CAACmF,QAAQ,CAAEpE,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACqE,MAAM,EAAE;cAChB;cACA,IAAIrE,KAAK,CAACsE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI1N,KAAK,CAAC2N,oBAAoB,CAAC;kBACjDC,KAAK,EAAE,QAAQ;kBAAO;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAI5E,KAAK,CAACsE,QAAQ,CAACjE,GAAG,EAAE;kBACtBkE,WAAW,CAAClE,GAAG,GAAGL,KAAK,CAACsE,QAAQ,CAACjE,GAAG;gBACtC;;gBAEA;gBACAL,KAAK,CAACsE,QAAQ,GAAGC,WAAW;gBAE5BxH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgD,KAAK,CAAC6E,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM5F,YAAY,CAACc,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM6B,KAAK,GAAGf,YAAY,CAACc,QAAQ,CAAC,CAAC,CAAC;YACtCmE,gBAAgB,CAAC7E,GAAG,CAACW,KAAK,CAAC;UAC7B;;UAEA;UACAZ,KAAK,CAACC,GAAG,CAAC6E,gBAAgB,CAAC;;UAE3B;UACA3M,gBAAgB,GAAG2M,gBAAgB;UAEnCnH,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B1D,kBAAkB,CAAC,IAAI,CAAC;UACxBuK,OAAO,CAACK,gBAAgB,CAAC;QAC3B,CAAC,EACAY,GAAG,IAAK;UACP/H,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC8H,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAExF,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDsE,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMmB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMf,gBAAgB,GAAG,MAAMP,gBAAgB,CAAC,CAAC;;QAEjD;QACAzC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIgD,gBAAgB,EAAE;UACpB,MAAMgB,YAAY,GAAG;YACnBzL,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UACD,MAAMuL,UAAU,GAAGlM,SAAS,CAACmC,OAAO,CAAC0D,YAAY,CAACoG,YAAY,CAACzL,SAAS,EAAEyL,YAAY,CAACxL,QAAQ,CAAC;UAChGwK,gBAAgB,CAAClK,QAAQ,CAACkF,GAAG,CAACiG,UAAU,CAACxJ,CAAC,EAAE,GAAG,EAAE,CAACwJ,UAAU,CAACvJ,CAAC,CAAC;UAC/DsI,gBAAgB,CAAC/E,QAAQ,CAACvD,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAGqI,YAAY,CAACtL,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9EqH,gBAAgB,CAAClD,YAAY,CAAC,CAAC;UAC/BkD,gBAAgB,CAACjD,iBAAiB,CAAC,IAAI,CAAC;UACxCrJ,eAAe,GAAGsM,gBAAgB,CAAClK,QAAQ,CAACsB,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAO0D,KAAK,EAAE;QACdjC,OAAO,CAACiC,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMoG,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI1B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMyB,WAAW,GAAIC,WAAW,IAAK;UACnCzI,OAAO,CAACC,GAAG,CAAC,WAAWqI,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI3O,UAAU,CAAC,CAAC;UAC/B2O,MAAM,CAACzB,IAAI,CACTqB,GAAG,EACFpB,IAAI,IAAK;YACRlH,OAAO,CAACC,GAAG,CAAC,WAAWqI,GAAG,EAAE,CAAC;YAC7BxB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAa,GAAG,IAAK;YACP/H,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC8H,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAExF,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAR,KAAK,IAAK;YACTjC,OAAO,CAACiC,KAAK,CAAC,SAASqG,GAAG,EAAE,EAAErG,KAAK,CAAC;YACpC,IAAIwG,WAAW,GAAG,CAAC,EAAE;cACnBzI,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B6E,UAAU,CAAC,MAAM0D,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL1B,MAAM,CAAC9E,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDuG,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI3O,UAAU,CAAC,CAAC;IAC/B2O,MAAM,CAACzB,IAAI,CACT,GAAGtL,QAAQ,4BAA4B,EACvC,MAAOuL,IAAI,IAAK;MACd,IAAI;QACF,MAAM1D,KAAK,GAAG0D,IAAI,CAAC7E,KAAK;QACxBmB,KAAK,CAACmF,KAAK,CAACxG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBqB,KAAK,CAACvG,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BE,KAAK,CAACC,GAAG,CAACkB,KAAK,CAAC;;QAEhB;QACA,MAAM0E,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOjG,KAAK,EAAE;QACdjC,OAAO,CAACiC,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA8F,GAAG,IAAK;MACP/H,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC8H,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAExF,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAR,KAAK,IAAK;MACTjC,OAAO,CAACiC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjC,OAAO,CAACiC,KAAK,CAAC,OAAO,EAAE;QACrB2G,IAAI,EAAE3G,KAAK,CAACrB,IAAI;QAChBiI,IAAI,EAAE5G,KAAK,CAAC1B,OAAO;QACnBuI,KAAK,EAAE,GAAGnN,QAAQ,4BAA4B;QAC9CoN,KAAK,EAAE,GAAGpN,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMqN,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACA9O,KAAK,CAACuF,MAAM,CAAC,CAAC;MAEd,IAAI1E,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACmD,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM+K,UAAU,GAAG1O,gBAAgB,CAACyC,QAAQ,CAACsB,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM4K,eAAe,GAAG3O,gBAAgB,CAAC4H,QAAQ,CAACvD,CAAC;;QAEnD;QACA;QACA,MAAMuK,gBAAgB,GAAG,EAAED,eAAe,GAAGtJ,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMuJ,YAAY,GAAG,IAAIvP,KAAK,CAACkK,OAAO,CACpC,CAAC,EAAE,GAAGnE,IAAI,CAACyJ,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAGvJ,IAAI,CAAC0J,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAnE,MAAM,CAAChI,QAAQ,CAACmC,IAAI,CAAC8J,UAAU,CAAC,CAAC5G,GAAG,CAAC+G,YAAY,CAAC;;QAElD;QACApE,MAAM,CAACxG,EAAE,CAAC0D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMqH,YAAY,GAAGN,UAAU,CAAC3K,KAAK,CAAC,CAAC;QACvC0G,MAAM,CAACzF,MAAM,CAACgK,YAAY,CAAC;;QAE3B;QACAvE,MAAM,CAACwE,sBAAsB,CAAC,CAAC;QAC/BxE,MAAM,CAAChB,YAAY,CAAC,CAAC;QACrBgB,MAAM,CAACf,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAlJ,QAAQ,CAACmD,OAAO,GAAG,KAAK;;QAExB;QACAnD,QAAQ,CAACuE,MAAM,CAACH,IAAI,CAAC8J,UAAU,CAAC;QAChClO,QAAQ,CAACyE,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnByJ,IAAI,EAAER,UAAU,CAACS,OAAO,CAAC,CAAC;UAC1BC,IAAI,EAAE3E,MAAM,CAAChI,QAAQ,CAAC0M,OAAO,CAAC,CAAC;UAC/BE,IAAI,EAAEL,YAAY,CAACG,OAAO,CAAC,CAAC;UAC5BG,IAAI,EAAE7E,MAAM,CAAC8E,iBAAiB,CAAC,IAAIjQ,KAAK,CAACkK,OAAO,CAAC,CAAC,CAAC,CAAC2F,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI5O,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACmD,OAAO,GAAG,IAAI;;QAEvB;QACA8G,MAAM,CAACxG,EAAE,CAAC0D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAItC,IAAI,CAACmK,GAAG,CAAC/E,MAAM,CAAChI,QAAQ,CAAC4B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCoG,MAAM,CAAChI,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BnH,QAAQ,CAACuE,MAAM,CAAC4C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B8C,MAAM,CAACzF,MAAM,CAACxE,QAAQ,CAACuE,MAAM,CAAC;UAC9BvE,QAAQ,CAACyE,MAAM,CAAC,CAAC;QACnB;MACF;MAEAzE,QAAQ,CAACyE,MAAM,CAAC,CAAC;MACjB4F,QAAQ,CAAC4E,MAAM,CAAC5H,KAAK,EAAE4C,MAAM,CAAC;IAChC,CAAC;IAED+D,OAAO,CAAC,CAAC;;IAET;IACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzBjF,MAAM,CAACkF,MAAM,GAAG/O,MAAM,CAAC+J,UAAU,GAAG/J,MAAM,CAACgK,WAAW;MACtDH,MAAM,CAACwE,sBAAsB,CAAC,CAAC;MAC/BpE,QAAQ,CAACG,OAAO,CAACpK,MAAM,CAAC+J,UAAU,EAAE/J,MAAM,CAACgK,WAAW,CAAC;IACzD,CAAC;IACDhK,MAAM,CAACgP,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA9O,MAAM,CAACiP,aAAa,GAAG,MAAM;MAC3B,IAAIpM,SAAS,CAACI,OAAO,EAAE;QACrBJ,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzClE,SAAS,CAACI,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCvB,SAAS,CAACI,OAAO,CAAC4F,YAAY,CAAC,CAAC;QAChChG,SAAS,CAACI,OAAO,CAAC6F,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIlJ,QAAQ,EAAE;UACZA,QAAQ,CAACuE,MAAM,CAAC4C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BnH,QAAQ,CAACmD,OAAO,GAAG,IAAI;UACvBnD,QAAQ,CAACyE,MAAM,CAAC,CAAC;QACnB;QAEA1E,UAAU,GAAG,QAAQ;QACrBiF,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAAqK,qBAAA;MACX,IAAI3P,oBAAoB,EAAE;QACxB4P,aAAa,CAAC5P,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACA,IAAI0B,aAAa,CAACgC,OAAO,EAAE;QACzBhC,aAAa,CAACgC,OAAO,CAACmM,GAAG,CAAC,CAAC;MAC7B;MACApP,MAAM,CAACqP,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAAtO,YAAY,CAACqC,OAAO,cAAAiM,qBAAA,uBAApBA,qBAAA,CAAsBI,WAAW,CAACrF,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAACsF,OAAO,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEtQ,OAAA,CAAAE,SAAA;IAAAyI,QAAA,gBACE3I,OAAA;MAAKuQ,GAAG,EAAE5O,YAAa;MAAC6O,KAAK,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpE9Q,OAAA;MAAKwQ,KAAK,EAAE7N,oBAAqB;MAAAgG,QAAA,gBAC/B3I,OAAA;QACEwQ,KAAK,EAAE;UACL,GAAGrN,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E4K,KAAK,EAAE5K,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsO,OAAO,EAAElN,kBAAmB;QAAA8E,QAAA,EAC7B;MAED;QAAAgI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9Q,OAAA;QACEwQ,KAAK,EAAE;UACL,GAAGrN,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E4K,KAAK,EAAE5K,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsO,OAAO,EAAEhN,kBAAmB;QAAA4E,QAAA,EAC7B;MAED;QAAAgI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAApP,EAAA,CA1sBMD,WAAW;AAAAuP,EAAA,GAAXvP,WAAW;AA2sBjB,SAAS0G,gBAAgBA,CAAC8I,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACT,KAAK,GAAG,GAAG;EAClBS,MAAM,CAACR,MAAM,GAAG,EAAE;;EAElB;EACAW,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACT,KAAK,GAAC,CAAC,EAAES,MAAM,CAACR,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMiB,OAAO,GAAG,IAAIlS,KAAK,CAACmS,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAIpS,KAAK,CAACqS,cAAc,CAAC;IAC9C7I,GAAG,EAAE0I,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAIvS,KAAK,CAACwS,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAC1D,KAAK,CAACxG,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOkK,MAAM;AACf;;AAEA;AACAjR,MAAM,CAACmR,WAAW,GAAG,CAAC3N,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAItE,gBAAgB,EAAE;IACpBA,gBAAgB,CAACyC,QAAQ,CAACkF,GAAG,CAACvD,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtCtE,gBAAgB,CAACyJ,YAAY,CAAC,CAAC;IAC/BzJ,gBAAgB,CAAC0J,iBAAiB,CAAC,IAAI,CAAC;IACxClE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA1D,MAAM,CAACoR,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMvH,MAAM,GAAGuG,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAI1H,MAAM,EAAE;MACV;MACA,MAAM2H,MAAM,GAAG3H,MAAM,CAAChI,QAAQ,CAACsB,KAAK,CAAC,CAAC;;MAEtC;MACA0G,MAAM,CAAChI,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9B8C,MAAM,CAACxG,EAAE,CAAC0D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtB8C,MAAM,CAACzF,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAyF,MAAM,CAAChB,YAAY,CAAC,CAAC;MACrBgB,MAAM,CAACf,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMlJ,QAAQ,GAAGwQ,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAI7R,QAAQ,EAAE;QACZA,QAAQ,CAACuE,MAAM,CAAC4C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BnH,QAAQ,CAACyE,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB6M,GAAG,EAAEF,MAAM,CAACjD,OAAO,CAAC,CAAC;QACrBoD,GAAG,EAAE9H,MAAM,CAAChI,QAAQ,CAAC0M,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOqD,CAAC,EAAE;IACVhN,OAAO,CAACiC,KAAK,CAAC,YAAY,EAAE+K,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMjI,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACF/E,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAM+G,aAAa,GAAG,IAAIjN,UAAU,CAAC,CAAC;IACtC,MAAMmN,IAAI,GAAG,MAAMF,aAAa,CAACiG,SAAS,CAAC,GAAGtR,QAAQ,uBAAuB,CAAC;IAC9E,MAAMuG,YAAY,GAAGgF,IAAI,CAAC7E,KAAK;;IAE/B;IACAH,YAAY,CAACmF,QAAQ,CAAEpE,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACqE,MAAM,EAAE;QAChBrE,KAAK,CAACsE,QAAQ,GAAG,IAAIzN,KAAK,CAAC2N,oBAAoB,CAAC;UAC9CC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,GAAG;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACA5M,qBAAqB,GAAGiH,YAAY;IACpClC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOgC,KAAK,EAAE;IACdjC,OAAO,CAACiC,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;AAED,eAAenG,WAAW;AAAC,IAAAuP,EAAA;AAAA6B,YAAA,CAAA7B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}