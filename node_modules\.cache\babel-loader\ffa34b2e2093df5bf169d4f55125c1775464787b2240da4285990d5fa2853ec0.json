{"ast": null, "code": "import Menu from \"./Menu\";\nimport MenuItem from \"./MenuItem\";\nimport SubMenu from \"./SubMenu\";\nimport MenuItemGroup from \"./MenuItemGroup\";\nimport { useFullPath } from \"./context/PathContext\";\nimport Divider from \"./Divider\";\nexport { SubMenu, MenuItem as Item, MenuItem, MenuItemGroup, MenuItemGroup as ItemGroup, Divider, /** @private Only used for antd internal. Do not use in your production. */\nuseFullPath };\nvar ExportMenu = Menu;\nExportMenu.Item = MenuItem;\nExportMenu.SubMenu = SubMenu;\nExportMenu.ItemGroup = MenuItemGroup;\nExportMenu.Divider = Divider;\nexport default ExportMenu;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "MenuItem", "SubMenu", "MenuItemGroup", "useFullPath", "Divider", "<PERSON><PERSON>", "ItemGroup", "ExportMenu"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-menu/es/index.js"], "sourcesContent": ["import Menu from \"./Menu\";\nimport MenuItem from \"./MenuItem\";\nimport SubMenu from \"./SubMenu\";\nimport MenuItemGroup from \"./MenuItemGroup\";\nimport { useFullPath } from \"./context/PathContext\";\nimport Divider from \"./Divider\";\nexport { SubMenu, MenuItem as Item, MenuItem, MenuItemGroup, MenuItemGroup as ItemGroup, Divider, /** @private Only used for antd internal. Do not use in your production. */\nuseFullPath };\nvar ExportMenu = Menu;\nExportMenu.Item = MenuItem;\nExportMenu.SubMenu = SubMenu;\nExportMenu.ItemGroup = MenuItemGroup;\nExportMenu.Divider = Divider;\nexport default ExportMenu;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASH,OAAO,EAAED,QAAQ,IAAIK,IAAI,EAAEL,QAAQ,EAAEE,aAAa,EAAEA,aAAa,IAAII,SAAS,EAAEF,OAAO,EAAE;AAClGD,WAAW;AACX,IAAII,UAAU,GAAGR,IAAI;AACrBQ,UAAU,CAACF,IAAI,GAAGL,QAAQ;AAC1BO,UAAU,CAACN,OAAO,GAAGA,OAAO;AAC5BM,UAAU,CAACD,SAAS,GAAGJ,aAAa;AACpCK,UAAU,CAACH,OAAO,GAAGA,OAAO;AAC5B,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}