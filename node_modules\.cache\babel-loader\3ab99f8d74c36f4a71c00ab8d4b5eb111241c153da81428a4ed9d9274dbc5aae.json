{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\SystemManagement.jsx\",\n  _s = $RefreshSig$();\n// src/pages/SystemManagement.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, message, Card } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport DeviceManagement from './DeviceManagement';\nimport styled from 'styled-components';\nimport devicesData from '../data/devices.json';\nimport intersectionsData from '../data/intersections.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\n\n// 修改容器样式，确保整个组件在3D模型上层\nconst SystemContainer = styled.div`\n  position: relative;\n  z-index: 1002;\n  background: white;\n  pointer-events: auto;\n`;\n_c = SystemContainer;\nconst TabsContainer = styled.div`\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n_c2 = TabsContainer;\nconst TabButton = styled.button`\n  padding: 10px 20px;\n  background: ${props => props.active ? '#e6f7ff' : 'white'};\n  border: none;\n  border-bottom: 2px solid ${props => props.active ? '#1890ff' : 'transparent'};\n  color: ${props => props.active ? '#1890ff' : 'rgba(0, 0, 0, 0.65)'};\n  cursor: pointer;\n  font-size: 14px;\n  margin-right: 20px;\n  transition: all 0.3s;\n  pointer-events: auto;\n  \n  &:hover {\n    color: #1890ff;\n  }\n  \n  &:focus {\n    outline: none;\n  }\n`;\n_c3 = TabButton;\nconst ContentArea = styled.div`\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n_c4 = ContentArea;\nconst SystemManagement = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [users, setUsers] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingUser, setEditingUser] = useState(null);\n  const [activeTab, setActiveTab] = useState('users');\n  const [intersections, setIntersections] = useState([]);\n  const [intersectionModalVisible, setIntersectionModalVisible] = useState(false);\n  const [editingIntersection, setEditingIntersection] = useState(null);\n  const [intersectionForm] = Form.useForm();\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      console.log('开始获取用户列表...');\n      const token = localStorage.getItem('token');\n      console.log('使用令牌获取用户列表:', token);\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/users`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('API响应:', response.data);\n      if (response.data && response.data.success) {\n        setUsers(response.data.data || []);\n      } else {\n        var _response$data;\n        message.warning('获取用户列表失败: ' + (((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '未知错误'));\n      }\n    } catch (error) {\n      console.error('API获取用户列表失败:', error);\n      message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchUsers();\n    // 从 devices.json 中获取唯一的路口名称\n    const uniqueLocations = [...new Set(devicesData.devices.map(device => device.location))];\n\n    // 将路口名称与已有的路口数据合并\n    const mergedIntersections = uniqueLocations.map(location => {\n      const existingIntersection = intersectionsData.intersections.find(i => i.name === location);\n      return existingIntersection || {\n        id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        name: location,\n        latitude: '',\n        longitude: '',\n        createdAt: new Date().toISOString()\n      };\n    });\n    setIntersections(mergedIntersections);\n  }, []);\n\n  // 修改标签切换处理函数\n  const handleTabChange = tab => {\n    console.log('切换到标签:', tab);\n    setActiveTab(tab);\n\n    // 切换到设备管理标签时重新获取设备列表\n    if (tab === 'devices') {\n      const deviceManagementRef = document.querySelector('#device-management');\n      if (deviceManagementRef) {\n        deviceManagementRef.fetchDevices();\n      }\n    }\n  };\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEditUser = user => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      role: user.role,\n      email: user.email\n    });\n    setModalVisible(true);\n  };\n  const handleDeleteUser = async userId => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 尝试通过API删除\n      try {\n        await axios.delete(`${apiUrl}/api/users/${userId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        message.success('用户已删除');\n        fetchUsers(); // 重新获取用户列表\n      } catch (error) {\n        console.error('API删除用户失败:', error);\n\n        // 如果API删除失败，从本地存储中删除\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        const updatedUsers = localUsers.filter(user => user.id !== userId);\n        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        message.success('用户已删除（本地存储）');\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (error.message || '未知错误'));\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('表单数据:', values);\n      const token = localStorage.getItem('token');\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 尝试通过API保存用户\n      try {\n        let response;\n        if (editingUser) {\n          // 更新用户\n          response = await axios.put(`${apiUrl}/api/users/${editingUser.id}`, values, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加用户\n          response = await axios.post(`${apiUrl}/api/users`, values, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        console.log('API响应:', response.data);\n        if (response.data && response.data.success) {\n          message.success(editingUser ? '用户已更新' : '用户已添加');\n          setModalVisible(false);\n          form.resetFields();\n          fetchUsers(); // 重新获取用户列表\n        } else {\n          var _response$data2;\n          message.error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || '操作失败');\n        }\n      } catch (error) {\n        console.error('API保存用户失败:', error);\n\n        // 如果API保存失败，保存到本地存储\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        if (editingUser) {\n          // 更新用户\n          const updatedUsers = localUsers.map(user => user.id === editingUser.id ? {\n            ...user,\n            ...values\n          } : user);\n          localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        } else {\n          // 添加用户\n          const newUser = {\n            ...values,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString()\n          };\n          localUsers.push(newUser);\n          localStorage.setItem('localUsers', JSON.stringify(localUsers));\n        }\n        message.success(editingUser ? '用户已更新（本地存储）' : '用户已添加（本地存储）');\n        setModalVisible(false);\n        form.resetFields();\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n  const columns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '角色',\n    dataIndex: 'role',\n    key: 'role',\n    render: role => {\n      const roleMap = {\n        'admin': '管理员',\n        'monitor': '监控人员',\n        'user': '普通用户',\n        'maintenance': '设备维护人员'\n      };\n      return roleMap[role] || role;\n    }\n  }, {\n    title: '邮箱',\n    dataIndex: 'email',\n    key: 'email'\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    render: date => date ? new Date(date).toLocaleString() : '-'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditUser(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteUser(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 添加路口相关的处理函数\n  const handleEditIntersection = intersection => {\n    setEditingIntersection(intersection);\n    intersectionForm.setFieldsValue(intersection);\n    setIntersectionModalVisible(true);\n  };\n  const handleIntersectionModalOk = async () => {\n    try {\n      const values = await intersectionForm.validateFields();\n      const updatedIntersections = intersections.map(intersection => intersection.id === editingIntersection.id ? {\n        ...intersection,\n        ...values\n      } : intersection);\n      setIntersections(updatedIntersections);\n      setIntersectionModalVisible(false);\n      message.success('路口信息更新成功');\n    } catch (error) {\n      message.error('表单验证失败');\n    }\n  };\n\n  // 添加路口管理的列定义\n  const intersectionColumns = [{\n    title: '路口名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '纬度',\n    dataIndex: 'latitude',\n    key: 'latitude'\n  }, {\n    title: '经度',\n    dataIndex: 'longitude',\n    key: 'longitude'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      onClick: () => handleEditIntersection(record),\n      children: \"\\u7F16\\u8F91\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 修改 renderContent 函数\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'users':\n        return /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7528\\u6237\\u5217\\u8868\",\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleAddUser,\n            children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 20\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            loading: loading,\n            dataSource: users,\n            columns: columns,\n            rowKey: \"id\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this);\n      case 'devices':\n        return /*#__PURE__*/_jsxDEV(DeviceManagement, {\n          id: \"device-management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 16\n        }, this);\n      case 'intersections':\n        return /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8DEF\\u53E3\\u7BA1\\u7406\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: intersections,\n            columns: intersectionColumns,\n            rowKey: \"id\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(SystemContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TabsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'users',\n        onClick: () => handleTabChange('users'),\n        children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'devices',\n        onClick: () => handleTabChange('devices'),\n        children: \"\\u8BBE\\u5907\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'intersections',\n        onClick: () => handleTabChange('intersections'),\n        children: \"\\u8DEF\\u53E3\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingUser ? \"编辑用户\" : \"添加用户\",\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: handleModalCancel,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          role: 'user'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          label: \"\\u7528\\u6237\\u540D\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 28\n            }, this),\n            placeholder: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), !editingUser && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"\\u5BC6\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入密码'\n          }, {\n            min: 6,\n            message: '密码长度至少为6个字符'\n          }],\n          extra: \"\\u5BC6\\u7801\\u957F\\u5EA6\\u81F3\\u5C11\\u4E3A6\\u4E2A\\u5B57\\u7B26\",\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 39\n            }, this),\n            placeholder: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"role\",\n          label: \"\\u89D2\\u8272\",\n          rules: [{\n            required: true,\n            message: '请选择角色'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u89D2\\u8272\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"admin\",\n              children: \"\\u7BA1\\u7406\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"monitor\",\n              children: \"\\u76D1\\u63A7\\u4EBA\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"user\",\n              children: \"\\u666E\\u901A\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"maintenance\",\n              children: \"\\u8BBE\\u5907\\u7EF4\\u62A4\\u4EBA\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"\\u90AE\\u7BB1\",\n          rules: [{\n            type: 'email',\n            message: '请输入有效的邮箱地址'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 28\n            }, this),\n            placeholder: \"\\u90AE\\u7BB1\\uFF08\\u9009\\u586B\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u7F16\\u8F91\\u8DEF\\u53E3\\u4FE1\\u606F\",\n      open: intersectionModalVisible,\n      onOk: handleIntersectionModalOk,\n      onCancel: () => setIntersectionModalVisible(false),\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: intersectionForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8DEF\\u53E3\\u540D\\u79F0\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"latitude\",\n          label: \"\\u7EAC\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入纬度'\n          }, {\n            pattern: /^-?([0-8]?[0-9]|90)(\\.[0-9]{1,6})?$/,\n            message: '请输入有效的纬度值 (-90 到 90)'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7EAC\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"longitude\",\n          label: \"\\u7ECF\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入经度'\n          }, {\n            pattern: /^-?((1?[0-7]?|[0-9]?)[0-9]|180)(\\.[0-9]{1,6})?$/,\n            message: '请输入有效的经度值 (-180 到 180)'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7ECF\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 397,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemManagement, \"rnQW//0AOPAI+JB4m9eK760YeW4=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c5 = SystemManagement;\nexport default SystemManagement;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SystemContainer\");\n$RefreshReg$(_c2, \"TabsContainer\");\n$RefreshReg$(_c3, \"TabButton\");\n$RefreshReg$(_c4, \"ContentArea\");\n$RefreshReg$(_c5, \"SystemManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Card", "UserOutlined", "LockOutlined", "MailOutlined", "axios", "DeviceManagement", "styled", "devicesData", "intersectionsData", "jsxDEV", "_jsxDEV", "Option", "SystemContainer", "div", "_c", "TabsContainer", "_c2", "TabButton", "button", "props", "active", "_c3", "ContentArea", "_c4", "SystemManagement", "_s", "loading", "setLoading", "users", "setUsers", "modalVisible", "setModalVisible", "form", "useForm", "editingUser", "setEditingUser", "activeTab", "setActiveTab", "intersections", "setIntersections", "intersectionModalVisible", "setIntersectionModalVisible", "editingIntersection", "setEditingIntersection", "intersectionForm", "fetchUsers", "console", "log", "token", "localStorage", "getItem", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "headers", "data", "success", "_response$data", "warning", "error", "uniqueLocations", "Set", "devices", "map", "device", "location", "mergedIntersections", "existingIntersection", "find", "i", "name", "id", "Date", "now", "Math", "random", "toString", "substr", "latitude", "longitude", "createdAt", "toISOString", "handleTabChange", "tab", "deviceManagementRef", "document", "querySelector", "fetchDevices", "handleAddUser", "resetFields", "handleEditUser", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username", "role", "email", "handleDeleteUser", "userId", "delete", "localUsers", "JSON", "parse", "updatedUsers", "filter", "setItem", "stringify", "handleModalOk", "values", "validateFields", "put", "post", "_response$data2", "newUser", "push", "handleModalCancel", "columns", "title", "dataIndex", "key", "render", "roleMap", "date", "toLocaleString", "_", "record", "children", "type", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "danger", "handleEditIntersection", "intersection", "handleIntersectionModalOk", "updatedIntersections", "intersectionColumns", "renderContent", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "destroyOnClose", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "prefix", "placeholder", "min", "Password", "value", "disabled", "pattern", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/SystemManagement.jsx"], "sourcesContent": ["// src/pages/SystemManagement.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, message, Card } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport DeviceManagement from './DeviceManagement';\nimport styled from 'styled-components';\nimport devicesData from '../data/devices.json';\nimport intersectionsData from '../data/intersections.json';\n\nconst { Option } = Select;\n\n// 修改容器样式，确保整个组件在3D模型上层\nconst SystemContainer = styled.div`\n  position: relative;\n  z-index: 1002;\n  background: white;\n  pointer-events: auto;\n`;\n\nconst TabsContainer = styled.div`\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n\nconst TabButton = styled.button`\n  padding: 10px 20px;\n  background: ${props => props.active ? '#e6f7ff' : 'white'};\n  border: none;\n  border-bottom: 2px solid ${props => props.active ? '#1890ff' : 'transparent'};\n  color: ${props => props.active ? '#1890ff' : 'rgba(0, 0, 0, 0.65)'};\n  cursor: pointer;\n  font-size: 14px;\n  margin-right: 20px;\n  transition: all 0.3s;\n  pointer-events: auto;\n  \n  &:hover {\n    color: #1890ff;\n  }\n  \n  &:focus {\n    outline: none;\n  }\n`;\n\nconst ContentArea = styled.div`\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n\nconst SystemManagement = () => {\n  const [loading, setLoading] = useState(true);\n  const [users, setUsers] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingUser, setEditingUser] = useState(null);\n  const [activeTab, setActiveTab] = useState('users');\n  const [intersections, setIntersections] = useState([]);\n  const [intersectionModalVisible, setIntersectionModalVisible] = useState(false);\n  const [editingIntersection, setEditingIntersection] = useState(null);\n  const [intersectionForm] = Form.useForm();\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      console.log('开始获取用户列表...');\n      \n      const token = localStorage.getItem('token');\n      console.log('使用令牌获取用户列表:', token);\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/users`, {\n        headers: { \n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      console.log('API响应:', response.data);\n      \n      if (response.data && response.data.success) {\n        setUsers(response.data.data || []);\n      } else {\n        message.warning('获取用户列表失败: ' + (response.data?.message || '未知错误'));\n      }\n    } catch (error) {\n      console.error('API获取用户列表失败:', error);\n      message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n    // 从 devices.json 中获取唯一的路口名称\n    const uniqueLocations = [...new Set(devicesData.devices.map(device => device.location))];\n    \n    // 将路口名称与已有的路口数据合并\n    const mergedIntersections = uniqueLocations.map(location => {\n      const existingIntersection = intersectionsData.intersections.find(i => i.name === location);\n      return existingIntersection || {\n        id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        name: location,\n        latitude: '',\n        longitude: '',\n        createdAt: new Date().toISOString()\n      };\n    });\n    \n    setIntersections(mergedIntersections);\n  }, []);\n\n  // 修改标签切换处理函数\n  const handleTabChange = (tab) => {\n    console.log('切换到标签:', tab);\n    setActiveTab(tab);\n    \n    // 切换到设备管理标签时重新获取设备列表\n    if (tab === 'devices') {\n      const deviceManagementRef = document.querySelector('#device-management');\n      if (deviceManagementRef) {\n        deviceManagementRef.fetchDevices();\n      }\n    }\n  };\n\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditUser = (user) => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      role: user.role,\n      email: user.email,\n    });\n    setModalVisible(true);\n  };\n\n  const handleDeleteUser = async (userId) => {\n    try {\n      const token = localStorage.getItem('token');\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      \n      // 尝试通过API删除\n      try {\n        await axios.delete(`${apiUrl}/api/users/${userId}`, {\n          headers: { \n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        message.success('用户已删除');\n        fetchUsers(); // 重新获取用户列表\n      } catch (error) {\n        console.error('API删除用户失败:', error);\n        \n        // 如果API删除失败，从本地存储中删除\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        const updatedUsers = localUsers.filter(user => user.id !== userId);\n        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        \n        message.success('用户已删除（本地存储）');\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('表单数据:', values);\n      \n      const token = localStorage.getItem('token');\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      \n      // 尝试通过API保存用户\n      try {\n        let response;\n        \n        if (editingUser) {\n          // 更新用户\n          response = await axios.put(`${apiUrl}/api/users/${editingUser.id}`, values, {\n            headers: { \n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加用户\n          response = await axios.post(`${apiUrl}/api/users`, values, {\n            headers: { \n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        \n        console.log('API响应:', response.data);\n        \n        if (response.data && response.data.success) {\n          message.success(editingUser ? '用户已更新' : '用户已添加');\n          setModalVisible(false);\n          form.resetFields();\n          fetchUsers(); // 重新获取用户列表\n        } else {\n          message.error(response.data?.message || '操作失败');\n        }\n      } catch (error) {\n        console.error('API保存用户失败:', error);\n        \n        // 如果API保存失败，保存到本地存储\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        \n        if (editingUser) {\n          // 更新用户\n          const updatedUsers = localUsers.map(user => \n            user.id === editingUser.id ? { ...user, ...values } : user\n          );\n          localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        } else {\n          // 添加用户\n          const newUser = {\n            ...values,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString()\n          };\n          localUsers.push(newUser);\n          localStorage.setItem('localUsers', JSON.stringify(localUsers));\n        }\n        \n        message.success(editingUser ? '用户已更新（本地存储）' : '用户已添加（本地存储）');\n        setModalVisible(false);\n        form.resetFields();\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n\n  const columns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: '角色',\n      dataIndex: 'role',\n      key: 'role',\n      render: (role) => {\n        const roleMap = {\n          'admin': '管理员',\n          'monitor': '监控人员',\n          'user': '普通用户',\n          'maintenance': '设备维护人员'\n        };\n        return roleMap[role] || role;\n      }\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      render: date => date ? new Date(date).toLocaleString() : '-'\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <div>\n          <Button type=\"link\" onClick={() => handleEditUser(record)}>编辑</Button>\n          <Button type=\"link\" danger onClick={() => handleDeleteUser(record.id)}>删除</Button>\n        </div>\n      ),\n    },\n  ];\n\n  // 添加路口相关的处理函数\n  const handleEditIntersection = (intersection) => {\n    setEditingIntersection(intersection);\n    intersectionForm.setFieldsValue(intersection);\n    setIntersectionModalVisible(true);\n  };\n\n  const handleIntersectionModalOk = async () => {\n    try {\n      const values = await intersectionForm.validateFields();\n      const updatedIntersections = intersections.map(intersection =>\n        intersection.id === editingIntersection.id\n          ? { ...intersection, ...values }\n          : intersection\n      );\n      setIntersections(updatedIntersections);\n      setIntersectionModalVisible(false);\n      message.success('路口信息更新成功');\n    } catch (error) {\n      message.error('表单验证失败');\n    }\n  };\n\n  // 添加路口管理的列定义\n  const intersectionColumns = [\n    {\n      title: '路口名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '纬度',\n      dataIndex: 'latitude',\n      key: 'latitude',\n    },\n    {\n      title: '经度',\n      dataIndex: 'longitude',\n      key: 'longitude',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Button type=\"link\" onClick={() => handleEditIntersection(record)}>\n          编辑\n        </Button>\n      ),\n    },\n  ];\n\n  // 修改 renderContent 函数\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'users':\n        return (\n          <Card \n            title=\"用户列表\" \n            extra={<Button type=\"primary\" onClick={handleAddUser}>添加用户</Button>}\n          >\n            <Table \n              loading={loading}\n              dataSource={users} \n              columns={columns} \n              rowKey=\"id\"\n            />\n          </Card>\n        );\n      case 'devices':\n        return <DeviceManagement id=\"device-management\" />;\n      case 'intersections':\n        return (\n          <Card title=\"路口管理\">\n            <Table \n              dataSource={intersections}\n              columns={intersectionColumns}\n              rowKey=\"id\"\n            />\n          </Card>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <SystemContainer>\n      <TabsContainer>\n        <TabButton \n          active={activeTab === 'users'} \n          onClick={() => handleTabChange('users')}\n        >\n          用户管理\n        </TabButton>\n        <TabButton \n          active={activeTab === 'devices'} \n          onClick={() => handleTabChange('devices')}\n        >\n          设备管理\n        </TabButton>\n        <TabButton \n          active={activeTab === 'intersections'} \n          onClick={() => handleTabChange('intersections')}\n        >\n          路口管理\n        </TabButton>\n      </TabsContainer>\n      \n      <ContentArea>\n        {renderContent()}\n      </ContentArea>\n      \n      <Modal\n        title={editingUser ? \"编辑用户\" : \"添加用户\"}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            role: 'user'\n          }}\n        >\n          <Form.Item\n            name=\"username\"\n            label=\"用户名\"\n            rules={[{ required: true, message: '请输入用户名' }]}\n          >\n            <Input prefix={<UserOutlined />} placeholder=\"用户名\" />\n          </Form.Item>\n          \n          {!editingUser && (\n            <Form.Item\n              name=\"password\"\n              label=\"密码\"\n              rules={[\n                { required: true, message: '请输入密码' },\n                { min: 6, message: '密码长度至少为6个字符' }\n              ]}\n              extra=\"密码长度至少为6个字符\"\n            >\n              <Input.Password prefix={<LockOutlined />} placeholder=\"密码\" />\n            </Form.Item>\n          )}\n          \n          <Form.Item\n            name=\"role\"\n            label=\"角色\"\n            rules={[{ required: true, message: '请选择角色' }]}\n          >\n            <Select placeholder=\"选择角色\">\n              <Option value=\"admin\">管理员</Option>\n              <Option value=\"monitor\">监控人员</Option>\n              <Option value=\"user\">普通用户</Option>\n              <Option value=\"maintenance\">设备维护人员</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"email\"\n            label=\"邮箱\"\n            rules={[\n              { type: 'email', message: '请输入有效的邮箱地址' }\n            ]}\n          >\n            <Input prefix={<MailOutlined />} placeholder=\"邮箱（选填）\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n      \n      {/* 添加路口管理 Modal */}\n      <Modal\n        title=\"编辑路口信息\"\n        open={intersectionModalVisible}\n        onOk={handleIntersectionModalOk}\n        onCancel={() => setIntersectionModalVisible(false)}\n      >\n        <Form\n          form={intersectionForm}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"路口名称\"\n          >\n            <Input disabled />\n          </Form.Item>\n          <Form.Item\n            name=\"latitude\"\n            label=\"纬度\"\n            rules={[\n              { required: true, message: '请输入纬度' },\n              {\n                pattern: /^-?([0-8]?[0-9]|90)(\\.[0-9]{1,6})?$/,\n                message: '请输入有效的纬度值 (-90 到 90)',\n              },\n            ]}\n          >\n            <Input placeholder=\"请输入纬度\" />\n          </Form.Item>\n          <Form.Item\n            name=\"longitude\"\n            label=\"经度\"\n            rules={[\n              { required: true, message: '请输入经度' },\n              {\n                pattern: /^-?((1?[0-7]?|[0-9]?)[0-9]|180)(\\.[0-9]{1,6})?$/,\n                message: '请输入有效的经度值 (-180 到 180)',\n              },\n            ]}\n          >\n            <Input placeholder=\"请输入经度\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </SystemContainer>\n  );\n};\n\nexport default SystemManagement;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,QAAQ,MAAM;AAC/E,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC5E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,iBAAiB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAM;EAAEC;AAAO,CAAC,GAAGb,MAAM;;AAEzB;AACA,MAAMc,eAAe,GAAGN,MAAM,CAACO,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,eAAe;AAOrB,MAAMG,aAAa,GAAGT,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,aAAa;AAUnB,MAAME,SAAS,GAAGX,MAAM,CAACY,MAAM;AAC/B;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D;AACA,6BAA6BD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,aAAa;AAC9E,WAAWD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,qBAAqB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIJ,SAAS;AAqBf,MAAMK,WAAW,GAAGhB,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GALID,WAAW;AAOjB,MAAME,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyC,IAAI,CAAC,GAAGpC,IAAI,CAACqC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACmD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACqD,gBAAgB,CAAC,GAAGhD,IAAI,CAACqC,OAAO,CAAC,CAAC;;EAEzC;EACA,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChBmB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAE1B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CJ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,KAAK,CAAC;;MAEjC;MACA,MAAMG,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMnD,KAAK,CAACoD,GAAG,CAAC,GAAGL,MAAM,YAAY,EAAE;QACtDM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUT,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEQ,QAAQ,CAACG,IAAI,CAAC;MAEpC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QAC1C9B,QAAQ,CAAC0B,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACpC,CAAC,MAAM;QAAA,IAAAE,cAAA;QACL7D,OAAO,CAAC8D,OAAO,CAAC,YAAY,IAAI,EAAAD,cAAA,GAAAL,QAAQ,CAACG,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAe7D,OAAO,KAAI,MAAM,CAAC,CAAC;MACpE;IACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC/D,OAAO,CAAC+D,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAC/D,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACR4B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDnC,SAAS,CAAC,MAAM;IACdqD,UAAU,CAAC,CAAC;IACZ;IACA,MAAMkB,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACzD,WAAW,CAAC0D,OAAO,CAACC,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;;IAExF;IACA,MAAMC,mBAAmB,GAAGN,eAAe,CAACG,GAAG,CAACE,QAAQ,IAAI;MAC1D,MAAME,oBAAoB,GAAG9D,iBAAiB,CAAC8B,aAAa,CAACiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKL,QAAQ,CAAC;MAC3F,OAAOE,oBAAoB,IAAI;QAC7BI,EAAE,EAAE,gBAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3EP,IAAI,EAAEL,QAAQ;QACda,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACpC,CAAC;IACH,CAAC,CAAC;IAEF7C,gBAAgB,CAAC8B,mBAAmB,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgB,eAAe,GAAIC,GAAG,IAAK;IAC/BxC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuC,GAAG,CAAC;IAC1BjD,YAAY,CAACiD,GAAG,CAAC;;IAEjB;IACA,IAAIA,GAAG,KAAK,SAAS,EAAE;MACrB,MAAMC,mBAAmB,GAAGC,QAAQ,CAACC,aAAa,CAAC,oBAAoB,CAAC;MACxE,IAAIF,mBAAmB,EAAE;QACvBA,mBAAmB,CAACG,YAAY,CAAC,CAAC;MACpC;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BxD,cAAc,CAAC,IAAI,CAAC;IACpBH,IAAI,CAAC4D,WAAW,CAAC,CAAC;IAClB7D,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM8D,cAAc,GAAIC,IAAI,IAAK;IAC/B3D,cAAc,CAAC2D,IAAI,CAAC;IACpB9D,IAAI,CAAC+D,cAAc,CAAC;MAClBC,QAAQ,EAAEF,IAAI,CAACE,QAAQ;MACvBC,IAAI,EAAEH,IAAI,CAACG,IAAI;MACfC,KAAK,EAAEJ,IAAI,CAACI;IACd,CAAC,CAAC;IACFnE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMoE,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI;MACF,MAAMpD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAI;QACF,MAAMlD,KAAK,CAACiG,MAAM,CAAC,GAAGlD,MAAM,cAAciD,MAAM,EAAE,EAAE;UAClD3C,OAAO,EAAE;YACP,eAAe,EAAE,UAAUT,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACFjD,OAAO,CAAC4D,OAAO,CAAC,OAAO,CAAC;QACxBd,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;QAElC;QACA,MAAMwC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACvD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QACzE,MAAMuD,YAAY,GAAGH,UAAU,CAACI,MAAM,CAACZ,IAAI,IAAIA,IAAI,CAACpB,EAAE,KAAK0B,MAAM,CAAC;QAClEnD,YAAY,CAAC0D,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACH,YAAY,CAAC,CAAC;QAEhE1G,OAAO,CAAC4D,OAAO,CAAC,aAAa,CAAC;QAC9Bd,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/D,OAAO,CAAC+D,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAC/D,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;EAED,MAAM8G,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM9E,IAAI,CAAC+E,cAAc,CAAC,CAAC;MAC1CjE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE+D,MAAM,CAAC;MAE5B,MAAM9D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAI;QACF,IAAIC,QAAQ;QAEZ,IAAIrB,WAAW,EAAE;UACf;UACAqB,QAAQ,GAAG,MAAMnD,KAAK,CAAC4G,GAAG,CAAC,GAAG7D,MAAM,cAAcjB,WAAW,CAACwC,EAAE,EAAE,EAAEoC,MAAM,EAAE;YAC1ErD,OAAO,EAAE;cACP,eAAe,EAAE,UAAUT,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAO,QAAQ,GAAG,MAAMnD,KAAK,CAAC6G,IAAI,CAAC,GAAG9D,MAAM,YAAY,EAAE2D,MAAM,EAAE;YACzDrD,OAAO,EAAE;cACP,eAAe,EAAE,UAAUT,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ;QAEAF,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEQ,QAAQ,CAACG,IAAI,CAAC;QAEpC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C5D,OAAO,CAAC4D,OAAO,CAACzB,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC;UAChDH,eAAe,CAAC,KAAK,CAAC;UACtBC,IAAI,CAAC4D,WAAW,CAAC,CAAC;UAClB/C,UAAU,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UAAA,IAAAqE,eAAA;UACLnH,OAAO,CAAC+D,KAAK,CAAC,EAAAoD,eAAA,GAAA3D,QAAQ,CAACG,IAAI,cAAAwD,eAAA,uBAAbA,eAAA,CAAenH,OAAO,KAAI,MAAM,CAAC;QACjD;MACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;QAElC;QACA,MAAMwC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACvD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QAEzE,IAAIhB,WAAW,EAAE;UACf;UACA,MAAMuE,YAAY,GAAGH,UAAU,CAACpC,GAAG,CAAC4B,IAAI,IACtCA,IAAI,CAACpB,EAAE,KAAKxC,WAAW,CAACwC,EAAE,GAAG;YAAE,GAAGoB,IAAI;YAAE,GAAGgB;UAAO,CAAC,GAAGhB,IACxD,CAAC;UACD7C,YAAY,CAAC0D,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACH,YAAY,CAAC,CAAC;QAClE,CAAC,MAAM;UACL;UACA,MAAMU,OAAO,GAAG;YACd,GAAGL,MAAM;YACTpC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC;YACzBI,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UACpC,CAAC;UACDkB,UAAU,CAACc,IAAI,CAACD,OAAO,CAAC;UACxBlE,YAAY,CAAC0D,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACN,UAAU,CAAC,CAAC;QAChE;QAEAvG,OAAO,CAAC4D,OAAO,CAACzB,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC;QAC5DH,eAAe,CAAC,KAAK,CAAC;QACtBC,IAAI,CAAC4D,WAAW,CAAC,CAAC;QAClB/C,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/D,OAAO,CAAC+D,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAMuD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BtF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMuF,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGzB,IAAI,IAAK;MAChB,MAAM0B,OAAO,GAAG;QACd,OAAO,EAAE,KAAK;QACd,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,MAAM;QACd,aAAa,EAAE;MACjB,CAAC;MACD,OAAOA,OAAO,CAAC1B,IAAI,CAAC,IAAIA,IAAI;IAC9B;EACF,CAAC,EACD;IACEsB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAEE,IAAI,IAAIA,IAAI,GAAG,IAAIjD,IAAI,CAACiD,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC3D,CAAC,EACD;IACEN,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChBrH,OAAA;MAAAsH,QAAA,gBACEtH,OAAA,CAAChB,MAAM;QAACuI,IAAI,EAAC,MAAM;QAACC,OAAO,EAAEA,CAAA,KAAMrC,cAAc,CAACkC,MAAM,CAAE;QAAAC,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtE5H,OAAA,CAAChB,MAAM;QAACuI,IAAI,EAAC,MAAM;QAACM,MAAM;QAACL,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC4B,MAAM,CAACrD,EAAE,CAAE;QAAAsD,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAET,CAAC,CACF;;EAED;EACA,MAAME,sBAAsB,GAAIC,YAAY,IAAK;IAC/C9F,sBAAsB,CAAC8F,YAAY,CAAC;IACpC7F,gBAAgB,CAACmD,cAAc,CAAC0C,YAAY,CAAC;IAC7ChG,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMiG,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACF,MAAM5B,MAAM,GAAG,MAAMlE,gBAAgB,CAACmE,cAAc,CAAC,CAAC;MACtD,MAAM4B,oBAAoB,GAAGrG,aAAa,CAAC4B,GAAG,CAACuE,YAAY,IACzDA,YAAY,CAAC/D,EAAE,KAAKhC,mBAAmB,CAACgC,EAAE,GACtC;QAAE,GAAG+D,YAAY;QAAE,GAAG3B;MAAO,CAAC,GAC9B2B,YACN,CAAC;MACDlG,gBAAgB,CAACoG,oBAAoB,CAAC;MACtClG,2BAA2B,CAAC,KAAK,CAAC;MAClC1C,OAAO,CAAC4D,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM8E,mBAAmB,GAAG,CAC1B;IACErB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChBrH,OAAA,CAAChB,MAAM;MAACuI,IAAI,EAAC,MAAM;MAACC,OAAO,EAAEA,CAAA,KAAMM,sBAAsB,CAACT,MAAM,CAAE;MAAAC,QAAA,EAAC;IAEnE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAEZ,CAAC,CACF;;EAED;EACA,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQzG,SAAS;MACf,KAAK,OAAO;QACV,oBACE1B,OAAA,CAACV,IAAI;UACHuH,KAAK,EAAC,0BAAM;UACZuB,KAAK,eAAEpI,OAAA,CAAChB,MAAM;YAACuI,IAAI,EAAC,SAAS;YAACC,OAAO,EAAEvC,aAAc;YAAAqC,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAE;UAAAN,QAAA,eAEpEtH,OAAA,CAACjB,KAAK;YACJiC,OAAO,EAAEA,OAAQ;YACjBqH,UAAU,EAAEnH,KAAM;YAClB0F,OAAO,EAAEA,OAAQ;YACjB0B,MAAM,EAAC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEX,KAAK,SAAS;QACZ,oBAAO5H,OAAA,CAACL,gBAAgB;UAACqE,EAAE,EAAC;QAAmB;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,eAAe;QAClB,oBACE5H,OAAA,CAACV,IAAI;UAACuH,KAAK,EAAC,0BAAM;UAAAS,QAAA,eAChBtH,OAAA,CAACjB,KAAK;YACJsJ,UAAU,EAAEzG,aAAc;YAC1BgF,OAAO,EAAEsB,mBAAoB;YAC7BI,MAAM,EAAC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEX;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE5H,OAAA,CAACE,eAAe;IAAAoH,QAAA,gBACdtH,OAAA,CAACK,aAAa;MAAAiH,QAAA,gBACZtH,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEgB,SAAS,KAAK,OAAQ;QAC9B8F,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,OAAO,CAAE;QAAA2C,QAAA,EACzC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZ5H,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEgB,SAAS,KAAK,SAAU;QAChC8F,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,SAAS,CAAE;QAAA2C,QAAA,EAC3C;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZ5H,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEgB,SAAS,KAAK,eAAgB;QACtC8F,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,eAAe,CAAE;QAAA2C,QAAA,EACjD;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEhB5H,OAAA,CAACY,WAAW;MAAA0G,QAAA,EACTa,aAAa,CAAC;IAAC;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEd5H,OAAA,CAACf,KAAK;MACJ4H,KAAK,EAAErF,WAAW,GAAG,MAAM,GAAG,MAAO;MACrC+G,IAAI,EAAEnH,YAAa;MACnBoH,IAAI,EAAErC,aAAc;MACpBsC,QAAQ,EAAE9B,iBAAkB;MAC5B+B,cAAc;MAAApB,QAAA,eAEdtH,OAAA,CAACd,IAAI;QACHoC,IAAI,EAAEA,IAAK;QACXqH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbrD,IAAI,EAAE;QACR,CAAE;QAAA+B,QAAA,gBAEFtH,OAAA,CAACd,IAAI,CAAC2J,IAAI;UACR9E,IAAI,EAAC,UAAU;UACf+E,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE3J,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAiI,QAAA,eAE/CtH,OAAA,CAACb,KAAK;YAAC8J,MAAM,eAAEjJ,OAAA,CAACT,YAAY;cAAAkI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACsB,WAAW,EAAC;UAAK;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,EAEX,CAACpG,WAAW,iBACXxB,OAAA,CAACd,IAAI,CAAC2J,IAAI;UACR9E,IAAI,EAAC,UAAU;UACf+E,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE3J,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAE8J,GAAG,EAAE,CAAC;YAAE9J,OAAO,EAAE;UAAc,CAAC,CAClC;UACF+I,KAAK,EAAC,+DAAa;UAAAd,QAAA,eAEnBtH,OAAA,CAACb,KAAK,CAACiK,QAAQ;YAACH,MAAM,eAAEjJ,OAAA,CAACR,YAAY;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACsB,WAAW,EAAC;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACZ,eAED5H,OAAA,CAACd,IAAI,CAAC2J,IAAI;UACR9E,IAAI,EAAC,MAAM;UACX+E,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE3J,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAiI,QAAA,eAE9CtH,OAAA,CAACZ,MAAM;YAAC8J,WAAW,EAAC,0BAAM;YAAA5B,QAAA,gBACxBtH,OAAA,CAACC,MAAM;cAACoJ,KAAK,EAAC,OAAO;cAAA/B,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC5H,OAAA,CAACC,MAAM;cAACoJ,KAAK,EAAC,SAAS;cAAA/B,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC5H,OAAA,CAACC,MAAM;cAACoJ,KAAK,EAAC,MAAM;cAAA/B,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC5H,OAAA,CAACC,MAAM;cAACoJ,KAAK,EAAC,aAAa;cAAA/B,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ5H,OAAA,CAACd,IAAI,CAAC2J,IAAI;UACR9E,IAAI,EAAC,OAAO;UACZ+E,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAExB,IAAI,EAAE,OAAO;YAAElI,OAAO,EAAE;UAAa,CAAC,CACxC;UAAAiI,QAAA,eAEFtH,OAAA,CAACb,KAAK;YAAC8J,MAAM,eAAEjJ,OAAA,CAACP,YAAY;cAAAgI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACsB,WAAW,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR5H,OAAA,CAACf,KAAK;MACJ4H,KAAK,EAAC,sCAAQ;MACd0B,IAAI,EAAEzG,wBAAyB;MAC/B0G,IAAI,EAAER,yBAA0B;MAChCS,QAAQ,EAAEA,CAAA,KAAM1G,2BAA2B,CAAC,KAAK,CAAE;MAAAuF,QAAA,eAEnDtH,OAAA,CAACd,IAAI;QACHoC,IAAI,EAAEY,gBAAiB;QACvByG,MAAM,EAAC,UAAU;QAAArB,QAAA,gBAEjBtH,OAAA,CAACd,IAAI,CAAC2J,IAAI;UACR9E,IAAI,EAAC,MAAM;UACX+E,KAAK,EAAC,0BAAM;UAAAxB,QAAA,eAEZtH,OAAA,CAACb,KAAK;YAACmK,QAAQ;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACZ5H,OAAA,CAACd,IAAI,CAAC2J,IAAI;UACR9E,IAAI,EAAC,UAAU;UACf+E,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE3J,OAAO,EAAE;UAAQ,CAAC,EACpC;YACEkK,OAAO,EAAE,qCAAqC;YAC9ClK,OAAO,EAAE;UACX,CAAC,CACD;UAAAiI,QAAA,eAEFtH,OAAA,CAACb,KAAK;YAAC+J,WAAW,EAAC;UAAO;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACZ5H,OAAA,CAACd,IAAI,CAAC2J,IAAI;UACR9E,IAAI,EAAC,WAAW;UAChB+E,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE3J,OAAO,EAAE;UAAQ,CAAC,EACpC;YACEkK,OAAO,EAAE,iDAAiD;YAC1DlK,OAAO,EAAE;UACX,CAAC,CACD;UAAAiI,QAAA,eAEFtH,OAAA,CAACb,KAAK;YAAC+J,WAAW,EAAC;UAAO;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEtB,CAAC;AAAC7G,EAAA,CAxdID,gBAAgB;EAAA,QAIL5B,IAAI,CAACqC,OAAO,EAMArC,IAAI,CAACqC,OAAO;AAAA;AAAAiI,GAAA,GAVnC1I,gBAAgB;AA0dtB,eAAeA,gBAAgB;AAAC,IAAAV,EAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAA2I,GAAA;AAAAC,YAAA,CAAArJ,EAAA;AAAAqJ,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAA5I,GAAA;AAAA4I,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}