{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\VideoPlayer.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport flvjs from 'flv.js';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst flv_BASE_URL = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\nconst FullscreenButton = ({\n  onClick\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  onClick: onClick,\n  style: {\n    position: 'absolute',\n    bottom: '10px',\n    right: '10px',\n    padding: '6px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 10,\n    transition: 'opacity 0.3s',\n    opacity: 0.6,\n    color: '#fff',\n    width: '24px',\n    height: '24px'\n  },\n  onMouseEnter: e => e.currentTarget.style.opacity = 1,\n  onMouseLeave: e => e.currentTarget.style.opacity = 0.6,\n  title: \"\\u5168\\u5C4F\",\n  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    style: {\n      width: '16px',\n      height: '16px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M3 5v4h2V5h4V3H5c-1.1 0-2 .9-2 2zm2 10H3v4c0 1.1.9 2 2 2h4v-2H5v-4zm14 4h-4v2h4c1.1 0 2-.9 2-2v-4h-2v4zm0-16h-4v2h4v4h2V5c0-1.1-.9-2-2-2z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 6,\n  columnNumber: 3\n}, this);\n_c = FullscreenButton;\nconst RefreshButton = ({\n  onClick\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  onClick: onClick,\n  style: {\n    position: 'absolute',\n    bottom: '10px',\n    right: '44px',\n    padding: '6px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 10,\n    transition: 'opacity 0.3s',\n    opacity: 0.6,\n    color: '#fff',\n    width: '24px',\n    height: '24px'\n  },\n  onMouseEnter: e => e.currentTarget.style.opacity = 1,\n  onMouseLeave: e => e.currentTarget.style.opacity = 0.6,\n  title: \"\\u5237\\u65B0\\u89C6\\u9891\\u6D41\",\n  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    style: {\n      width: '16px',\n      height: '16px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 37,\n  columnNumber: 3\n}, this);\n_c2 = RefreshButton;\nconst VideoPlayer = ({\n  deviceId,\n  rtspUrl,\n  flvUrl\n}) => {\n  _s();\n  const videoRef = useRef(null);\n  const containerRef = useRef(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [hasRtspUrl, setHasRtspUrl] = useState(!!rtspUrl);\n  const [debugInfo, setDebugInfo] = useState('');\n  const playerRef = useRef(null);\n  const handleFullscreen = () => {\n    const container = containerRef.current;\n    if (!container) return;\n    if (!document.fullscreenElement) {\n      if (container.requestFullscreen) {\n        container.requestFullscreen().catch(e => console.warn('进入全屏失败:', e));\n      } else if (container.webkitRequestFullscreen) {\n        container.webkitRequestFullscreen();\n      } else if (container.msRequestFullscreen) {\n        container.msRequestFullscreen();\n      }\n    } else {\n      if (document.exitFullscreen) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen().catch(e => console.warn('退出全屏失败:', e));\n        }\n      } else if (document.webkitExitFullscreen) {\n        if (document.webkitFullscreenElement) {\n          document.webkitExitFullscreen();\n        }\n      } else if (document.msExitFullscreen) {\n        if (document.msFullscreenElement) {\n          document.msExitFullscreen();\n        }\n      }\n    }\n  };\n  const handleDoubleClick = () => {\n    const container = containerRef.current;\n    if (!container) return;\n    if (!document.fullscreenElement) {\n      if (container.requestFullscreen) {\n        container.requestFullscreen().catch(e => console.warn('进入全屏失败:', e));\n      } else if (container.webkitRequestFullscreen) {\n        container.webkitRequestFullscreen();\n      } else if (container.msRequestFullscreen) {\n        container.msRequestFullscreen();\n      }\n    } else {\n      if (document.exitFullscreen) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen().catch(e => console.warn('退出全屏失败:', e));\n        }\n      } else if (document.webkitExitFullscreen) {\n        if (document.webkitFullscreenElement) {\n          document.webkitExitFullscreen();\n        }\n      } else if (document.msExitFullscreen) {\n        if (document.msFullscreenElement) {\n          document.msExitFullscreen();\n        }\n      }\n    }\n  };\n\n  // 手动刷新视频流\n  const [refreshKey, setRefreshKey] = useState(0);\n  const handleRefresh = () => {\n    console.log('手动刷新视频流');\n    setRefreshKey(prev => prev + 1); // 触发 useEffect 重新执行\n  };\n  useEffect(() => {\n    if (!rtspUrl) {\n      setHasRtspUrl(false);\n      setLoading(false);\n      return;\n    }\n    setHasRtspUrl(true);\n    setLoading(true);\n    setError(null);\n    let retryCount = 0;\n    const maxRetries = 10;\n    let retryTimeout = null;\n    let healthCheckInterval = null;\n    let isDestroyed = false;\n\n    // 重连延迟计算（指数退避）\n    const getRetryDelay = count => {\n      return Math.min(1000 * Math.pow(1.5, count), 10000); // 最大10秒\n    };\n\n    // 检查流状态\n    const checkStreamHealth = async () => {\n      if (isDestroyed || !playerRef.current) return;\n      try {\n        // 首先检查后端流状态API\n        const apiResponse = await fetch('http://localhost:8001/api/streams', {\n          method: 'GET',\n          cache: 'no-cache'\n        });\n        if (apiResponse.ok) {\n          const streams = await apiResponse.json();\n          const streamPath = `/live/${deviceId}`;\n          const streamInfo = streams[streamPath];\n          if (!streamInfo || !streamInfo.publisher) {\n            console.warn(`后端流状态检查: 流 ${deviceId} 不可用`);\n            if (playerRef.current && !isDestroyed) {\n              handlePlayerError('BACKEND_STREAM_UNAVAILABLE', 'Backend reports stream unavailable');\n            }\n            return;\n          } else {\n            console.log(`后端流状态检查: 流 ${deviceId} 可用`);\n          }\n        }\n\n        // 然后检查FLV流的HTTP状态\n        const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n        const response = await fetch(url, {\n          method: 'HEAD',\n          cache: 'no-cache',\n          timeout: 5000\n        });\n        if (!response.ok) {\n          console.warn(`FLV流健康检查失败: ${response.status}`);\n          if (playerRef.current && !isDestroyed) {\n            handlePlayerError('FLV_STREAM_UNAVAILABLE', 'FLV stream health check failed');\n          }\n        } else {\n          console.log(`FLV流健康检查: 流 ${deviceId} 可用`);\n        }\n      } catch (error) {\n        console.warn('流健康检查异常:', error);\n        // 网络错误时不立即重连，可能是临时网络问题\n      }\n    };\n\n    // 处理播放器错误和重连\n    const handlePlayerError = (errorType, errorDetail) => {\n      if (isDestroyed) return;\n      console.error('播放器错误:', errorType, errorDetail);\n      setDebugInfo(`播放器错误: ${errorType}`);\n      if (retryCount < maxRetries) {\n        retryCount++;\n        const delay = getRetryDelay(retryCount);\n        console.log(`准备重试播放器 (${retryCount}/${maxRetries})，延迟 ${delay}ms`);\n        setDebugInfo(`重连中... (${retryCount}/${maxRetries})`);\n        retryTimeout = setTimeout(() => {\n          if (!isDestroyed) {\n            console.log('重新初始化播放器...');\n            const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n            initializePlayer(url);\n          }\n        }, delay);\n      } else {\n        console.error('播放器重试次数超过限制');\n        setError(`连接失败 (已重试${maxRetries}次)`);\n        setLoading(false);\n      }\n    };\n    const loadVideo = () => {\n      if (flvjs.isSupported()) {\n        const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n        setDebugInfo(`正在检查流状态...`);\n        console.log('开始初始化播放器...');\n        initializePlayer(url);\n        return;\n      } else {\n        setError('您的浏览器不支持FLV视频流');\n        setLoading(false);\n      }\n    };\n    const initializePlayer = flvUrl => {\n      if (isDestroyed) return;\n      console.log('开始初始化 FLV 播放器');\n\n      // 清理旧的播放器\n      if (playerRef.current) {\n        try {\n          // 清理流监控定时器\n          if (playerRef.current._streamMonitorInterval) {\n            clearInterval(playerRef.current._streamMonitorInterval);\n            playerRef.current._streamMonitorInterval = null;\n          }\n          playerRef.current.pause();\n          playerRef.current.unload();\n          playerRef.current.detachMediaElement();\n          playerRef.current.destroy();\n        } catch (e) {\n          console.warn('清理旧播放器时出错:', e);\n        }\n        playerRef.current = null;\n      }\n\n      // 清理健康检查定时器\n      if (healthCheckInterval) {\n        clearInterval(healthCheckInterval);\n        healthCheckInterval = null;\n      }\n      const player = flvjs.createPlayer({\n        type: 'flv',\n        url: flvUrl,\n        isLive: true,\n        hasAudio: false,\n        hasVideo: true,\n        cors: true,\n        enableStashBuffer: false,\n        stashInitialSize: 128,\n        lazyLoad: false,\n        lazyLoadMaxDuration: 0,\n        fixAudioTimestampGap: false,\n        seekType: 'range',\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache',\n          'If-None-Match': '*'\n        }\n      });\n\n      // 错误处理\n      player.on(flvjs.Events.ERROR, (errorType, errorDetail) => {\n        if (isDestroyed) return;\n        handlePlayerError(errorType, errorDetail);\n      });\n\n      // 媒体信息事件\n      player.on(flvjs.Events.MEDIA_INFO, mediaInfo => {\n        if (isDestroyed) return;\n        console.log('媒体信息:', mediaInfo);\n        setDebugInfo('获取到媒体信息');\n\n        // 重置重试计数\n        retryCount = 0;\n\n        // 启动健康检查\n        if (!healthCheckInterval) {\n          healthCheckInterval = setInterval(checkStreamHealth, 15000); // 每15秒检查一次\n        }\n\n        // 启动流状态监控（更频繁的检查）\n        const streamMonitorInterval = setInterval(async () => {\n          if (isDestroyed) return;\n          try {\n            const apiResponse = await fetch('http://localhost:8001/api/streams', {\n              method: 'GET',\n              cache: 'no-cache'\n            });\n            if (apiResponse.ok) {\n              const streams = await apiResponse.json();\n              const streamPath = `/live/${deviceId}`;\n              const streamInfo = streams[streamPath];\n\n              // 如果后端显示流可用，但前端播放器有问题，尝试重连\n              if (streamInfo && streamInfo.publisher && playerRef.current) {\n                const video = videoRef.current;\n                if (video && (video.paused || video.ended || video.readyState < 2)) {\n                  console.log('检测到流状态不一致，尝试重新加载播放器...');\n                  try {\n                    playerRef.current.unload();\n                    playerRef.current.load();\n                    playerRef.current.play();\n                  } catch (e) {\n                    console.warn('重新加载播放器失败:', e);\n                  }\n                }\n              }\n            }\n          } catch (error) {\n            console.warn('流状态监控异常:', error);\n          }\n        }, 10000); // 每10秒检查一次\n\n        // 保存监控定时器引用\n        player._streamMonitorInterval = streamMonitorInterval;\n      });\n\n      // 统计信息事件\n      player.on(flvjs.Events.STATISTICS_INFO, stats => {\n        if (isDestroyed) return;\n        console.log('播放统计:', stats);\n      });\n\n      // 加载开始事件\n      player.on(flvjs.Events.LOADING_COMPLETE, () => {\n        if (isDestroyed) return;\n        console.log('流加载完成');\n        setDebugInfo('流加载完成');\n      });\n\n      // 缓冲区事件\n      player.on(flvjs.Events.RECOVERED_EARLY_EOF, () => {\n        if (isDestroyed) return;\n        console.log('从早期EOF恢复');\n      });\n      try {\n        if (isDestroyed || !videoRef.current) return;\n        console.log('正在加载视频元素');\n        player.attachMediaElement(videoRef.current);\n\n        // 视频事件监听\n        const onPlaying = () => {\n          if (isDestroyed) return;\n          console.log('视频开始播放');\n          setLoading(false);\n          setError(null);\n          setDebugInfo('播放中...');\n          retryCount = 0; // 重置重试计数\n        };\n        const onWaiting = () => {\n          if (isDestroyed) return;\n          console.log('视频缓冲中...');\n          setDebugInfo('缓冲中...');\n        };\n        const onError = e => {\n          if (isDestroyed) return;\n          console.error('视频元素错误:', e);\n          handlePlayerError('VIDEO_ERROR', e.message || 'Video element error');\n        };\n        const onLoadStart = () => {\n          if (isDestroyed) return;\n          setDebugInfo('开始加载...');\n        };\n        videoRef.current.addEventListener('playing', onPlaying);\n        videoRef.current.addEventListener('waiting', onWaiting);\n        videoRef.current.addEventListener('error', onError);\n        videoRef.current.addEventListener('loadstart', onLoadStart);\n\n        // 保存事件监听器引用以便清理\n        player._videoEventListeners = {\n          playing: onPlaying,\n          waiting: onWaiting,\n          error: onError,\n          loadstart: onLoadStart\n        };\n        player.load();\n        playerRef.current = player;\n        player.play().catch(e => {\n          if (isDestroyed) return;\n          console.error('播放失败:', e);\n          handlePlayerError('PLAY_FAILED', e.message);\n        });\n      } catch (e) {\n        if (isDestroyed) return;\n        console.error('播放器初始化失败:', e);\n        handlePlayerError('INIT_FAILED', e.message);\n      }\n    };\n    loadVideo();\n    return () => {\n      isDestroyed = true;\n\n      // 清理重试定时器\n      if (retryTimeout) {\n        clearTimeout(retryTimeout);\n        retryTimeout = null;\n      }\n\n      // 清理健康检查定时器\n      if (healthCheckInterval) {\n        clearInterval(healthCheckInterval);\n        healthCheckInterval = null;\n      }\n\n      // 清理播放器\n      if (playerRef.current) {\n        try {\n          // 清理流监控定时器\n          if (playerRef.current._streamMonitorInterval) {\n            clearInterval(playerRef.current._streamMonitorInterval);\n            playerRef.current._streamMonitorInterval = null;\n          }\n\n          // 清理视频事件监听器\n          if (playerRef.current._videoEventListeners && videoRef.current) {\n            const listeners = playerRef.current._videoEventListeners;\n            videoRef.current.removeEventListener('playing', listeners.playing);\n            videoRef.current.removeEventListener('waiting', listeners.waiting);\n            videoRef.current.removeEventListener('error', listeners.error);\n            videoRef.current.removeEventListener('loadstart', listeners.loadstart);\n          }\n          playerRef.current.pause();\n          playerRef.current.unload();\n          playerRef.current.detachMediaElement();\n          playerRef.current.destroy();\n        } catch (e) {\n          console.warn('清理播放器时出错:', e);\n        }\n        playerRef.current = null;\n      }\n    };\n  }, [deviceId, rtspUrl, flvUrl, refreshKey]);\n  if (!hasRtspUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#000',\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      onDoubleClick: handleDoubleClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u89C6\\u9891\\u6D41\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: \"(\\u8BE5\\u6444\\u50CF\\u5934\\u672A\\u914D\\u7F6ERTSP\\u5730\\u5740)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n        onClick: handleFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RefreshButton, {\n        onClick: handleRefresh\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#000',\n        position: 'relative'\n      },\n      onDoubleClick: handleDoubleClick,\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        controls: false,\n        autoPlay: true,\n        muted: true,\n        playsInline: true,\n        style: {\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover'\n        },\n        onDoubleClick: handleDoubleClick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n        onClick: handleFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RefreshButton, {\n        onClick: handleRefresh\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          color: '#fff',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u89C6\\u9891\\u6D41...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '11px',\n            marginTop: '4px'\n          },\n          children: debugInfo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#000',\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      onDoubleClick: handleDoubleClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u89C6\\u9891\\u6D41\\u9519\\u8BEF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: [\"(\", error, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n        onClick: handleFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RefreshButton, {\n        onClick: handleRefresh\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    style: {\n      width: '100%',\n      height: '100%',\n      backgroundColor: '#000',\n      position: 'relative'\n    },\n    onDoubleClick: handleDoubleClick,\n    children: [/*#__PURE__*/_jsxDEV(\"video\", {\n      ref: videoRef,\n      controls: false,\n      autoPlay: true,\n      muted: true,\n      playsInline: true,\n      style: {\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      },\n      onDoubleClick: handleDoubleClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n      onClick: handleFullscreen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RefreshButton, {\n      onClick: handleRefresh\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '50%',\n        left: '50%',\n        transform: 'translate(-50%, -50%)',\n        color: '#fff',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u89C6\\u9891\\u6D41...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: debugInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 585,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlayer, \"VczM7BBMaeV2P3ImB7OnmG6IMCc=\");\n_c3 = VideoPlayer;\nexport default VideoPlayer;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"FullscreenButton\");\n$RefreshReg$(_c2, \"RefreshButton\");\n$RefreshReg$(_c3, \"VideoPlayer\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "flvjs", "jsxDEV", "_jsxDEV", "flv_BASE_URL", "process", "env", "REACT_APP_FLV_URL", "FullscreenButton", "onClick", "style", "position", "bottom", "right", "padding", "backgroundColor", "borderRadius", "cursor", "display", "alignItems", "justifyContent", "zIndex", "transition", "opacity", "color", "width", "height", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "title", "children", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RefreshButton", "_c2", "VideoPlayer", "deviceId", "rtspUrl", "flvUrl", "_s", "videoRef", "containerRef", "error", "setError", "loading", "setLoading", "hasRtspUrl", "setHasRtspUrl", "debugInfo", "setDebugInfo", "playerRef", "handleFullscreen", "container", "current", "document", "fullscreenElement", "requestFullscreen", "catch", "console", "warn", "webkitRequestFullscreen", "msRequestFullscreen", "exitFullscreen", "webkitExitFullscreen", "webkitFullscreenElement", "msExitFullscreen", "msFullscreenElement", "handleDoubleClick", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "handleRefresh", "log", "prev", "retryCount", "maxRetries", "retryTimeout", "healthCheckInterval", "isDestroyed", "getRetryDelay", "count", "Math", "min", "pow", "checkStreamHealth", "apiResponse", "fetch", "method", "cache", "ok", "streams", "json", "streamPath", "streamInfo", "publisher", "handlePlayerError", "url", "response", "timeout", "status", "errorType", "errorDetail", "delay", "setTimeout", "initializePlayer", "loadVideo", "isSupported", "_streamMonitorInterval", "clearInterval", "pause", "unload", "detachMediaElement", "destroy", "player", "createPlayer", "type", "isLive", "hasAudio", "hasVideo", "cors", "enableStashBuffer", "stashInitialSize", "lazyLoad", "lazyLoadMaxDuration", "fixAudioTimestampGap", "seekType", "headers", "on", "Events", "ERROR", "MEDIA_INFO", "mediaInfo", "setInterval", "streamMonitorInterval", "video", "paused", "ended", "readyState", "load", "play", "STATISTICS_INFO", "stats", "LOADING_COMPLETE", "RECOVERED_EARLY_EOF", "attachMediaElement", "onPlaying", "onWaiting", "onError", "message", "onLoadStart", "addEventListener", "_videoEventListeners", "playing", "waiting", "loadstart", "clearTimeout", "listeners", "removeEventListener", "ref", "onDoubleClick", "fontSize", "marginTop", "controls", "autoPlay", "muted", "playsInline", "objectFit", "top", "left", "transform", "textAlign", "_c3", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/VideoPlayer.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport flvjs from 'flv.js';\nconst flv_BASE_URL = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\n\nconst FullscreenButton = ({ onClick }) => (\n  <div\n    onClick={onClick}\n    style={{\n      position: 'absolute',\n      bottom: '10px',\n      right: '10px',\n      padding: '6px',\n      backgroundColor: 'rgba(0, 0, 0, 0.5)',\n      borderRadius: '4px',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 10,\n      transition: 'opacity 0.3s',\n      opacity: 0.6,\n      color: '#fff',\n      width: '24px',\n      height: '24px'\n    }}\n    onMouseEnter={(e) => e.currentTarget.style.opacity = 1}\n    onMouseLeave={(e) => e.currentTarget.style.opacity = 0.6}\n    title=\"全屏\"\n  >\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" style={{ width: '16px', height: '16px' }}>\n      <path d=\"M3 5v4h2V5h4V3H5c-1.1 0-2 .9-2 2zm2 10H3v4c0 1.1.9 2 2 2h4v-2H5v-4zm14 4h-4v2h4c1.1 0 2-.9 2-2v-4h-2v4zm0-16h-4v2h4v4h2V5c0-1.1-.9-2-2-2z\"/>\n    </svg>\n  </div>\n);\n\nconst RefreshButton = ({ onClick }) => (\n  <div\n    onClick={onClick}\n    style={{\n      position: 'absolute',\n      bottom: '10px',\n      right: '44px',\n      padding: '6px',\n      backgroundColor: 'rgba(0, 0, 0, 0.5)',\n      borderRadius: '4px',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 10,\n      transition: 'opacity 0.3s',\n      opacity: 0.6,\n      color: '#fff',\n      width: '24px',\n      height: '24px'\n    }}\n    onMouseEnter={(e) => e.currentTarget.style.opacity = 1}\n    onMouseLeave={(e) => e.currentTarget.style.opacity = 0.6}\n    title=\"刷新视频流\"\n  >\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" style={{ width: '16px', height: '16px' }}>\n      <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\n    </svg>\n  </div>\n);\n\nconst VideoPlayer = ({ deviceId, rtspUrl, flvUrl }) => {\n  const videoRef = useRef(null);\n  const containerRef = useRef(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [hasRtspUrl, setHasRtspUrl] = useState(!!rtspUrl);\n  const [debugInfo, setDebugInfo] = useState('');\n  const playerRef = useRef(null);\n\n  const handleFullscreen = () => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    if (!document.fullscreenElement) {\n      if (container.requestFullscreen) {\n        container.requestFullscreen().catch(e => console.warn('进入全屏失败:', e));\n      } else if (container.webkitRequestFullscreen) {\n        container.webkitRequestFullscreen();\n      } else if (container.msRequestFullscreen) {\n        container.msRequestFullscreen();\n      }\n    } else {\n      if (document.exitFullscreen) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen().catch(e => console.warn('退出全屏失败:', e));\n        }\n      } else if (document.webkitExitFullscreen) {\n        if (document.webkitFullscreenElement) {\n          document.webkitExitFullscreen();\n        }\n      } else if (document.msExitFullscreen) {\n        if (document.msFullscreenElement) {\n          document.msExitFullscreen();\n        }\n      }\n    }\n  };\n\n  const handleDoubleClick = () => {\n    const container = containerRef.current;\n    if (!container) return;\n    if (!document.fullscreenElement) {\n      if (container.requestFullscreen) {\n        container.requestFullscreen().catch(e => console.warn('进入全屏失败:', e));\n      } else if (container.webkitRequestFullscreen) {\n        container.webkitRequestFullscreen();\n      } else if (container.msRequestFullscreen) {\n        container.msRequestFullscreen();\n      }\n    } else {\n      if (document.exitFullscreen) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen().catch(e => console.warn('退出全屏失败:', e));\n        }\n      } else if (document.webkitExitFullscreen) {\n        if (document.webkitFullscreenElement) {\n          document.webkitExitFullscreen();\n        }\n      } else if (document.msExitFullscreen) {\n        if (document.msFullscreenElement) {\n          document.msExitFullscreen();\n        }\n      }\n    }\n  };\n\n  // 手动刷新视频流\n  const [refreshKey, setRefreshKey] = useState(0);\n  const handleRefresh = () => {\n    console.log('手动刷新视频流');\n    setRefreshKey(prev => prev + 1); // 触发 useEffect 重新执行\n  };\n\n  useEffect(() => {\n    if (!rtspUrl) {\n      setHasRtspUrl(false);\n      setLoading(false);\n      return;\n    }\n\n    setHasRtspUrl(true);\n    setLoading(true);\n    setError(null);\n\n    let retryCount = 0;\n    const maxRetries = 10;\n    let retryTimeout = null;\n    let healthCheckInterval = null;\n    let isDestroyed = false;\n\n    // 重连延迟计算（指数退避）\n    const getRetryDelay = (count) => {\n      return Math.min(1000 * Math.pow(1.5, count), 10000); // 最大10秒\n    };\n\n    // 检查流状态\n    const checkStreamHealth = async () => {\n      if (isDestroyed || !playerRef.current) return;\n\n      try {\n        // 首先检查后端流状态API\n        const apiResponse = await fetch('http://localhost:8001/api/streams', {\n          method: 'GET',\n          cache: 'no-cache'\n        });\n\n        if (apiResponse.ok) {\n          const streams = await apiResponse.json();\n          const streamPath = `/live/${deviceId}`;\n          const streamInfo = streams[streamPath];\n\n          if (!streamInfo || !streamInfo.publisher) {\n            console.warn(`后端流状态检查: 流 ${deviceId} 不可用`);\n            if (playerRef.current && !isDestroyed) {\n              handlePlayerError('BACKEND_STREAM_UNAVAILABLE', 'Backend reports stream unavailable');\n            }\n            return;\n          } else {\n            console.log(`后端流状态检查: 流 ${deviceId} 可用`);\n          }\n        }\n\n        // 然后检查FLV流的HTTP状态\n        const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n        const response = await fetch(url, {\n          method: 'HEAD',\n          cache: 'no-cache',\n          timeout: 5000\n        });\n\n        if (!response.ok) {\n          console.warn(`FLV流健康检查失败: ${response.status}`);\n          if (playerRef.current && !isDestroyed) {\n            handlePlayerError('FLV_STREAM_UNAVAILABLE', 'FLV stream health check failed');\n          }\n        } else {\n          console.log(`FLV流健康检查: 流 ${deviceId} 可用`);\n        }\n      } catch (error) {\n        console.warn('流健康检查异常:', error);\n        // 网络错误时不立即重连，可能是临时网络问题\n      }\n    };\n\n    // 处理播放器错误和重连\n    const handlePlayerError = (errorType, errorDetail) => {\n      if (isDestroyed) return;\n\n      console.error('播放器错误:', errorType, errorDetail);\n      setDebugInfo(`播放器错误: ${errorType}`);\n\n      if (retryCount < maxRetries) {\n        retryCount++;\n        const delay = getRetryDelay(retryCount);\n\n        console.log(`准备重试播放器 (${retryCount}/${maxRetries})，延迟 ${delay}ms`);\n        setDebugInfo(`重连中... (${retryCount}/${maxRetries})`);\n\n        retryTimeout = setTimeout(() => {\n          if (!isDestroyed) {\n            console.log('重新初始化播放器...');\n            const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n            initializePlayer(url);\n          }\n        }, delay);\n      } else {\n        console.error('播放器重试次数超过限制');\n        setError(`连接失败 (已重试${maxRetries}次)`);\n        setLoading(false);\n      }\n    };\n\n    const loadVideo = () => {\n      if (flvjs.isSupported()) {\n        const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n        setDebugInfo(`正在检查流状态...`);\n\n        console.log('开始初始化播放器...');\n        initializePlayer(url);\n        return;\n      } else {\n        setError('您的浏览器不支持FLV视频流');\n        setLoading(false);\n      }\n    };\n\n    const initializePlayer = (flvUrl) => {\n      if (isDestroyed) return;\n\n      console.log('开始初始化 FLV 播放器');\n\n      // 清理旧的播放器\n      if (playerRef.current) {\n        try {\n          // 清理流监控定时器\n          if (playerRef.current._streamMonitorInterval) {\n            clearInterval(playerRef.current._streamMonitorInterval);\n            playerRef.current._streamMonitorInterval = null;\n          }\n\n          playerRef.current.pause();\n          playerRef.current.unload();\n          playerRef.current.detachMediaElement();\n          playerRef.current.destroy();\n        } catch (e) {\n          console.warn('清理旧播放器时出错:', e);\n        }\n        playerRef.current = null;\n      }\n\n      // 清理健康检查定时器\n      if (healthCheckInterval) {\n        clearInterval(healthCheckInterval);\n        healthCheckInterval = null;\n      }\n\n      const player = flvjs.createPlayer({\n        type: 'flv',\n        url: flvUrl,\n        isLive: true,\n        hasAudio: false,\n        hasVideo: true,\n        cors: true,\n        enableStashBuffer: false,\n        stashInitialSize: 128,\n        lazyLoad: false,\n        lazyLoadMaxDuration: 0,\n        fixAudioTimestampGap: false,\n        seekType: 'range',\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache',\n          'If-None-Match': '*'\n        }\n      });\n\n      // 错误处理\n      player.on(flvjs.Events.ERROR, (errorType, errorDetail) => {\n        if (isDestroyed) return;\n        handlePlayerError(errorType, errorDetail);\n      });\n\n      // 媒体信息事件\n      player.on(flvjs.Events.MEDIA_INFO, (mediaInfo) => {\n        if (isDestroyed) return;\n        console.log('媒体信息:', mediaInfo);\n        setDebugInfo('获取到媒体信息');\n\n        // 重置重试计数\n        retryCount = 0;\n\n        // 启动健康检查\n        if (!healthCheckInterval) {\n          healthCheckInterval = setInterval(checkStreamHealth, 15000); // 每15秒检查一次\n        }\n\n        // 启动流状态监控（更频繁的检查）\n        const streamMonitorInterval = setInterval(async () => {\n          if (isDestroyed) return;\n\n          try {\n            const apiResponse = await fetch('http://localhost:8001/api/streams', {\n              method: 'GET',\n              cache: 'no-cache'\n            });\n\n            if (apiResponse.ok) {\n              const streams = await apiResponse.json();\n              const streamPath = `/live/${deviceId}`;\n              const streamInfo = streams[streamPath];\n\n              // 如果后端显示流可用，但前端播放器有问题，尝试重连\n              if (streamInfo && streamInfo.publisher && playerRef.current) {\n                const video = videoRef.current;\n                if (video && (video.paused || video.ended || video.readyState < 2)) {\n                  console.log('检测到流状态不一致，尝试重新加载播放器...');\n                  try {\n                    playerRef.current.unload();\n                    playerRef.current.load();\n                    playerRef.current.play();\n                  } catch (e) {\n                    console.warn('重新加载播放器失败:', e);\n                  }\n                }\n              }\n            }\n          } catch (error) {\n            console.warn('流状态监控异常:', error);\n          }\n        }, 10000); // 每10秒检查一次\n\n        // 保存监控定时器引用\n        player._streamMonitorInterval = streamMonitorInterval;\n      });\n\n      // 统计信息事件\n      player.on(flvjs.Events.STATISTICS_INFO, (stats) => {\n        if (isDestroyed) return;\n        console.log('播放统计:', stats);\n      });\n\n      // 加载开始事件\n      player.on(flvjs.Events.LOADING_COMPLETE, () => {\n        if (isDestroyed) return;\n        console.log('流加载完成');\n        setDebugInfo('流加载完成');\n      });\n\n      // 缓冲区事件\n      player.on(flvjs.Events.RECOVERED_EARLY_EOF, () => {\n        if (isDestroyed) return;\n        console.log('从早期EOF恢复');\n      });\n\n      try {\n        if (isDestroyed || !videoRef.current) return;\n\n        console.log('正在加载视频元素');\n        player.attachMediaElement(videoRef.current);\n\n        // 视频事件监听\n        const onPlaying = () => {\n          if (isDestroyed) return;\n          console.log('视频开始播放');\n          setLoading(false);\n          setError(null);\n          setDebugInfo('播放中...');\n          retryCount = 0; // 重置重试计数\n        };\n\n        const onWaiting = () => {\n          if (isDestroyed) return;\n          console.log('视频缓冲中...');\n          setDebugInfo('缓冲中...');\n        };\n\n        const onError = (e) => {\n          if (isDestroyed) return;\n          console.error('视频元素错误:', e);\n          handlePlayerError('VIDEO_ERROR', e.message || 'Video element error');\n        };\n\n        const onLoadStart = () => {\n          if (isDestroyed) return;\n          setDebugInfo('开始加载...');\n        };\n\n        videoRef.current.addEventListener('playing', onPlaying);\n        videoRef.current.addEventListener('waiting', onWaiting);\n        videoRef.current.addEventListener('error', onError);\n        videoRef.current.addEventListener('loadstart', onLoadStart);\n\n        // 保存事件监听器引用以便清理\n        player._videoEventListeners = {\n          playing: onPlaying,\n          waiting: onWaiting,\n          error: onError,\n          loadstart: onLoadStart\n        };\n\n        player.load();\n        playerRef.current = player;\n\n        player.play().catch(e => {\n          if (isDestroyed) return;\n          console.error('播放失败:', e);\n          handlePlayerError('PLAY_FAILED', e.message);\n        });\n\n      } catch (e) {\n        if (isDestroyed) return;\n        console.error('播放器初始化失败:', e);\n        handlePlayerError('INIT_FAILED', e.message);\n      }\n    };\n\n    loadVideo();\n\n    return () => {\n      isDestroyed = true;\n\n      // 清理重试定时器\n      if (retryTimeout) {\n        clearTimeout(retryTimeout);\n        retryTimeout = null;\n      }\n\n      // 清理健康检查定时器\n      if (healthCheckInterval) {\n        clearInterval(healthCheckInterval);\n        healthCheckInterval = null;\n      }\n\n      // 清理播放器\n      if (playerRef.current) {\n        try {\n          // 清理流监控定时器\n          if (playerRef.current._streamMonitorInterval) {\n            clearInterval(playerRef.current._streamMonitorInterval);\n            playerRef.current._streamMonitorInterval = null;\n          }\n\n          // 清理视频事件监听器\n          if (playerRef.current._videoEventListeners && videoRef.current) {\n            const listeners = playerRef.current._videoEventListeners;\n            videoRef.current.removeEventListener('playing', listeners.playing);\n            videoRef.current.removeEventListener('waiting', listeners.waiting);\n            videoRef.current.removeEventListener('error', listeners.error);\n            videoRef.current.removeEventListener('loadstart', listeners.loadstart);\n          }\n\n          playerRef.current.pause();\n          playerRef.current.unload();\n          playerRef.current.detachMediaElement();\n          playerRef.current.destroy();\n        } catch (e) {\n          console.warn('清理播放器时出错:', e);\n        }\n        playerRef.current = null;\n      }\n    };\n  }, [deviceId, rtspUrl, flvUrl, refreshKey]);\n\n  if (!hasRtspUrl) {\n    return (\n      <div\n        ref={containerRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          backgroundColor: '#000',\n          position: 'relative',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}\n        onDoubleClick={handleDoubleClick}\n      >\n        <div>视频流</div>\n        <div style={{ fontSize: '11px', marginTop: '4px' }}>\n          (该摄像头未配置RTSP地址)\n        </div>\n        <FullscreenButton onClick={handleFullscreen} />\n        <RefreshButton onClick={handleRefresh} />\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div\n        ref={containerRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          backgroundColor: '#000',\n          position: 'relative'\n        }}\n        onDoubleClick={handleDoubleClick}\n      >\n        <video\n          ref={videoRef}\n          controls={false}\n          autoPlay\n          muted\n          playsInline\n          style={{\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n          }}\n          onDoubleClick={handleDoubleClick}\n        />\n        <FullscreenButton onClick={handleFullscreen} />\n        <RefreshButton onClick={handleRefresh} />\n        <div style={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          color: '#fff',\n          textAlign: 'center'\n        }}>\n          <div>正在加载视频流...</div>\n          <div style={{ fontSize: '11px', marginTop: '4px' }}>\n            {debugInfo}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div\n        ref={containerRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          backgroundColor: '#000',\n          position: 'relative',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}\n        onDoubleClick={handleDoubleClick}\n      >\n        <div>视频流错误</div>\n        <div style={{ fontSize: '11px', marginTop: '4px' }}>\n          ({error})\n        </div>\n        <FullscreenButton onClick={handleFullscreen} />\n        <RefreshButton onClick={handleRefresh} />\n      </div>\n    );\n  }\n\n  return (\n    <div\n      ref={containerRef}\n      style={{\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#000',\n        position: 'relative'\n      }}\n      onDoubleClick={handleDoubleClick}\n    >\n      <video\n        ref={videoRef}\n        controls={false}\n        autoPlay\n        muted\n        playsInline\n        style={{\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover',\n        }}\n        onDoubleClick={handleDoubleClick}\n      />\n      <FullscreenButton onClick={handleFullscreen} />\n      <RefreshButton onClick={handleRefresh} />\n      {loading && (\n        <div style={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          color: '#fff',\n          textAlign: 'center'\n        }}>\n          <div>正在加载视频流...</div>\n          <div style={{ fontSize: '11px', marginTop: '4px' }}>\n            {debugInfo}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default VideoPlayer; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC3B,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAQ,CAAC,kBACnCN,OAAA;EACEM,OAAO,EAAEA,OAAQ;EACjBC,KAAK,EAAE;IACLC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,KAAK;IACdC,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,cAAc;IAC1BC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAE;EACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACa,OAAO,GAAG,CAAE;EACvDO,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACa,OAAO,GAAG,GAAI;EACzDQ,KAAK,EAAC,cAAI;EAAAC,QAAA,eAEV7B,OAAA;IAAK8B,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,cAAc;IAACxB,KAAK,EAAE;MAAEe,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAM,QAAA,eACpF7B,OAAA;MAAMgC,CAAC,EAAC;IAA2I;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClJ;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,EAAA,GA7BIhC,gBAAgB;AA+BtB,MAAMiC,aAAa,GAAGA,CAAC;EAAEhC;AAAQ,CAAC,kBAChCN,OAAA;EACEM,OAAO,EAAEA,OAAQ;EACjBC,KAAK,EAAE;IACLC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,KAAK;IACdC,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,cAAc;IAC1BC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAE;EACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACa,OAAO,GAAG,CAAE;EACvDO,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACa,OAAO,GAAG,GAAI;EACzDQ,KAAK,EAAC,gCAAO;EAAAC,QAAA,eAEb7B,OAAA;IAAK8B,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,cAAc;IAACxB,KAAK,EAAE;MAAEe,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAM,QAAA,eACpF7B,OAAA;MAAMgC,CAAC,EAAC;IAA4M;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnN;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACG,GAAA,GA7BID,aAAa;AA+BnB,MAAME,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAMC,QAAQ,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMkD,YAAY,GAAGlD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC6C,OAAO,CAAC;EACvD,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM0D,SAAS,GAAG3D,MAAM,CAAC,IAAI,CAAC;EAE9B,MAAM4D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAGX,YAAY,CAACY,OAAO;IACtC,IAAI,CAACD,SAAS,EAAE;IAEhB,IAAI,CAACE,QAAQ,CAACC,iBAAiB,EAAE;MAC/B,IAAIH,SAAS,CAACI,iBAAiB,EAAE;QAC/BJ,SAAS,CAACI,iBAAiB,CAAC,CAAC,CAACC,KAAK,CAACrC,CAAC,IAAIsC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEvC,CAAC,CAAC,CAAC;MACtE,CAAC,MAAM,IAAIgC,SAAS,CAACQ,uBAAuB,EAAE;QAC5CR,SAAS,CAACQ,uBAAuB,CAAC,CAAC;MACrC,CAAC,MAAM,IAAIR,SAAS,CAACS,mBAAmB,EAAE;QACxCT,SAAS,CAACS,mBAAmB,CAAC,CAAC;MACjC;IACF,CAAC,MAAM;MACL,IAAIP,QAAQ,CAACQ,cAAc,EAAE;QAC3B,IAAIR,QAAQ,CAACC,iBAAiB,EAAE;UAC9BD,QAAQ,CAACQ,cAAc,CAAC,CAAC,CAACL,KAAK,CAACrC,CAAC,IAAIsC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEvC,CAAC,CAAC,CAAC;QAClE;MACF,CAAC,MAAM,IAAIkC,QAAQ,CAACS,oBAAoB,EAAE;QACxC,IAAIT,QAAQ,CAACU,uBAAuB,EAAE;UACpCV,QAAQ,CAACS,oBAAoB,CAAC,CAAC;QACjC;MACF,CAAC,MAAM,IAAIT,QAAQ,CAACW,gBAAgB,EAAE;QACpC,IAAIX,QAAQ,CAACY,mBAAmB,EAAE;UAChCZ,QAAQ,CAACW,gBAAgB,CAAC,CAAC;QAC7B;MACF;IACF;EACF,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMf,SAAS,GAAGX,YAAY,CAACY,OAAO;IACtC,IAAI,CAACD,SAAS,EAAE;IAChB,IAAI,CAACE,QAAQ,CAACC,iBAAiB,EAAE;MAC/B,IAAIH,SAAS,CAACI,iBAAiB,EAAE;QAC/BJ,SAAS,CAACI,iBAAiB,CAAC,CAAC,CAACC,KAAK,CAACrC,CAAC,IAAIsC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEvC,CAAC,CAAC,CAAC;MACtE,CAAC,MAAM,IAAIgC,SAAS,CAACQ,uBAAuB,EAAE;QAC5CR,SAAS,CAACQ,uBAAuB,CAAC,CAAC;MACrC,CAAC,MAAM,IAAIR,SAAS,CAACS,mBAAmB,EAAE;QACxCT,SAAS,CAACS,mBAAmB,CAAC,CAAC;MACjC;IACF,CAAC,MAAM;MACL,IAAIP,QAAQ,CAACQ,cAAc,EAAE;QAC3B,IAAIR,QAAQ,CAACC,iBAAiB,EAAE;UAC9BD,QAAQ,CAACQ,cAAc,CAAC,CAAC,CAACL,KAAK,CAACrC,CAAC,IAAIsC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEvC,CAAC,CAAC,CAAC;QAClE;MACF,CAAC,MAAM,IAAIkC,QAAQ,CAACS,oBAAoB,EAAE;QACxC,IAAIT,QAAQ,CAACU,uBAAuB,EAAE;UACpCV,QAAQ,CAACS,oBAAoB,CAAC,CAAC;QACjC;MACF,CAAC,MAAM,IAAIT,QAAQ,CAACW,gBAAgB,EAAE;QACpC,IAAIX,QAAQ,CAACY,mBAAmB,EAAE;UAChCZ,QAAQ,CAACW,gBAAgB,CAAC,CAAC;QAC7B;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM8E,aAAa,GAAGA,CAAA,KAAM;IAC1BZ,OAAO,CAACa,GAAG,CAAC,SAAS,CAAC;IACtBF,aAAa,CAACG,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC;EAEDlF,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+C,OAAO,EAAE;MACZU,aAAa,CAAC,KAAK,CAAC;MACpBF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAE,aAAa,CAAC,IAAI,CAAC;IACnBF,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI8B,UAAU,GAAG,CAAC;IAClB,MAAMC,UAAU,GAAG,EAAE;IACrB,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAIC,mBAAmB,GAAG,IAAI;IAC9B,IAAIC,WAAW,GAAG,KAAK;;IAEvB;IACA,MAAMC,aAAa,GAAIC,KAAK,IAAK;MAC/B,OAAOC,IAAI,CAACC,GAAG,CAAC,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEH,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACvD,CAAC;;IAED;IACA,MAAMI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAIN,WAAW,IAAI,CAAC3B,SAAS,CAACG,OAAO,EAAE;MAEvC,IAAI;QACF;QACA,MAAM+B,WAAW,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;UACnEC,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE;QACT,CAAC,CAAC;QAEF,IAAIH,WAAW,CAACI,EAAE,EAAE;UAClB,MAAMC,OAAO,GAAG,MAAML,WAAW,CAACM,IAAI,CAAC,CAAC;UACxC,MAAMC,UAAU,GAAG,SAASvD,QAAQ,EAAE;UACtC,MAAMwD,UAAU,GAAGH,OAAO,CAACE,UAAU,CAAC;UAEtC,IAAI,CAACC,UAAU,IAAI,CAACA,UAAU,CAACC,SAAS,EAAE;YACxCnC,OAAO,CAACC,IAAI,CAAC,cAAcvB,QAAQ,MAAM,CAAC;YAC1C,IAAIc,SAAS,CAACG,OAAO,IAAI,CAACwB,WAAW,EAAE;cACrCiB,iBAAiB,CAAC,4BAA4B,EAAE,oCAAoC,CAAC;YACvF;YACA;UACF,CAAC,MAAM;YACLpC,OAAO,CAACa,GAAG,CAAC,cAAcnC,QAAQ,KAAK,CAAC;UAC1C;QACF;;QAEA;QACA,MAAM2D,GAAG,GAAGzD,MAAM,IAAI,GAAG1C,YAAY,SAASwC,QAAQ,MAAM;QAC5D,MAAM4D,QAAQ,GAAG,MAAMX,KAAK,CAACU,GAAG,EAAE;UAChCT,MAAM,EAAE,MAAM;UACdC,KAAK,EAAE,UAAU;UACjBU,OAAO,EAAE;QACX,CAAC,CAAC;QAEF,IAAI,CAACD,QAAQ,CAACR,EAAE,EAAE;UAChB9B,OAAO,CAACC,IAAI,CAAC,eAAeqC,QAAQ,CAACE,MAAM,EAAE,CAAC;UAC9C,IAAIhD,SAAS,CAACG,OAAO,IAAI,CAACwB,WAAW,EAAE;YACrCiB,iBAAiB,CAAC,wBAAwB,EAAE,gCAAgC,CAAC;UAC/E;QACF,CAAC,MAAM;UACLpC,OAAO,CAACa,GAAG,CAAC,eAAenC,QAAQ,KAAK,CAAC;QAC3C;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdgB,OAAO,CAACC,IAAI,CAAC,UAAU,EAAEjB,KAAK,CAAC;QAC/B;MACF;IACF,CAAC;;IAED;IACA,MAAMoD,iBAAiB,GAAGA,CAACK,SAAS,EAAEC,WAAW,KAAK;MACpD,IAAIvB,WAAW,EAAE;MAEjBnB,OAAO,CAAChB,KAAK,CAAC,QAAQ,EAAEyD,SAAS,EAAEC,WAAW,CAAC;MAC/CnD,YAAY,CAAC,UAAUkD,SAAS,EAAE,CAAC;MAEnC,IAAI1B,UAAU,GAAGC,UAAU,EAAE;QAC3BD,UAAU,EAAE;QACZ,MAAM4B,KAAK,GAAGvB,aAAa,CAACL,UAAU,CAAC;QAEvCf,OAAO,CAACa,GAAG,CAAC,YAAYE,UAAU,IAAIC,UAAU,QAAQ2B,KAAK,IAAI,CAAC;QAClEpD,YAAY,CAAC,WAAWwB,UAAU,IAAIC,UAAU,GAAG,CAAC;QAEpDC,YAAY,GAAG2B,UAAU,CAAC,MAAM;UAC9B,IAAI,CAACzB,WAAW,EAAE;YAChBnB,OAAO,CAACa,GAAG,CAAC,aAAa,CAAC;YAC1B,MAAMwB,GAAG,GAAGzD,MAAM,IAAI,GAAG1C,YAAY,SAASwC,QAAQ,MAAM;YAC5DmE,gBAAgB,CAACR,GAAG,CAAC;UACvB;QACF,CAAC,EAAEM,KAAK,CAAC;MACX,CAAC,MAAM;QACL3C,OAAO,CAAChB,KAAK,CAAC,aAAa,CAAC;QAC5BC,QAAQ,CAAC,YAAY+B,UAAU,IAAI,CAAC;QACpC7B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,MAAM2D,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI/G,KAAK,CAACgH,WAAW,CAAC,CAAC,EAAE;QACvB,MAAMV,GAAG,GAAGzD,MAAM,IAAI,GAAG1C,YAAY,SAASwC,QAAQ,MAAM;QAC5Da,YAAY,CAAC,YAAY,CAAC;QAE1BS,OAAO,CAACa,GAAG,CAAC,aAAa,CAAC;QAC1BgC,gBAAgB,CAACR,GAAG,CAAC;QACrB;MACF,CAAC,MAAM;QACLpD,QAAQ,CAAC,gBAAgB,CAAC;QAC1BE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,MAAM0D,gBAAgB,GAAIjE,MAAM,IAAK;MACnC,IAAIuC,WAAW,EAAE;MAEjBnB,OAAO,CAACa,GAAG,CAAC,eAAe,CAAC;;MAE5B;MACA,IAAIrB,SAAS,CAACG,OAAO,EAAE;QACrB,IAAI;UACF;UACA,IAAIH,SAAS,CAACG,OAAO,CAACqD,sBAAsB,EAAE;YAC5CC,aAAa,CAACzD,SAAS,CAACG,OAAO,CAACqD,sBAAsB,CAAC;YACvDxD,SAAS,CAACG,OAAO,CAACqD,sBAAsB,GAAG,IAAI;UACjD;UAEAxD,SAAS,CAACG,OAAO,CAACuD,KAAK,CAAC,CAAC;UACzB1D,SAAS,CAACG,OAAO,CAACwD,MAAM,CAAC,CAAC;UAC1B3D,SAAS,CAACG,OAAO,CAACyD,kBAAkB,CAAC,CAAC;UACtC5D,SAAS,CAACG,OAAO,CAAC0D,OAAO,CAAC,CAAC;QAC7B,CAAC,CAAC,OAAO3F,CAAC,EAAE;UACVsC,OAAO,CAACC,IAAI,CAAC,YAAY,EAAEvC,CAAC,CAAC;QAC/B;QACA8B,SAAS,CAACG,OAAO,GAAG,IAAI;MAC1B;;MAEA;MACA,IAAIuB,mBAAmB,EAAE;QACvB+B,aAAa,CAAC/B,mBAAmB,CAAC;QAClCA,mBAAmB,GAAG,IAAI;MAC5B;MAEA,MAAMoC,MAAM,GAAGvH,KAAK,CAACwH,YAAY,CAAC;QAChCC,IAAI,EAAE,KAAK;QACXnB,GAAG,EAAEzD,MAAM;QACX6E,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,IAAI;QACVC,iBAAiB,EAAE,KAAK;QACxBC,gBAAgB,EAAE,GAAG;QACrBC,QAAQ,EAAE,KAAK;QACfC,mBAAmB,EAAE,CAAC;QACtBC,oBAAoB,EAAE,KAAK;QAC3BC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU;UAC3B,QAAQ,EAAE,UAAU;UACpB,eAAe,EAAE;QACnB;MACF,CAAC,CAAC;;MAEF;MACAb,MAAM,CAACc,EAAE,CAACrI,KAAK,CAACsI,MAAM,CAACC,KAAK,EAAE,CAAC7B,SAAS,EAAEC,WAAW,KAAK;QACxD,IAAIvB,WAAW,EAAE;QACjBiB,iBAAiB,CAACK,SAAS,EAAEC,WAAW,CAAC;MAC3C,CAAC,CAAC;;MAEF;MACAY,MAAM,CAACc,EAAE,CAACrI,KAAK,CAACsI,MAAM,CAACE,UAAU,EAAGC,SAAS,IAAK;QAChD,IAAIrD,WAAW,EAAE;QACjBnB,OAAO,CAACa,GAAG,CAAC,OAAO,EAAE2D,SAAS,CAAC;QAC/BjF,YAAY,CAAC,SAAS,CAAC;;QAEvB;QACAwB,UAAU,GAAG,CAAC;;QAEd;QACA,IAAI,CAACG,mBAAmB,EAAE;UACxBA,mBAAmB,GAAGuD,WAAW,CAAChD,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC;QAC/D;;QAEA;QACA,MAAMiD,qBAAqB,GAAGD,WAAW,CAAC,YAAY;UACpD,IAAItD,WAAW,EAAE;UAEjB,IAAI;YACF,MAAMO,WAAW,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;cACnEC,MAAM,EAAE,KAAK;cACbC,KAAK,EAAE;YACT,CAAC,CAAC;YAEF,IAAIH,WAAW,CAACI,EAAE,EAAE;cAClB,MAAMC,OAAO,GAAG,MAAML,WAAW,CAACM,IAAI,CAAC,CAAC;cACxC,MAAMC,UAAU,GAAG,SAASvD,QAAQ,EAAE;cACtC,MAAMwD,UAAU,GAAGH,OAAO,CAACE,UAAU,CAAC;;cAEtC;cACA,IAAIC,UAAU,IAAIA,UAAU,CAACC,SAAS,IAAI3C,SAAS,CAACG,OAAO,EAAE;gBAC3D,MAAMgF,KAAK,GAAG7F,QAAQ,CAACa,OAAO;gBAC9B,IAAIgF,KAAK,KAAKA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACG,UAAU,GAAG,CAAC,CAAC,EAAE;kBAClE9E,OAAO,CAACa,GAAG,CAAC,wBAAwB,CAAC;kBACrC,IAAI;oBACFrB,SAAS,CAACG,OAAO,CAACwD,MAAM,CAAC,CAAC;oBAC1B3D,SAAS,CAACG,OAAO,CAACoF,IAAI,CAAC,CAAC;oBACxBvF,SAAS,CAACG,OAAO,CAACqF,IAAI,CAAC,CAAC;kBAC1B,CAAC,CAAC,OAAOtH,CAAC,EAAE;oBACVsC,OAAO,CAACC,IAAI,CAAC,YAAY,EAAEvC,CAAC,CAAC;kBAC/B;gBACF;cACF;YACF;UACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;YACdgB,OAAO,CAACC,IAAI,CAAC,UAAU,EAAEjB,KAAK,CAAC;UACjC;QACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;QAEX;QACAsE,MAAM,CAACN,sBAAsB,GAAG0B,qBAAqB;MACvD,CAAC,CAAC;;MAEF;MACApB,MAAM,CAACc,EAAE,CAACrI,KAAK,CAACsI,MAAM,CAACY,eAAe,EAAGC,KAAK,IAAK;QACjD,IAAI/D,WAAW,EAAE;QACjBnB,OAAO,CAACa,GAAG,CAAC,OAAO,EAAEqE,KAAK,CAAC;MAC7B,CAAC,CAAC;;MAEF;MACA5B,MAAM,CAACc,EAAE,CAACrI,KAAK,CAACsI,MAAM,CAACc,gBAAgB,EAAE,MAAM;QAC7C,IAAIhE,WAAW,EAAE;QACjBnB,OAAO,CAACa,GAAG,CAAC,OAAO,CAAC;QACpBtB,YAAY,CAAC,OAAO,CAAC;MACvB,CAAC,CAAC;;MAEF;MACA+D,MAAM,CAACc,EAAE,CAACrI,KAAK,CAACsI,MAAM,CAACe,mBAAmB,EAAE,MAAM;QAChD,IAAIjE,WAAW,EAAE;QACjBnB,OAAO,CAACa,GAAG,CAAC,UAAU,CAAC;MACzB,CAAC,CAAC;MAEF,IAAI;QACF,IAAIM,WAAW,IAAI,CAACrC,QAAQ,CAACa,OAAO,EAAE;QAEtCK,OAAO,CAACa,GAAG,CAAC,UAAU,CAAC;QACvByC,MAAM,CAAC+B,kBAAkB,CAACvG,QAAQ,CAACa,OAAO,CAAC;;QAE3C;QACA,MAAM2F,SAAS,GAAGA,CAAA,KAAM;UACtB,IAAInE,WAAW,EAAE;UACjBnB,OAAO,CAACa,GAAG,CAAC,QAAQ,CAAC;UACrB1B,UAAU,CAAC,KAAK,CAAC;UACjBF,QAAQ,CAAC,IAAI,CAAC;UACdM,YAAY,CAAC,QAAQ,CAAC;UACtBwB,UAAU,GAAG,CAAC,CAAC,CAAC;QAClB,CAAC;QAED,MAAMwE,SAAS,GAAGA,CAAA,KAAM;UACtB,IAAIpE,WAAW,EAAE;UACjBnB,OAAO,CAACa,GAAG,CAAC,UAAU,CAAC;UACvBtB,YAAY,CAAC,QAAQ,CAAC;QACxB,CAAC;QAED,MAAMiG,OAAO,GAAI9H,CAAC,IAAK;UACrB,IAAIyD,WAAW,EAAE;UACjBnB,OAAO,CAAChB,KAAK,CAAC,SAAS,EAAEtB,CAAC,CAAC;UAC3B0E,iBAAiB,CAAC,aAAa,EAAE1E,CAAC,CAAC+H,OAAO,IAAI,qBAAqB,CAAC;QACtE,CAAC;QAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;UACxB,IAAIvE,WAAW,EAAE;UACjB5B,YAAY,CAAC,SAAS,CAAC;QACzB,CAAC;QAEDT,QAAQ,CAACa,OAAO,CAACgG,gBAAgB,CAAC,SAAS,EAAEL,SAAS,CAAC;QACvDxG,QAAQ,CAACa,OAAO,CAACgG,gBAAgB,CAAC,SAAS,EAAEJ,SAAS,CAAC;QACvDzG,QAAQ,CAACa,OAAO,CAACgG,gBAAgB,CAAC,OAAO,EAAEH,OAAO,CAAC;QACnD1G,QAAQ,CAACa,OAAO,CAACgG,gBAAgB,CAAC,WAAW,EAAED,WAAW,CAAC;;QAE3D;QACApC,MAAM,CAACsC,oBAAoB,GAAG;UAC5BC,OAAO,EAAEP,SAAS;UAClBQ,OAAO,EAAEP,SAAS;UAClBvG,KAAK,EAAEwG,OAAO;UACdO,SAAS,EAAEL;QACb,CAAC;QAEDpC,MAAM,CAACyB,IAAI,CAAC,CAAC;QACbvF,SAAS,CAACG,OAAO,GAAG2D,MAAM;QAE1BA,MAAM,CAAC0B,IAAI,CAAC,CAAC,CAACjF,KAAK,CAACrC,CAAC,IAAI;UACvB,IAAIyD,WAAW,EAAE;UACjBnB,OAAO,CAAChB,KAAK,CAAC,OAAO,EAAEtB,CAAC,CAAC;UACzB0E,iBAAiB,CAAC,aAAa,EAAE1E,CAAC,CAAC+H,OAAO,CAAC;QAC7C,CAAC,CAAC;MAEJ,CAAC,CAAC,OAAO/H,CAAC,EAAE;QACV,IAAIyD,WAAW,EAAE;QACjBnB,OAAO,CAAChB,KAAK,CAAC,WAAW,EAAEtB,CAAC,CAAC;QAC7B0E,iBAAiB,CAAC,aAAa,EAAE1E,CAAC,CAAC+H,OAAO,CAAC;MAC7C;IACF,CAAC;IAED3C,SAAS,CAAC,CAAC;IAEX,OAAO,MAAM;MACX3B,WAAW,GAAG,IAAI;;MAElB;MACA,IAAIF,YAAY,EAAE;QAChB+E,YAAY,CAAC/E,YAAY,CAAC;QAC1BA,YAAY,GAAG,IAAI;MACrB;;MAEA;MACA,IAAIC,mBAAmB,EAAE;QACvB+B,aAAa,CAAC/B,mBAAmB,CAAC;QAClCA,mBAAmB,GAAG,IAAI;MAC5B;;MAEA;MACA,IAAI1B,SAAS,CAACG,OAAO,EAAE;QACrB,IAAI;UACF;UACA,IAAIH,SAAS,CAACG,OAAO,CAACqD,sBAAsB,EAAE;YAC5CC,aAAa,CAACzD,SAAS,CAACG,OAAO,CAACqD,sBAAsB,CAAC;YACvDxD,SAAS,CAACG,OAAO,CAACqD,sBAAsB,GAAG,IAAI;UACjD;;UAEA;UACA,IAAIxD,SAAS,CAACG,OAAO,CAACiG,oBAAoB,IAAI9G,QAAQ,CAACa,OAAO,EAAE;YAC9D,MAAMsG,SAAS,GAAGzG,SAAS,CAACG,OAAO,CAACiG,oBAAoB;YACxD9G,QAAQ,CAACa,OAAO,CAACuG,mBAAmB,CAAC,SAAS,EAAED,SAAS,CAACJ,OAAO,CAAC;YAClE/G,QAAQ,CAACa,OAAO,CAACuG,mBAAmB,CAAC,SAAS,EAAED,SAAS,CAACH,OAAO,CAAC;YAClEhH,QAAQ,CAACa,OAAO,CAACuG,mBAAmB,CAAC,OAAO,EAAED,SAAS,CAACjH,KAAK,CAAC;YAC9DF,QAAQ,CAACa,OAAO,CAACuG,mBAAmB,CAAC,WAAW,EAAED,SAAS,CAACF,SAAS,CAAC;UACxE;UAEAvG,SAAS,CAACG,OAAO,CAACuD,KAAK,CAAC,CAAC;UACzB1D,SAAS,CAACG,OAAO,CAACwD,MAAM,CAAC,CAAC;UAC1B3D,SAAS,CAACG,OAAO,CAACyD,kBAAkB,CAAC,CAAC;UACtC5D,SAAS,CAACG,OAAO,CAAC0D,OAAO,CAAC,CAAC;QAC7B,CAAC,CAAC,OAAO3F,CAAC,EAAE;UACVsC,OAAO,CAACC,IAAI,CAAC,WAAW,EAAEvC,CAAC,CAAC;QAC9B;QACA8B,SAAS,CAACG,OAAO,GAAG,IAAI;MAC1B;IACF,CAAC;EACH,CAAC,EAAE,CAACjB,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAE8B,UAAU,CAAC,CAAC;EAE3C,IAAI,CAACtB,UAAU,EAAE;IACf,oBACEnD,OAAA;MACEkK,GAAG,EAAEpH,YAAa;MAClBvC,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdX,eAAe,EAAE,MAAM;QACvBJ,QAAQ,EAAE,UAAU;QACpBO,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MACFkJ,aAAa,EAAE3F,iBAAkB;MAAA3C,QAAA,gBAEjC7B,OAAA;QAAA6B,QAAA,EAAK;MAAG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACdpC,OAAA;QAAKO,KAAK,EAAE;UAAE6J,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAxI,QAAA,EAAC;MAEpD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNpC,OAAA,CAACK,gBAAgB;QAACC,OAAO,EAAEkD;MAAiB;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CpC,OAAA,CAACsC,aAAa;QAAChC,OAAO,EAAEqE;MAAc;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAEV;EAEA,IAAIa,OAAO,EAAE;IACX,oBACEjD,OAAA;MACEkK,GAAG,EAAEpH,YAAa;MAClBvC,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdX,eAAe,EAAE,MAAM;QACvBJ,QAAQ,EAAE;MACZ,CAAE;MACF2J,aAAa,EAAE3F,iBAAkB;MAAA3C,QAAA,gBAEjC7B,OAAA;QACEkK,GAAG,EAAErH,QAAS;QACdyH,QAAQ,EAAE,KAAM;QAChBC,QAAQ;QACRC,KAAK;QACLC,WAAW;QACXlK,KAAK,EAAE;UACLe,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdmJ,SAAS,EAAE;QACb,CAAE;QACFP,aAAa,EAAE3F;MAAkB;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACFpC,OAAA,CAACK,gBAAgB;QAACC,OAAO,EAAEkD;MAAiB;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CpC,OAAA,CAACsC,aAAa;QAAChC,OAAO,EAAEqE;MAAc;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzCpC,OAAA;QAAKO,KAAK,EAAE;UACVC,QAAQ,EAAE,UAAU;UACpBmK,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE,uBAAuB;UAClCxJ,KAAK,EAAE,MAAM;UACbyJ,SAAS,EAAE;QACb,CAAE;QAAAjJ,QAAA,gBACA7B,OAAA;UAAA6B,QAAA,EAAK;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrBpC,OAAA;UAAKO,KAAK,EAAE;YAAE6J,QAAQ,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAM,CAAE;UAAAxI,QAAA,EAChDwB;QAAS;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIW,KAAK,EAAE;IACT,oBACE/C,OAAA;MACEkK,GAAG,EAAEpH,YAAa;MAClBvC,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdX,eAAe,EAAE,MAAM;QACvBJ,QAAQ,EAAE,UAAU;QACpBO,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MACFkJ,aAAa,EAAE3F,iBAAkB;MAAA3C,QAAA,gBAEjC7B,OAAA;QAAA6B,QAAA,EAAK;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChBpC,OAAA;QAAKO,KAAK,EAAE;UAAE6J,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAxI,QAAA,GAAC,GACjD,EAACkB,KAAK,EAAC,GACV;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNpC,OAAA,CAACK,gBAAgB;QAACC,OAAO,EAAEkD;MAAiB;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CpC,OAAA,CAACsC,aAAa;QAAChC,OAAO,EAAEqE;MAAc;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAEV;EAEA,oBACEpC,OAAA;IACEkK,GAAG,EAAEpH,YAAa;IAClBvC,KAAK,EAAE;MACLe,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdX,eAAe,EAAE,MAAM;MACvBJ,QAAQ,EAAE;IACZ,CAAE;IACF2J,aAAa,EAAE3F,iBAAkB;IAAA3C,QAAA,gBAEjC7B,OAAA;MACEkK,GAAG,EAAErH,QAAS;MACdyH,QAAQ,EAAE,KAAM;MAChBC,QAAQ;MACRC,KAAK;MACLC,WAAW;MACXlK,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdmJ,SAAS,EAAE;MACb,CAAE;MACFP,aAAa,EAAE3F;IAAkB;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,eACFpC,OAAA,CAACK,gBAAgB;MAACC,OAAO,EAAEkD;IAAiB;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/CpC,OAAA,CAACsC,aAAa;MAAChC,OAAO,EAAEqE;IAAc;MAAA1C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACxCa,OAAO,iBACNjD,OAAA;MAAKO,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBmK,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,uBAAuB;QAClCxJ,KAAK,EAAE,MAAM;QACbyJ,SAAS,EAAE;MACb,CAAE;MAAAjJ,QAAA,gBACA7B,OAAA;QAAA6B,QAAA,EAAK;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrBpC,OAAA;QAAKO,KAAK,EAAE;UAAE6J,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAxI,QAAA,EAChDwB;MAAS;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACQ,EAAA,CAhjBIJ,WAAW;AAAAuI,GAAA,GAAXvI,WAAW;AAkjBjB,eAAeA,WAAW;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAwI,GAAA;AAAAC,YAAA,CAAA3I,EAAA;AAAA2I,YAAA,CAAAzI,GAAA;AAAAyI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}