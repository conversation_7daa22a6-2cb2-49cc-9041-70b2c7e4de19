{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport VirtualList from 'rc-virtual-list';\nimport * as React from 'react';\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport BodyLine from \"./BodyLine\";\nimport { GridContext, StaticContext } from \"./context\";\nvar Grid = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var data = props.data,\n    onScroll = props.onScroll;\n  var _useContext = useContext(TableContext, ['flattenColumns', 'onColumnResize', 'getRowKey', 'prefixCls', 'expandedKeys', 'childrenColumnName', 'scrollX', 'direction']),\n    flattenColumns = _useContext.flattenColumns,\n    onColumnResize = _useContext.onColumnResize,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    prefixCls = _useContext.prefixCls,\n    childrenColumnName = _useContext.childrenColumnName,\n    scrollX = _useContext.scrollX,\n    direction = _useContext.direction;\n  var _useContext2 = useContext(StaticContext),\n    sticky = _useContext2.sticky,\n    scrollY = _useContext2.scrollY,\n    listItemHeight = _useContext2.listItemHeight,\n    getComponent = _useContext2.getComponent,\n    onTablePropScroll = _useContext2.onScroll;\n\n  // =========================== Ref ============================\n  var listRef = React.useRef();\n\n  // =========================== Data ===========================\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // ========================== Column ==========================\n  var columnsWidth = React.useMemo(function () {\n    var total = 0;\n    return flattenColumns.map(function (_ref) {\n      var width = _ref.width,\n        key = _ref.key;\n      total += width;\n      return [key, width, total];\n    });\n  }, [flattenColumns]);\n  var columnsOffset = React.useMemo(function () {\n    return columnsWidth.map(function (colWidth) {\n      return colWidth[2];\n    });\n  }, [columnsWidth]);\n  React.useEffect(function () {\n    columnsWidth.forEach(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n        key = _ref3[0],\n        width = _ref3[1];\n      onColumnResize(key, width);\n    });\n  }, [columnsWidth]);\n\n  // =========================== Ref ============================\n  React.useImperativeHandle(ref, function () {\n    var _listRef$current2;\n    var obj = {\n      scrollTo: function scrollTo(config) {\n        var _listRef$current;\n        (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.scrollTo(config);\n      },\n      nativeElement: (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 ? void 0 : _listRef$current2.nativeElement\n    };\n    Object.defineProperty(obj, 'scrollLeft', {\n      get: function get() {\n        var _listRef$current3;\n        return ((_listRef$current3 = listRef.current) === null || _listRef$current3 === void 0 ? void 0 : _listRef$current3.getScrollInfo().x) || 0;\n      },\n      set: function set(value) {\n        var _listRef$current4;\n        (_listRef$current4 = listRef.current) === null || _listRef$current4 === void 0 || _listRef$current4.scrollTo({\n          left: value\n        });\n      }\n    });\n    return obj;\n  });\n\n  // ======================= Col/Row Span =======================\n  var getRowSpan = function getRowSpan(column, index) {\n    var _flattenData$index;\n    var record = (_flattenData$index = flattenData[index]) === null || _flattenData$index === void 0 ? void 0 : _flattenData$index.record;\n    var onCell = column.onCell;\n    if (onCell) {\n      var _cellProps$rowSpan;\n      var cellProps = onCell(record, index);\n      return (_cellProps$rowSpan = cellProps === null || cellProps === void 0 ? void 0 : cellProps.rowSpan) !== null && _cellProps$rowSpan !== void 0 ? _cellProps$rowSpan : 1;\n    }\n    return 1;\n  };\n  var extraRender = function extraRender(info) {\n    var start = info.start,\n      end = info.end,\n      getSize = info.getSize,\n      offsetY = info.offsetY;\n\n    // Do nothing if no data\n    if (end < 0) {\n      return null;\n    }\n\n    // Find first rowSpan column\n    var firstRowSpanColumns = flattenColumns.filter(\n    // rowSpan is 0\n    function (column) {\n      return getRowSpan(column, start) === 0;\n    });\n    var startIndex = start;\n    var _loop = function _loop(i) {\n      firstRowSpanColumns = firstRowSpanColumns.filter(function (column) {\n        return getRowSpan(column, i) === 0;\n      });\n      if (!firstRowSpanColumns.length) {\n        startIndex = i;\n        return 1; // break\n      }\n    };\n    for (var i = start; i >= 0; i -= 1) {\n      if (_loop(i)) break;\n    }\n\n    // Find last rowSpan column\n    var lastRowSpanColumns = flattenColumns.filter(\n    // rowSpan is not 1\n    function (column) {\n      return getRowSpan(column, end) !== 1;\n    });\n    var endIndex = end;\n    var _loop2 = function _loop2(_i) {\n      lastRowSpanColumns = lastRowSpanColumns.filter(function (column) {\n        return getRowSpan(column, _i) !== 1;\n      });\n      if (!lastRowSpanColumns.length) {\n        endIndex = Math.max(_i - 1, end);\n        return 1; // break\n      }\n    };\n    for (var _i = end; _i < flattenData.length; _i += 1) {\n      if (_loop2(_i)) break;\n    }\n\n    // Collect the line who has rowSpan\n    var spanLines = [];\n    var _loop3 = function _loop3(_i2) {\n      var item = flattenData[_i2];\n\n      // This code will never reach, just incase\n      if (!item) {\n        return 1; // continue\n      }\n      if (flattenColumns.some(function (column) {\n        return getRowSpan(column, _i2) > 1;\n      })) {\n        spanLines.push(_i2);\n      }\n    };\n    for (var _i2 = startIndex; _i2 <= endIndex; _i2 += 1) {\n      if (_loop3(_i2)) continue;\n    }\n\n    // Patch extra line on the page\n    var nodes = spanLines.map(function (index) {\n      var item = flattenData[index];\n      var rowKey = getRowKey(item.record, index);\n      var getHeight = function getHeight(rowSpan) {\n        var endItemIndex = index + rowSpan - 1;\n        var endItemKey = getRowKey(flattenData[endItemIndex].record, endItemIndex);\n        var sizeInfo = getSize(rowKey, endItemKey);\n        return sizeInfo.bottom - sizeInfo.top;\n      };\n      var sizeInfo = getSize(rowKey);\n      return /*#__PURE__*/React.createElement(BodyLine, {\n        key: index,\n        data: item,\n        rowKey: rowKey,\n        index: index,\n        style: {\n          top: -offsetY + sizeInfo.top\n        },\n        extra: true,\n        getHeight: getHeight\n      });\n    });\n    return nodes;\n  };\n\n  // ========================= Context ==========================\n  var gridContext = React.useMemo(function () {\n    return {\n      columnsOffset: columnsOffset\n    };\n  }, [columnsOffset]);\n\n  // ========================== Render ==========================\n  var tblPrefixCls = \"\".concat(prefixCls, \"-tbody\");\n\n  // default 'div' in rc-virtual-list\n  var wrapperComponent = getComponent(['body', 'wrapper']);\n\n  // ========================== Sticky Scroll Bar ==========================\n  var horizontalScrollBarStyle = {};\n  if (sticky) {\n    horizontalScrollBarStyle.position = 'sticky';\n    horizontalScrollBarStyle.bottom = 0;\n    if (_typeof(sticky) === 'object' && sticky.offsetScroll) {\n      horizontalScrollBarStyle.bottom = sticky.offsetScroll;\n    }\n  }\n  return /*#__PURE__*/React.createElement(GridContext.Provider, {\n    value: gridContext\n  }, /*#__PURE__*/React.createElement(VirtualList, {\n    fullHeight: false,\n    ref: listRef,\n    prefixCls: \"\".concat(tblPrefixCls, \"-virtual\"),\n    styles: {\n      horizontalScrollBar: horizontalScrollBarStyle\n    },\n    className: tblPrefixCls,\n    height: scrollY,\n    itemHeight: listItemHeight || 24,\n    data: flattenData,\n    itemKey: function itemKey(item) {\n      return getRowKey(item.record);\n    },\n    component: wrapperComponent,\n    scrollWidth: scrollX,\n    direction: direction,\n    onVirtualScroll: function onVirtualScroll(_ref4) {\n      var _listRef$current5;\n      var x = _ref4.x;\n      onScroll({\n        currentTarget: (_listRef$current5 = listRef.current) === null || _listRef$current5 === void 0 ? void 0 : _listRef$current5.nativeElement,\n        scrollLeft: x\n      });\n    },\n    onScroll: onTablePropScroll,\n    extraRender: extraRender\n  }, function (item, index, itemProps) {\n    var rowKey = getRowKey(item.record, index);\n    return /*#__PURE__*/React.createElement(BodyLine, {\n      data: item,\n      rowKey: rowKey,\n      index: index,\n      style: itemProps.style\n    });\n  }));\n});\nvar ResponseGrid = responseImmutable(Grid);\nif (process.env.NODE_ENV !== 'production') {\n  ResponseGrid.displayName = 'ResponseGrid';\n}\nexport default ResponseGrid;", "map": {"version": 3, "names": ["_typeof", "_slicedToArray", "useContext", "VirtualList", "React", "TableContext", "responseImmutable", "useFlattenRecords", "BodyLine", "GridContext", "StaticContext", "Grid", "forwardRef", "props", "ref", "data", "onScroll", "_useContext", "flattenColumns", "onColumnResize", "getRowKey", "expandedKeys", "prefixCls", "childrenColumnName", "scrollX", "direction", "_useContext2", "sticky", "scrollY", "listItemHeight", "getComponent", "onTablePropScroll", "listRef", "useRef", "flattenData", "columnsWidth", "useMemo", "total", "map", "_ref", "width", "key", "columnsOffset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "for<PERSON>ach", "_ref2", "_ref3", "useImperativeHandle", "_listRef$current2", "obj", "scrollTo", "config", "_listRef$current", "current", "nativeElement", "Object", "defineProperty", "get", "_listRef$current3", "getScrollInfo", "x", "set", "value", "_listRef$current4", "left", "getRowSpan", "column", "index", "_flattenData$index", "record", "onCell", "_cellProps$rowSpan", "cellProps", "rowSpan", "extraRender", "info", "start", "end", "getSize", "offsetY", "firstRowSpanColumns", "filter", "startIndex", "_loop", "i", "length", "lastRowSpanColumns", "endIndex", "_loop2", "_i", "Math", "max", "spanLines", "_loop3", "_i2", "item", "some", "push", "nodes", "<PERSON><PERSON><PERSON>", "getHeight", "endItemIndex", "endItemKey", "sizeInfo", "bottom", "top", "createElement", "style", "extra", "gridContext", "tblPrefixCls", "concat", "wrapperComponent", "horizontalScrollBarStyle", "position", "offsetScroll", "Provider", "fullHeight", "styles", "horizontalScrollBar", "className", "height", "itemHeight", "itemKey", "component", "scrollWidth", "onVirtualScroll", "_ref4", "_listRef$current5", "currentTarget", "scrollLeft", "itemProps", "ResponseGrid", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-table/es/VirtualTable/BodyGrid.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport VirtualList from 'rc-virtual-list';\nimport * as React from 'react';\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport BodyLine from \"./BodyLine\";\nimport { GridContext, StaticContext } from \"./context\";\nvar Grid = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var data = props.data,\n    onScroll = props.onScroll;\n  var _useContext = useContext(TableContext, ['flattenColumns', 'onColumnResize', 'getRowKey', 'prefixCls', 'expandedKeys', 'childrenColumnName', 'scrollX', 'direction']),\n    flattenColumns = _useContext.flattenColumns,\n    onColumnResize = _useContext.onColumnResize,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    prefixCls = _useContext.prefixCls,\n    childrenColumnName = _useContext.childrenColumnName,\n    scrollX = _useContext.scrollX,\n    direction = _useContext.direction;\n  var _useContext2 = useContext(StaticContext),\n    sticky = _useContext2.sticky,\n    scrollY = _useContext2.scrollY,\n    listItemHeight = _useContext2.listItemHeight,\n    getComponent = _useContext2.getComponent,\n    onTablePropScroll = _useContext2.onScroll;\n\n  // =========================== Ref ============================\n  var listRef = React.useRef();\n\n  // =========================== Data ===========================\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // ========================== Column ==========================\n  var columnsWidth = React.useMemo(function () {\n    var total = 0;\n    return flattenColumns.map(function (_ref) {\n      var width = _ref.width,\n        key = _ref.key;\n      total += width;\n      return [key, width, total];\n    });\n  }, [flattenColumns]);\n  var columnsOffset = React.useMemo(function () {\n    return columnsWidth.map(function (colWidth) {\n      return colWidth[2];\n    });\n  }, [columnsWidth]);\n  React.useEffect(function () {\n    columnsWidth.forEach(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n        key = _ref3[0],\n        width = _ref3[1];\n      onColumnResize(key, width);\n    });\n  }, [columnsWidth]);\n\n  // =========================== Ref ============================\n  React.useImperativeHandle(ref, function () {\n    var _listRef$current2;\n    var obj = {\n      scrollTo: function scrollTo(config) {\n        var _listRef$current;\n        (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.scrollTo(config);\n      },\n      nativeElement: (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 ? void 0 : _listRef$current2.nativeElement\n    };\n    Object.defineProperty(obj, 'scrollLeft', {\n      get: function get() {\n        var _listRef$current3;\n        return ((_listRef$current3 = listRef.current) === null || _listRef$current3 === void 0 ? void 0 : _listRef$current3.getScrollInfo().x) || 0;\n      },\n      set: function set(value) {\n        var _listRef$current4;\n        (_listRef$current4 = listRef.current) === null || _listRef$current4 === void 0 || _listRef$current4.scrollTo({\n          left: value\n        });\n      }\n    });\n    return obj;\n  });\n\n  // ======================= Col/Row Span =======================\n  var getRowSpan = function getRowSpan(column, index) {\n    var _flattenData$index;\n    var record = (_flattenData$index = flattenData[index]) === null || _flattenData$index === void 0 ? void 0 : _flattenData$index.record;\n    var onCell = column.onCell;\n    if (onCell) {\n      var _cellProps$rowSpan;\n      var cellProps = onCell(record, index);\n      return (_cellProps$rowSpan = cellProps === null || cellProps === void 0 ? void 0 : cellProps.rowSpan) !== null && _cellProps$rowSpan !== void 0 ? _cellProps$rowSpan : 1;\n    }\n    return 1;\n  };\n  var extraRender = function extraRender(info) {\n    var start = info.start,\n      end = info.end,\n      getSize = info.getSize,\n      offsetY = info.offsetY;\n\n    // Do nothing if no data\n    if (end < 0) {\n      return null;\n    }\n\n    // Find first rowSpan column\n    var firstRowSpanColumns = flattenColumns.filter(\n    // rowSpan is 0\n    function (column) {\n      return getRowSpan(column, start) === 0;\n    });\n    var startIndex = start;\n    var _loop = function _loop(i) {\n      firstRowSpanColumns = firstRowSpanColumns.filter(function (column) {\n        return getRowSpan(column, i) === 0;\n      });\n      if (!firstRowSpanColumns.length) {\n        startIndex = i;\n        return 1; // break\n      }\n    };\n    for (var i = start; i >= 0; i -= 1) {\n      if (_loop(i)) break;\n    }\n\n    // Find last rowSpan column\n    var lastRowSpanColumns = flattenColumns.filter(\n    // rowSpan is not 1\n    function (column) {\n      return getRowSpan(column, end) !== 1;\n    });\n    var endIndex = end;\n    var _loop2 = function _loop2(_i) {\n      lastRowSpanColumns = lastRowSpanColumns.filter(function (column) {\n        return getRowSpan(column, _i) !== 1;\n      });\n      if (!lastRowSpanColumns.length) {\n        endIndex = Math.max(_i - 1, end);\n        return 1; // break\n      }\n    };\n    for (var _i = end; _i < flattenData.length; _i += 1) {\n      if (_loop2(_i)) break;\n    }\n\n    // Collect the line who has rowSpan\n    var spanLines = [];\n    var _loop3 = function _loop3(_i2) {\n      var item = flattenData[_i2];\n\n      // This code will never reach, just incase\n      if (!item) {\n        return 1; // continue\n      }\n      if (flattenColumns.some(function (column) {\n        return getRowSpan(column, _i2) > 1;\n      })) {\n        spanLines.push(_i2);\n      }\n    };\n    for (var _i2 = startIndex; _i2 <= endIndex; _i2 += 1) {\n      if (_loop3(_i2)) continue;\n    }\n\n    // Patch extra line on the page\n    var nodes = spanLines.map(function (index) {\n      var item = flattenData[index];\n      var rowKey = getRowKey(item.record, index);\n      var getHeight = function getHeight(rowSpan) {\n        var endItemIndex = index + rowSpan - 1;\n        var endItemKey = getRowKey(flattenData[endItemIndex].record, endItemIndex);\n        var sizeInfo = getSize(rowKey, endItemKey);\n        return sizeInfo.bottom - sizeInfo.top;\n      };\n      var sizeInfo = getSize(rowKey);\n      return /*#__PURE__*/React.createElement(BodyLine, {\n        key: index,\n        data: item,\n        rowKey: rowKey,\n        index: index,\n        style: {\n          top: -offsetY + sizeInfo.top\n        },\n        extra: true,\n        getHeight: getHeight\n      });\n    });\n    return nodes;\n  };\n\n  // ========================= Context ==========================\n  var gridContext = React.useMemo(function () {\n    return {\n      columnsOffset: columnsOffset\n    };\n  }, [columnsOffset]);\n\n  // ========================== Render ==========================\n  var tblPrefixCls = \"\".concat(prefixCls, \"-tbody\");\n\n  // default 'div' in rc-virtual-list\n  var wrapperComponent = getComponent(['body', 'wrapper']);\n\n  // ========================== Sticky Scroll Bar ==========================\n  var horizontalScrollBarStyle = {};\n  if (sticky) {\n    horizontalScrollBarStyle.position = 'sticky';\n    horizontalScrollBarStyle.bottom = 0;\n    if (_typeof(sticky) === 'object' && sticky.offsetScroll) {\n      horizontalScrollBarStyle.bottom = sticky.offsetScroll;\n    }\n  }\n  return /*#__PURE__*/React.createElement(GridContext.Provider, {\n    value: gridContext\n  }, /*#__PURE__*/React.createElement(VirtualList, {\n    fullHeight: false,\n    ref: listRef,\n    prefixCls: \"\".concat(tblPrefixCls, \"-virtual\"),\n    styles: {\n      horizontalScrollBar: horizontalScrollBarStyle\n    },\n    className: tblPrefixCls,\n    height: scrollY,\n    itemHeight: listItemHeight || 24,\n    data: flattenData,\n    itemKey: function itemKey(item) {\n      return getRowKey(item.record);\n    },\n    component: wrapperComponent,\n    scrollWidth: scrollX,\n    direction: direction,\n    onVirtualScroll: function onVirtualScroll(_ref4) {\n      var _listRef$current5;\n      var x = _ref4.x;\n      onScroll({\n        currentTarget: (_listRef$current5 = listRef.current) === null || _listRef$current5 === void 0 ? void 0 : _listRef$current5.nativeElement,\n        scrollLeft: x\n      });\n    },\n    onScroll: onTablePropScroll,\n    extraRender: extraRender\n  }, function (item, index, itemProps) {\n    var rowKey = getRowKey(item.record, index);\n    return /*#__PURE__*/React.createElement(BodyLine, {\n      data: item,\n      rowKey: rowKey,\n      index: index,\n      style: itemProps.style\n    });\n  }));\n});\nvar ResponseGrid = responseImmutable(Grid);\nif (process.env.NODE_ENV !== 'production') {\n  ResponseGrid.displayName = 'ResponseGrid';\n}\nexport default ResponseGrid;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,WAAW,MAAM,iBAAiB;AACzC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,iBAAiB,QAAQ,yBAAyB;AACzE,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,WAAW,EAAEC,aAAa,QAAQ,WAAW;AACtD,IAAIC,IAAI,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;EAC3B,IAAIC,WAAW,GAAGf,UAAU,CAACG,YAAY,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IACtKa,cAAc,GAAGD,WAAW,CAACC,cAAc;IAC3CC,cAAc,GAAGF,WAAW,CAACE,cAAc;IAC3CC,SAAS,GAAGH,WAAW,CAACG,SAAS;IACjCC,YAAY,GAAGJ,WAAW,CAACI,YAAY;IACvCC,SAAS,GAAGL,WAAW,CAACK,SAAS;IACjCC,kBAAkB,GAAGN,WAAW,CAACM,kBAAkB;IACnDC,OAAO,GAAGP,WAAW,CAACO,OAAO;IAC7BC,SAAS,GAAGR,WAAW,CAACQ,SAAS;EACnC,IAAIC,YAAY,GAAGxB,UAAU,CAACQ,aAAa,CAAC;IAC1CiB,MAAM,GAAGD,YAAY,CAACC,MAAM;IAC5BC,OAAO,GAAGF,YAAY,CAACE,OAAO;IAC9BC,cAAc,GAAGH,YAAY,CAACG,cAAc;IAC5CC,YAAY,GAAGJ,YAAY,CAACI,YAAY;IACxCC,iBAAiB,GAAGL,YAAY,CAACV,QAAQ;;EAE3C;EACA,IAAIgB,OAAO,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,CAAC;;EAE5B;EACA,IAAIC,WAAW,GAAG3B,iBAAiB,CAACQ,IAAI,EAAEQ,kBAAkB,EAAEF,YAAY,EAAED,SAAS,CAAC;;EAEtF;EACA,IAAIe,YAAY,GAAG/B,KAAK,CAACgC,OAAO,CAAC,YAAY;IAC3C,IAAIC,KAAK,GAAG,CAAC;IACb,OAAOnB,cAAc,CAACoB,GAAG,CAAC,UAAUC,IAAI,EAAE;MACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;QACpBC,GAAG,GAAGF,IAAI,CAACE,GAAG;MAChBJ,KAAK,IAAIG,KAAK;MACd,OAAO,CAACC,GAAG,EAAED,KAAK,EAAEH,KAAK,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnB,cAAc,CAAC,CAAC;EACpB,IAAIwB,aAAa,GAAGtC,KAAK,CAACgC,OAAO,CAAC,YAAY;IAC5C,OAAOD,YAAY,CAACG,GAAG,CAAC,UAAUK,QAAQ,EAAE;MAC1C,OAAOA,QAAQ,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;EAClB/B,KAAK,CAACwC,SAAS,CAAC,YAAY;IAC1BT,YAAY,CAACU,OAAO,CAAC,UAAUC,KAAK,EAAE;MACpC,IAAIC,KAAK,GAAG9C,cAAc,CAAC6C,KAAK,EAAE,CAAC,CAAC;QAClCL,GAAG,GAAGM,KAAK,CAAC,CAAC,CAAC;QACdP,KAAK,GAAGO,KAAK,CAAC,CAAC,CAAC;MAClB5B,cAAc,CAACsB,GAAG,EAAED,KAAK,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;;EAElB;EACA/B,KAAK,CAAC4C,mBAAmB,CAAClC,GAAG,EAAE,YAAY;IACzC,IAAImC,iBAAiB;IACrB,IAAIC,GAAG,GAAG;MACRC,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAE;QAClC,IAAIC,gBAAgB;QACpB,CAACA,gBAAgB,GAAGrB,OAAO,CAACsB,OAAO,MAAM,IAAI,IAAID,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAACF,QAAQ,CAACC,MAAM,CAAC;MACnH,CAAC;MACDG,aAAa,EAAE,CAACN,iBAAiB,GAAGjB,OAAO,CAACsB,OAAO,MAAM,IAAI,IAAIL,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACM;IAC7H,CAAC;IACDC,MAAM,CAACC,cAAc,CAACP,GAAG,EAAE,YAAY,EAAE;MACvCQ,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClB,IAAIC,iBAAiB;QACrB,OAAO,CAAC,CAACA,iBAAiB,GAAG3B,OAAO,CAACsB,OAAO,MAAM,IAAI,IAAIK,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACC,aAAa,CAAC,CAAC,CAACC,CAAC,KAAK,CAAC;MAC7I,CAAC;MACDC,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;QACvB,IAAIC,iBAAiB;QACrB,CAACA,iBAAiB,GAAGhC,OAAO,CAACsB,OAAO,MAAM,IAAI,IAAIU,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACb,QAAQ,CAAC;UAC3Gc,IAAI,EAAEF;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOb,GAAG;EACZ,CAAC,CAAC;;EAEF;EACA,IAAIgB,UAAU,GAAG,SAASA,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAClD,IAAIC,kBAAkB;IACtB,IAAIC,MAAM,GAAG,CAACD,kBAAkB,GAAGnC,WAAW,CAACkC,KAAK,CAAC,MAAM,IAAI,IAAIC,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,MAAM;IACrI,IAAIC,MAAM,GAAGJ,MAAM,CAACI,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACV,IAAIC,kBAAkB;MACtB,IAAIC,SAAS,GAAGF,MAAM,CAACD,MAAM,EAAEF,KAAK,CAAC;MACrC,OAAO,CAACI,kBAAkB,GAAGC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,OAAO,MAAM,IAAI,IAAIF,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC;IAC1K;IACA,OAAO,CAAC;EACV,CAAC;EACD,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;IAC3C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpBC,GAAG,GAAGF,IAAI,CAACE,GAAG;MACdC,OAAO,GAAGH,IAAI,CAACG,OAAO;MACtBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;;IAExB;IACA,IAAIF,GAAG,GAAG,CAAC,EAAE;MACX,OAAO,IAAI;IACb;;IAEA;IACA,IAAIG,mBAAmB,GAAG/D,cAAc,CAACgE,MAAM;IAC/C;IACA,UAAUf,MAAM,EAAE;MAChB,OAAOD,UAAU,CAACC,MAAM,EAAEU,KAAK,CAAC,KAAK,CAAC;IACxC,CAAC,CAAC;IACF,IAAIM,UAAU,GAAGN,KAAK;IACtB,IAAIO,KAAK,GAAG,SAASA,KAAKA,CAACC,CAAC,EAAE;MAC5BJ,mBAAmB,GAAGA,mBAAmB,CAACC,MAAM,CAAC,UAAUf,MAAM,EAAE;QACjE,OAAOD,UAAU,CAACC,MAAM,EAAEkB,CAAC,CAAC,KAAK,CAAC;MACpC,CAAC,CAAC;MACF,IAAI,CAACJ,mBAAmB,CAACK,MAAM,EAAE;QAC/BH,UAAU,GAAGE,CAAC;QACd,OAAO,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,KAAK,IAAIA,CAAC,GAAGR,KAAK,EAAEQ,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAClC,IAAID,KAAK,CAACC,CAAC,CAAC,EAAE;IAChB;;IAEA;IACA,IAAIE,kBAAkB,GAAGrE,cAAc,CAACgE,MAAM;IAC9C;IACA,UAAUf,MAAM,EAAE;MAChB,OAAOD,UAAU,CAACC,MAAM,EAAEW,GAAG,CAAC,KAAK,CAAC;IACtC,CAAC,CAAC;IACF,IAAIU,QAAQ,GAAGV,GAAG;IAClB,IAAIW,MAAM,GAAG,SAASA,MAAMA,CAACC,EAAE,EAAE;MAC/BH,kBAAkB,GAAGA,kBAAkB,CAACL,MAAM,CAAC,UAAUf,MAAM,EAAE;QAC/D,OAAOD,UAAU,CAACC,MAAM,EAAEuB,EAAE,CAAC,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,IAAI,CAACH,kBAAkB,CAACD,MAAM,EAAE;QAC9BE,QAAQ,GAAGG,IAAI,CAACC,GAAG,CAACF,EAAE,GAAG,CAAC,EAAEZ,GAAG,CAAC;QAChC,OAAO,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,KAAK,IAAIY,EAAE,GAAGZ,GAAG,EAAEY,EAAE,GAAGxD,WAAW,CAACoD,MAAM,EAAEI,EAAE,IAAI,CAAC,EAAE;MACnD,IAAID,MAAM,CAACC,EAAE,CAAC,EAAE;IAClB;;IAEA;IACA,IAAIG,SAAS,GAAG,EAAE;IAClB,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAE;MAChC,IAAIC,IAAI,GAAG9D,WAAW,CAAC6D,GAAG,CAAC;;MAE3B;MACA,IAAI,CAACC,IAAI,EAAE;QACT,OAAO,CAAC,CAAC,CAAC;MACZ;MACA,IAAI9E,cAAc,CAAC+E,IAAI,CAAC,UAAU9B,MAAM,EAAE;QACxC,OAAOD,UAAU,CAACC,MAAM,EAAE4B,GAAG,CAAC,GAAG,CAAC;MACpC,CAAC,CAAC,EAAE;QACFF,SAAS,CAACK,IAAI,CAACH,GAAG,CAAC;MACrB;IACF,CAAC;IACD,KAAK,IAAIA,GAAG,GAAGZ,UAAU,EAAEY,GAAG,IAAIP,QAAQ,EAAEO,GAAG,IAAI,CAAC,EAAE;MACpD,IAAID,MAAM,CAACC,GAAG,CAAC,EAAE;IACnB;;IAEA;IACA,IAAII,KAAK,GAAGN,SAAS,CAACvD,GAAG,CAAC,UAAU8B,KAAK,EAAE;MACzC,IAAI4B,IAAI,GAAG9D,WAAW,CAACkC,KAAK,CAAC;MAC7B,IAAIgC,MAAM,GAAGhF,SAAS,CAAC4E,IAAI,CAAC1B,MAAM,EAAEF,KAAK,CAAC;MAC1C,IAAIiC,SAAS,GAAG,SAASA,SAASA,CAAC3B,OAAO,EAAE;QAC1C,IAAI4B,YAAY,GAAGlC,KAAK,GAAGM,OAAO,GAAG,CAAC;QACtC,IAAI6B,UAAU,GAAGnF,SAAS,CAACc,WAAW,CAACoE,YAAY,CAAC,CAAChC,MAAM,EAAEgC,YAAY,CAAC;QAC1E,IAAIE,QAAQ,GAAGzB,OAAO,CAACqB,MAAM,EAAEG,UAAU,CAAC;QAC1C,OAAOC,QAAQ,CAACC,MAAM,GAAGD,QAAQ,CAACE,GAAG;MACvC,CAAC;MACD,IAAIF,QAAQ,GAAGzB,OAAO,CAACqB,MAAM,CAAC;MAC9B,OAAO,aAAahG,KAAK,CAACuG,aAAa,CAACnG,QAAQ,EAAE;QAChDiC,GAAG,EAAE2B,KAAK;QACVrD,IAAI,EAAEiF,IAAI;QACVI,MAAM,EAAEA,MAAM;QACdhC,KAAK,EAAEA,KAAK;QACZwC,KAAK,EAAE;UACLF,GAAG,EAAE,CAAC1B,OAAO,GAAGwB,QAAQ,CAACE;QAC3B,CAAC;QACDG,KAAK,EAAE,IAAI;QACXR,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOF,KAAK;EACd,CAAC;;EAED;EACA,IAAIW,WAAW,GAAG1G,KAAK,CAACgC,OAAO,CAAC,YAAY;IAC1C,OAAO;MACLM,aAAa,EAAEA;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,IAAIqE,YAAY,GAAG,EAAE,CAACC,MAAM,CAAC1F,SAAS,EAAE,QAAQ,CAAC;;EAEjD;EACA,IAAI2F,gBAAgB,GAAGnF,YAAY,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;;EAExD;EACA,IAAIoF,wBAAwB,GAAG,CAAC,CAAC;EACjC,IAAIvF,MAAM,EAAE;IACVuF,wBAAwB,CAACC,QAAQ,GAAG,QAAQ;IAC5CD,wBAAwB,CAACT,MAAM,GAAG,CAAC;IACnC,IAAIzG,OAAO,CAAC2B,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAACyF,YAAY,EAAE;MACvDF,wBAAwB,CAACT,MAAM,GAAG9E,MAAM,CAACyF,YAAY;IACvD;EACF;EACA,OAAO,aAAahH,KAAK,CAACuG,aAAa,CAAClG,WAAW,CAAC4G,QAAQ,EAAE;IAC5DtD,KAAK,EAAE+C;EACT,CAAC,EAAE,aAAa1G,KAAK,CAACuG,aAAa,CAACxG,WAAW,EAAE;IAC/CmH,UAAU,EAAE,KAAK;IACjBxG,GAAG,EAAEkB,OAAO;IACZV,SAAS,EAAE,EAAE,CAAC0F,MAAM,CAACD,YAAY,EAAE,UAAU,CAAC;IAC9CQ,MAAM,EAAE;MACNC,mBAAmB,EAAEN;IACvB,CAAC;IACDO,SAAS,EAAEV,YAAY;IACvBW,MAAM,EAAE9F,OAAO;IACf+F,UAAU,EAAE9F,cAAc,IAAI,EAAE;IAChCd,IAAI,EAAEmB,WAAW;IACjB0F,OAAO,EAAE,SAASA,OAAOA,CAAC5B,IAAI,EAAE;MAC9B,OAAO5E,SAAS,CAAC4E,IAAI,CAAC1B,MAAM,CAAC;IAC/B,CAAC;IACDuD,SAAS,EAAEZ,gBAAgB;IAC3Ba,WAAW,EAAEtG,OAAO;IACpBC,SAAS,EAAEA,SAAS;IACpBsG,eAAe,EAAE,SAASA,eAAeA,CAACC,KAAK,EAAE;MAC/C,IAAIC,iBAAiB;MACrB,IAAIpE,CAAC,GAAGmE,KAAK,CAACnE,CAAC;MACf7C,QAAQ,CAAC;QACPkH,aAAa,EAAE,CAACD,iBAAiB,GAAGjG,OAAO,CAACsB,OAAO,MAAM,IAAI,IAAI2E,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC1E,aAAa;QACxI4E,UAAU,EAAEtE;MACd,CAAC,CAAC;IACJ,CAAC;IACD7C,QAAQ,EAAEe,iBAAiB;IAC3B4C,WAAW,EAAEA;EACf,CAAC,EAAE,UAAUqB,IAAI,EAAE5B,KAAK,EAAEgE,SAAS,EAAE;IACnC,IAAIhC,MAAM,GAAGhF,SAAS,CAAC4E,IAAI,CAAC1B,MAAM,EAAEF,KAAK,CAAC;IAC1C,OAAO,aAAahE,KAAK,CAACuG,aAAa,CAACnG,QAAQ,EAAE;MAChDO,IAAI,EAAEiF,IAAI;MACVI,MAAM,EAAEA,MAAM;MACdhC,KAAK,EAAEA,KAAK;MACZwC,KAAK,EAAEwB,SAAS,CAACxB;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIyB,YAAY,GAAG/H,iBAAiB,CAACK,IAAI,CAAC;AAC1C,IAAI2H,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,YAAY,CAACI,WAAW,GAAG,cAAc;AAC3C;AACA,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}