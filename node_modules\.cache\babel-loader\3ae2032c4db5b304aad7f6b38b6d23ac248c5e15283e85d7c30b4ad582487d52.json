{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n    rsi: 'changli/cloud/v2x/rsu/rsi',\n    // 添加 RSI 主题\n    spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = newPos => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = newRotation => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null); // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      const messageData = JSON.parse(message.toString());\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n\n        // 检查设备时间戳\n        const deviceMac = messageData.mac;\n        const messageTimestamp = messageData.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n        const rsuid = messageData.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle\n          });\n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n      // 处理SPAT消息（红绿灯状态）\n      if (topic === MQTT_CONFIG.topics.spat && messageData.type === 'SPAT') {\n        console.log('收到SPAT消息:', messageData);\n        const spatData = messageData.data;\n        if (spatData) {\n          const rsuId = spatData.rsuId;\n          const intersections = spatData.intersections || [];\n\n          // 处理每个路口的信息\n          intersections.forEach(intersection => {\n            const interId = intersection.interId;\n            const interRegion = intersection.interRegion;\n            const phases = intersection.phases || [];\n\n            // 收集该路口的所有相位信息\n            const intersectionPhases = [];\n\n            // 处理每个相位的信息\n            phases.forEach(phase => {\n              const trafficGroup = phase.trafficGroup; // 路口编号\n              const phaseId = phase.phaseId; // 相位编号\n              const trafficDirec = phase.trafficDirec; // 方向（L-左转，S-直行，R-右转）\n              const remainTime = phase.remainTime; // 倒计时剩余秒数\n              const trafficLight = phase.trafficLight; // 交通灯颜色（R-红色，Y-黄色，G-绿色）\n\n              // 将相位信息添加到集合中\n              intersectionPhases.push({\n                trafficGroup,\n                phaseId,\n                trafficDirec,\n                remainTime,\n                trafficLight\n              });\n\n              // 显示红绿灯信息\n              console.log('红绿灯信息:', {\n                路口ID: interId,\n                // 区域ID: interRegion,\n                路口编号: trafficGroup,\n                相位编号: phaseId,\n                方向: trafficDirec === 'L' ? '左转' : trafficDirec === 'S' ? '直行' : trafficDirec === 'R' ? '右转' : '未知',\n                剩余时间: remainTime,\n                灯色: trafficLight === 'R' ? '红灯' : trafficLight === 'Y' ? '黄灯' : trafficLight === 'G' ? '绿灯' : '未知'\n              });\n            });\n\n            // 更新红绿灯状态\n            trafficLightStates.set(interId, {\n              rsuId,\n              interId,\n              phases: intersectionPhases\n            });\n\n            // 发送SPAT消息到其他组件\n            window.postMessage({\n              type: 'SPAT',\n              data: {\n                rsuId,\n                interId,\n                phases: intersectionPhases\n              }\n            }, '*');\n          });\n        } else {\n          console.warn('SPAT消息中缺少data字段:', messageData);\n        }\n        return;\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.topics.rsi && messageData.type === 'RSI') {\n        console.log('收到RSI消息:', messageData);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: messageData.data\n        }, '*');\n        const rsiData = messageData.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.topics.scene && messageData.type === 'SCENE') {\n        console.log('收到场景事件消息:', messageData);\n        const sceneData = messageData.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 100, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理场景\n      if (scene) {\n        while (scene.children.length > 0) {\n          scene.remove(scene.children[0]);\n        }\n      }\n\n      // 8. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u8DEF\\u53E3\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u8DEF\\u53E3\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1286,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1275,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"AzySYBOlkW5aLZL+qBbtARrnYic=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`), loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`)]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse(child => {\n      if (child.isMesh) {\n        const newMaterial = new THREE.MeshStandardMaterial({\n          // child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // 保留原始贴图\n        if (child.material.map) {\n          newMaterial.map = child.material.map;\n        }\n        child.materia = newMaterial;\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(3, 3, 3);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n      }\n    });\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n\n  // 将标记添加到场景中\n  scene.add(sprite);\n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!preloadedTrafficLightModel || !scene) {\n    console.error('红绿灯模型或场景未初始化');\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n\n      // 创建红绿灯模型\n      const trafficLightModel = preloadedTrafficLightModel.clone();\n\n      // 设置位置，离地面高度为5米\n      trafficLightModel.position.set(modelPos.x, 5, -modelPos.y);\n\n      // 添加交互所需的信息\n      trafficLightModel.userData = {\n        type: 'trafficLight',\n        interId: intersection.interId,\n        name: intersection.name\n      };\n\n      // 遍历模型的所有子对象，添加相同的userData\n      trafficLightModel.traverse(child => {\n        if (child.isMesh) {\n          child.userData = {\n            type: 'trafficLight',\n            interId: intersection.interId,\n            name: intersection.name\n          };\n        }\n      });\n\n      // 将红绿灯添加到场景中\n      scene.add(trafficLightModel);\n\n      // 存储红绿灯引用\n      trafficLightsMap.set(intersection.interId, {\n        model: trafficLightModel,\n        intersection: intersection,\n        position: modelPos\n      });\n      console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n    }\n  });\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "lastPosition", "lastRotation", "ALPHA", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "rsi", "spat", "BASE_URL", "vehicleModels", "Map", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "console", "log", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "updateMatrix", "updateMatrixWorld", "相机位置", "toArray", "目标点", "handleMqttMessage", "topic", "message", "toString", "messageData", "JSON", "parse", "type", "_messageData$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "get", "设备MAC", "消息时间戳", "最新时间戳", "时间戳", "是否为最新", "participants", "rsuid", "now", "Date", "for<PERSON>ach", "participant", "id", "partPtcId", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "newPosition", "Vector3", "vehicleObj", "newVehicleModel", "is<PERSON><PERSON>", "toFixed", "filteredPosition", "spatData", "rsuId", "interRegion", "intersectionPhases", "phase", "trafficGroup", "phaseId", "trafficDirec", "remainTime", "trafficLight", "push", "路口ID", "路口编号", "相位编号", "方向", "剩余时间", "灯色", "postMessage", "warn", "rsiData", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "事件ID", "事件类型", "事件说明", "开始时间", "结束时间", "位置", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "payload", "stringify", "onerror", "onclose", "setTimeout", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "length", "xhr", "loaded", "total", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "handleMainVehicleChange", "intervalId", "setInterval", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "size", "bordered", "dropdownStyle", "maxHeight", "ref", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "trafficLightGltf", "all", "loadAsync", "materia", "types", "文本", "颜色", "createTrafficLights", "converterInstance", "lightObj", "trafficLightModel", "userData", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n    rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n    spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = (newPos) => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = (newRotation) => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);  // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    \n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      const messageData = JSON.parse(message.toString());\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        // 检查设备时间戳\n        const deviceMac = messageData.mac;\n        const messageTimestamp = messageData.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n          return;\n        }\n        \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        \n        const participants = messageData.data?.participants || [];\n        const rsuid = messageData.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle\n          });\n          \n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      // 处理SPAT消息（红绿灯状态）\n      if (topic === MQTT_CONFIG.topics.spat && messageData.type === 'SPAT') {\n        console.log('收到SPAT消息:', messageData);\n        \n        const spatData = messageData.data;\n        if (spatData) {\n          const rsuId = spatData.rsuId;\n          const intersections = spatData.intersections || [];\n          \n          // 处理每个路口的信息\n          intersections.forEach(intersection => {\n            const interId = intersection.interId;\n            const interRegion = intersection.interRegion;\n            const phases = intersection.phases || [];\n            \n            // 收集该路口的所有相位信息\n            const intersectionPhases = [];\n            \n            // 处理每个相位的信息\n            phases.forEach(phase => {\n              const trafficGroup = phase.trafficGroup; // 路口编号\n              const phaseId = phase.phaseId;          // 相位编号\n              const trafficDirec = phase.trafficDirec; // 方向（L-左转，S-直行，R-右转）\n              const remainTime = phase.remainTime;    // 倒计时剩余秒数\n              const trafficLight = phase.trafficLight; // 交通灯颜色（R-红色，Y-黄色，G-绿色）\n              \n              // 将相位信息添加到集合中\n              intersectionPhases.push({\n                trafficGroup,\n                phaseId,\n                trafficDirec,\n                remainTime,\n                trafficLight\n              });\n              \n              // 显示红绿灯信息\n              console.log('红绿灯信息:', {\n                路口ID: interId,\n                // 区域ID: interRegion,\n                路口编号: trafficGroup,\n                相位编号: phaseId,\n                方向: trafficDirec === 'L' ? '左转' : trafficDirec === 'S' ? '直行' : trafficDirec === 'R' ? '右转' : '未知',\n                剩余时间: remainTime,\n                灯色: trafficLight === 'R' ? '红灯' : trafficLight === 'Y' ? '黄灯' : trafficLight === 'G' ? '绿灯' : '未知'\n              });\n            });\n            \n            // 更新红绿灯状态\n            trafficLightStates.set(interId, {\n              rsuId,\n              interId,\n              phases: intersectionPhases\n            });\n            \n            // 发送SPAT消息到其他组件\n            window.postMessage({\n              type: 'SPAT',\n              data: {\n                rsuId,\n                interId,\n                phases: intersectionPhases\n              }\n            }, '*');\n          });\n        } else {\n          console.warn('SPAT消息中缺少data字段:', messageData);\n        }\n        \n        return;\n      }\n      \n\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.topics.rsi && messageData.type === 'RSI') {\n        console.log('收到RSI消息:', messageData);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: messageData.data\n        }, '*');\n        \n        const rsiData = messageData.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        \n        return;\n      }\n      \n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.topics.scene && messageData.type === 'SCENE') {\n        console.log('收到场景事件消息:', messageData);\n        \n        const sceneData = messageData.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      } \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          100,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理场景\n      if (scene) {\n        while(scene.children.length > 0) { \n          scene.remove(scene.children[0]); \n        }\n      }\n      \n      // 8. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  return (\n    <>\n      <span style={labelStyle}>路口选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择路口位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/people.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        const newMaterial = new THREE.MeshStandardMaterial({\n        // child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // 保留原始贴图\n        if (child.material.map) {\n          newMaterial.map = child.material.map;\n        }\n        child.materia = newMaterial;\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n    \n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(3, 3, 3);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!preloadedTrafficLightModel || !scene) {\n    console.error('红绿灯模型或场景未初始化');\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      // 创建红绿灯模型\n      const trafficLightModel = preloadedTrafficLightModel.clone();\n      \n      // 设置位置，离地面高度为5米\n      trafficLightModel.position.set(modelPos.x, 5, -modelPos.y);\n      \n      // 添加交互所需的信息\n      trafficLightModel.userData = {\n        type: 'trafficLight',\n        interId: intersection.interId,\n        name: intersection.name\n      };\n      \n      // 遍历模型的所有子对象，添加相同的userData\n      trafficLightModel.traverse((child) => {\n        if (child.isMesh) {\n          child.userData = {\n            type: 'trafficLight',\n            interId: intersection.interId,\n            name: intersection.name\n          };\n        }\n      });\n      \n      // 将红绿灯添加到场景中\n      scene.add(trafficLightModel);\n      \n      // 存储红绿灯引用\n      trafficLightsMap.set(intersection.interId, {\n        model: trafficLightModel,\n        intersection: intersection,\n        position: modelPos\n      });\n      \n      console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n    }\n  });\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,GAAG,CAAC,CAAC;;AAEnB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE,2BAA2B;IAChCZ,KAAK,EAAE,6BAA6B;IACpCa,GAAG,EAAE,2BAA2B;IAAG;IACnCC,IAAI,EAAE,4BAA4B,CAAE;EACtC;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAIC,gBAAgB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIE,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIH,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAII,kBAAkB,GAAG,IAAIJ,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC;AACA,MAAMK,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGT,QAAQ,oBAAoB,CAAC;IAC7D,MAAMU,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCf,gBAAgB,GAAGW,WAAW,CAACI,KAAK;QACpCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEjB,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCjB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,MAAM,IAAK;EACjC,IAAI,CAAC1C,YAAY,EAAE;IACjBA,YAAY,GAAG0C,MAAM,CAACC,KAAK,CAAC,CAAC;IAC7B,OAAOD,MAAM;EACf;EAEA,MAAME,SAAS,GAAGP,aAAa,CAACK,MAAM,CAACG,CAAC,EAAE7C,YAAY,CAAC6C,CAAC,EAAE3C,KAAK,CAAC;EAChE,MAAM4C,SAAS,GAAGT,aAAa,CAACK,MAAM,CAACK,CAAC,EAAE/C,YAAY,CAAC+C,CAAC,EAAE7C,KAAK,CAAC;EAChE,MAAM8C,SAAS,GAAGX,aAAa,CAACK,MAAM,CAACO,CAAC,EAAEjD,YAAY,CAACiD,CAAC,EAAE/C,KAAK,CAAC;EAEhEF,YAAY,CAACkD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACjD,OAAOhD,YAAY,CAAC2C,KAAK,CAAC,CAAC;AAC7B,CAAC;;AAED;AACA,MAAMQ,cAAc,GAAIC,WAAW,IAAK;EACtC,IAAInD,YAAY,KAAK,IAAI,EAAE;IACzBA,YAAY,GAAGmD,WAAW;IAC1B,OAAOA,WAAW;EACpB;;EAEA;EACA,IAAIC,IAAI,GAAGD,WAAW,GAAGnD,YAAY;EACrC,IAAIoD,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EAExC,MAAMC,gBAAgB,GAAGnB,aAAa,CAACpC,YAAY,GAAGoD,IAAI,EAAEpD,YAAY,EAAEC,KAAK,CAAC;EAChFD,YAAY,GAAGuD,gBAAgB;EAC/B,OAAOA,gBAAgB;AACzB,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGvF,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMwF,UAAU,GAAGxF,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMyF,SAAS,GAAGzF,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMqF,aAAa,GAAG1F,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM2F,eAAe,GAAG3F,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM4F,aAAa,GAAG5F,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM6F,iBAAiB,GAAG7F,MAAM,CAAC,IAAI,CAAC,CAAC,CAAE;EACzC,MAAM,CAAC8F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAAC+F,YAAY,EAAEC,eAAe,CAAC,GAAGhG,QAAQ,CAAC;IAC/CiG,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMuG,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGzH,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC0H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1H,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC2H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5H,QAAQ,CAAC;IAC7D6H,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAEhC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxBqD,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,uBAAuB,GAAG;IAC9BzB,QAAQ,EAAE,OAAO;IACjB0B,GAAG,EAAE,MAAM;IACXxB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BwB,KAAK,EAAE,OAAO;IAAG;IACjBvB,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMc,UAAU,GAAG;IACjB5B,QAAQ,EAAE,OAAO;IACjB0B,GAAG,EAAE,MAAM;IACXxB,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBqB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbjB,QAAQ,EAAE,MAAM;IAChBkB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC5B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM6B,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnC,WAAW,CAAC,QAAQ,CAAC;IACrBlF,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACqH,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrC,WAAW,CAAC,QAAQ,CAAC;IACrBlF,UAAU,GAAG,QAAQ;IAErB,IAAIoG,SAAS,CAACoB,OAAO,IAAIvH,QAAQ,EAAE;MACjC;MACA,MAAMwH,UAAU,GAAGrB,SAAS,CAACoB,OAAO,CAACpC,QAAQ,CAAClC,KAAK,CAAC,CAAC;MACrD,MAAMwE,SAAS,GAAGtB,SAAS,CAACoB,OAAO,CAACG,EAAE,CAACzE,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIjE,KAAK,CAAC2I,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;QAAEzE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,GAAG;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCsE,MAAM,CAAC7I,KAAK,CAAC8I,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd9B,SAAS,CAACoB,OAAO,CAACpC,QAAQ,CAAC+C,IAAI,CAACV,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,IAAInJ,KAAK,CAAC2I,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEzE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BsE,MAAM,CAAC7I,KAAK,CAAC8I,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd9B,SAAS,CAACoB,OAAO,CAACG,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;MACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGpI,QAAQ,CAACqI,MAAM,CAACpF,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIjE,KAAK,CAAC2I,KAAK,CAACS,aAAa,CAAC,CAC3BR,EAAE,CAAC;QAAEzE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BsE,MAAM,CAAC7I,KAAK,CAAC8I,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdjI,QAAQ,CAACqI,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACAjC,SAAS,CAACoB,OAAO,CAACe,MAAM,CAACtI,QAAQ,CAACqI,MAAM,CAAC;QACzCrI,QAAQ,CAACuI,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAnI,QAAQ,CAACqH,OAAO,GAAG,IAAI;;MAEvB;MACArH,QAAQ,CAACwI,WAAW,GAAG,EAAE;MACzBxI,QAAQ,CAACyI,WAAW,GAAG,GAAG;MAC1BzI,QAAQ,CAAC0I,aAAa,GAAG9E,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC7D,QAAQ,CAAC2I,aAAa,GAAG,CAAC;MAC1B3I,QAAQ,CAACuI,MAAM,CAAC,CAAC;MAEjB/F,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBmG,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAG9J,iBAAiB,CAAC+J,aAAa,CAAC9G,IAAI,CAAC+G,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAI9C,SAAS,CAACoB,OAAO,IAAIvH,QAAQ,EAAE;MACjDqG,uBAAuB,CAAC4C,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAGlF,SAAS,CAACoD,OAAO,CAAC+B,YAAY,CAChDC,UAAU,CAACN,YAAY,CAACrE,SAAS,CAAC,EAClC2E,UAAU,CAACN,YAAY,CAACpE,QAAQ,CAClC,CAAC;MAEDrC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvB+G,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACH7E,SAAS,EAAEqE,YAAY,CAACrE,SAAS;UACjCC,QAAQ,EAAEoE,YAAY,CAACpE;QACzB,CAAC;QACD6E,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACAtJ,UAAU,GAAG,cAAc;MAC3BkF,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAACoB,OAAO,CAACpC,QAAQ,CAAC3B,GAAG,CAAC6F,WAAW,CAAClG,CAAC,EAAE,GAAG,EAAE,CAACkG,WAAW,CAAChG,CAAC,CAAC;;MAElE;MACArD,QAAQ,CAACqI,MAAM,CAAC7E,GAAG,CAAC6F,WAAW,CAAClG,CAAC,EAAE,CAAC,EAAE,CAACkG,WAAW,CAAChG,CAAC,CAAC;;MAErD;MACA8C,SAAS,CAACoB,OAAO,CAACe,MAAM,CAACtI,QAAQ,CAACqI,MAAM,CAAC;;MAEzC;MACArI,QAAQ,CAACqH,OAAO,GAAG,IAAI;MACvBrH,QAAQ,CAACuI,MAAM,CAAC,CAAC;;MAEjB;MACApC,SAAS,CAACoB,OAAO,CAACoC,YAAY,CAAC,CAAC;MAChCxD,SAAS,CAACoB,OAAO,CAACqC,iBAAiB,CAAC,IAAI,CAAC;MAEzCpH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB+G,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBS,IAAI,EAAE1D,SAAS,CAACoB,OAAO,CAACpC,QAAQ,CAAC2E,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAE/J,QAAQ,CAACqI,MAAM,CAACyB,OAAO,CAAC,CAAC;QAC9BJ,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC5C,IAAI,CAAC7J,KAAK,EAAE;MACVmC,OAAO,CAACE,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IAEAF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBwH,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACC,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIF,KAAK,KAAKxJ,WAAW,CAACM,MAAM,CAACE,GAAG,IAAImJ,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAAA,IAAAC,iBAAA;QAClEhI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2H,WAAW,CAAC;;QAEpC;QACA,MAAMK,SAAS,GAAGL,WAAW,CAACM,GAAG;QACjC,MAAMC,gBAAgB,GAAGP,WAAW,CAACQ,EAAE;;QAEvC;QACA,MAAMC,aAAa,GAAGtJ,gBAAgB,CAACuJ,GAAG,CAACL,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrDrI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;YACzBsI,KAAK,EAAEN,SAAS;YAChBO,KAAK,EAAEL,gBAAgB;YACvBM,KAAK,EAAEJ;UACT,CAAC,CAAC;UACF;QACF;;QAEA;QACAtJ,gBAAgB,CAACiC,GAAG,CAACiH,SAAS,EAAEE,gBAAgB,CAAC;QAEjDnI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;UACzBsI,KAAK,EAAEN,SAAS;UAChBS,GAAG,EAAEP,gBAAgB;UACrBQ,KAAK,EAAE,CAACN,aAAa,IAAIF,gBAAgB,IAAIE;QAC/C,CAAC,CAAC;QAEF,MAAMO,YAAY,GAAG,EAAAZ,iBAAA,GAAAJ,WAAW,CAACtI,IAAI,cAAA0I,iBAAA,uBAAhBA,iBAAA,CAAkBY,YAAY,KAAI,EAAE;QACzD,MAAMC,KAAK,GAAGjB,WAAW,CAACtI,IAAI,CAACuJ,KAAK;;QAEpC;QACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAF,YAAY,CAACI,OAAO,CAACC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIL,KAAK,GAAGI,WAAW,CAACE,SAAS;UACzC,MAAMpB,IAAI,GAAGkB,WAAW,CAACG,WAAW;UAEpC,IAAGrB,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAMsB,KAAK,GAAG;cACZjH,SAAS,EAAE2E,UAAU,CAACkC,WAAW,CAACK,WAAW,CAAC;cAC9CjH,QAAQ,EAAE0E,UAAU,CAACkC,WAAW,CAACM,UAAU,CAAC;cAC5CjH,KAAK,EAAEyE,UAAU,CAACkC,WAAW,CAACO,SAAS,CAAC;cACxCjH,OAAO,EAAEwE,UAAU,CAACkC,WAAW,CAACQ,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAG/H,SAAS,CAACoD,OAAO,CAAC+B,YAAY,CAACuC,KAAK,CAACjH,SAAS,EAAEiH,KAAK,CAAChH,QAAQ,CAAC;;YAEhF;YACA,IAAIsH,cAAc;YAClB,QAAQ5B,IAAI;cACV,KAAK,GAAG;gBAAE;gBACR4B,cAAc,GAAGlM,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRkM,cAAc,GAAGjM,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRiM,cAAc,GAAGhM,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIiM,KAAK,GAAG/K,aAAa,CAACyJ,GAAG,CAACY,EAAE,CAAC;YAEjC,IAAI,CAACU,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAME,QAAQ,GAAGF,cAAc,CAAClJ,KAAK,CAAC,CAAC;cACvC;cACA,MAAMqJ,MAAM,GAAG/B,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzC8B,QAAQ,CAAClH,QAAQ,CAAC3B,GAAG,CAAC0I,QAAQ,CAAC/I,CAAC,EAAEmJ,MAAM,EAAE,CAACJ,QAAQ,CAAC7I,CAAC,CAAC;cACtDgJ,QAAQ,CAACE,QAAQ,CAAClJ,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGgI,KAAK,CAAC9G,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7DxD,KAAK,CAACmM,GAAG,CAACH,QAAQ,CAAC;cAEnBhL,aAAa,CAACmC,GAAG,CAACkI,EAAE,EAAE;gBACpBU,KAAK,EAAEC,QAAQ;gBACfI,UAAU,EAAEnB,GAAG;gBACff,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAI6B,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAACjH,QAAQ,CAAC3B,GAAG,CAAC0I,QAAQ,CAAC/I,CAAC,EAAEiJ,KAAK,CAAC7B,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC2B,QAAQ,CAAC7I,CAAC,CAAC;cACjF+I,KAAK,CAACA,KAAK,CAACG,QAAQ,CAAClJ,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGgI,KAAK,CAAC9G,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEuI,KAAK,CAACK,UAAU,GAAGnB,GAAG;cACtBc,KAAK,CAACA,KAAK,CAACzC,YAAY,CAAC,CAAC;cAC1ByC,KAAK,CAACA,KAAK,CAACxC,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAM8C,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACxB,YAAY,CAACyB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,SAAS,CAAC,CAAC;QAE9DtK,aAAa,CAACmK,OAAO,CAAC,CAACuB,SAAS,EAAErB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAGyB,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAACtB,EAAE,CAAC,EAAE;YACzErL,KAAK,CAAC4M,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7B/K,aAAa,CAAC6L,MAAM,CAACxB,EAAE,CAAC;YACxBlJ,OAAO,CAACC,GAAG,CAAC,oBAAoBiJ,EAAE,QAAQqB,SAAS,CAACxC,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAIN,KAAK,KAAKxJ,WAAW,CAACM,MAAM,CAACC,GAAG,IAAIoJ,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAClE/H,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2H,WAAW,CAAC;QAEpC,MAAM+C,OAAO,GAAG/C,WAAW,CAACtI,IAAI;QAChC,MAAMsL,KAAK,GAAGD,OAAO,CAAC5K,KAAK;QAC3B,MAAM8K,QAAQ,GAAG;UACfzI,SAAS,EAAE2E,UAAU,CAAC4D,OAAO,CAACG,QAAQ,CAAC;UACvCzI,QAAQ,EAAE0E,UAAU,CAAC4D,OAAO,CAACI,OAAO,CAAC;UACrCzI,KAAK,EAAEyE,UAAU,CAAC4D,OAAO,CAACnB,SAAS,CAAC;UACpCjH,OAAO,EAAEwE,UAAU,CAAC4D,OAAO,CAAClB,WAAW;QACzC,CAAC;QAEDzJ,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE4K,QAAQ,CAAC;QAClC7K,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE2K,KAAK,CAAC;;QAE3B;QACA,MAAMlB,QAAQ,GAAG/H,SAAS,CAACoD,OAAO,CAAC+B,YAAY,CAAC+D,QAAQ,CAACzI,SAAS,EAAEyI,QAAQ,CAACxI,QAAQ,CAAC;QACtF,MAAM2I,WAAW,GAAG,IAAI5O,KAAK,CAAC6O,OAAO,CAACvB,QAAQ,CAAC/I,CAAC,EAAE,GAAG,EAAE,CAAC+I,QAAQ,CAAC7I,CAAC,CAAC;QACnE,MAAMK,WAAW,GAAGE,IAAI,CAACC,EAAE,GAAGwJ,QAAQ,CAACtI,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;;QAE9D;QACA,IAAI6J,UAAU,GAAGrM,aAAa,CAACyJ,GAAG,CAACsC,KAAK,CAAC;;QAEzC;QACA,MAAM9K,aAAa,GAAG8K,KAAK,KAAK5L,gBAAgB;QAEhD,IAAI,CAACkM,UAAU,IAAIzN,qBAAqB,EAAE;UACxC;UACA,MAAM0N,eAAe,GAAG1N,qBAAqB,CAACgD,KAAK,CAAC,CAAC;UACrD0K,eAAe,CAACxI,QAAQ,CAAC+C,IAAI,CAACsF,WAAW,CAAC;UAC1CG,eAAe,CAACpB,QAAQ,CAAClJ,CAAC,GAAGK,WAAW;UACxCrD,KAAK,CAACmM,GAAG,CAACmB,eAAe,CAAC;;UAE1B;UACAtM,aAAa,CAACmC,GAAG,CAAC4J,KAAK,EAAE;YACvBhB,KAAK,EAAEuB,eAAe;YACtBlB,UAAU,EAAElB,IAAI,CAACD,GAAG,CAAC,CAAC;YACtBf,IAAI,EAAE,GAAG;YAAE;YACXqD,MAAM,EAAEtL;UACV,CAAC,CAAC;UAEFE,OAAO,CAACC,GAAG,CAAC,aAAa2K,KAAK,SAASI,WAAW,CAACrK,CAAC,CAAC0K,OAAO,CAAC,CAAC,CAAC,KAAKL,WAAW,CAACnK,CAAC,CAACwK,OAAO,CAAC,CAAC,CAAC,KAAKL,WAAW,CAACjK,CAAC,CAACsK,OAAO,CAAC,CAAC,CAAC,YAAYvL,aAAa,EAAE,CAAC;;UAErJ;UACA,IAAIA,aAAa,EAAE;YACjB9C,gBAAgB,GAAGmO,eAAe;YAClChJ,eAAe,CAAC0I,QAAQ,CAAC;YACzB7K,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2K,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIM,UAAU,EAAE;UACrB;UACA,MAAMI,gBAAgB,GAAG/K,cAAc,CAACyK,WAAW,CAAC;UACpD,MAAM1J,gBAAgB,GAAGL,cAAc,CAACC,WAAW,CAAC;;UAEpD;UACAgK,UAAU,CAACtB,KAAK,CAACjH,QAAQ,CAAC+C,IAAI,CAAC4F,gBAAgB,CAAC;UAChDJ,UAAU,CAACtB,KAAK,CAACG,QAAQ,CAAClJ,CAAC,GAAGS,gBAAgB;UAC9C4J,UAAU,CAACtB,KAAK,CAACzC,YAAY,CAAC,CAAC;UAC/B+D,UAAU,CAACtB,KAAK,CAACxC,iBAAiB,CAAC,IAAI,CAAC;UACxC8D,UAAU,CAACjB,UAAU,GAAGlB,IAAI,CAACD,GAAG,CAAC,CAAC;UAClCoC,UAAU,CAACE,MAAM,GAAGtL,aAAa,CAAC,CAAC;;UAEnCE,OAAO,CAACC,GAAG,CAAC,cAAc2K,KAAK,SAASU,gBAAgB,CAAC3K,CAAC,CAAC0K,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAACzK,CAAC,CAACwK,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAACvK,CAAC,CAACsK,OAAO,CAAC,CAAC,CAAC,YAAYvL,aAAa,EAAE,CAAC;;UAErK;UACA,IAAIA,aAAa,EAAE;YACjB9C,gBAAgB,GAAGkO,UAAU,CAACtB,KAAK;YACnCzH,eAAe,CAAC0I,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAM/B,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,MAAMoB,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhCrL,aAAa,CAACmK,OAAO,CAAC,CAACuB,SAAS,EAAErB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAGyB,SAAS,CAACN,UAAU,GAAGC,iBAAiB,EAAE;YAClDrM,KAAK,CAAC4M,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7B/K,aAAa,CAAC6L,MAAM,CAACxB,EAAE,CAAC;YACxBlJ,OAAO,CAACC,GAAG,CAAC,mBAAmBiJ,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;MACA;MACA,IAAIzB,KAAK,KAAKxJ,WAAW,CAACM,MAAM,CAACI,IAAI,IAAIiJ,WAAW,CAACG,IAAI,KAAK,MAAM,EAAE;QACpE/H,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2H,WAAW,CAAC;QAErC,MAAM2D,QAAQ,GAAG3D,WAAW,CAACtI,IAAI;QACjC,IAAIiM,QAAQ,EAAE;UACZ,MAAMC,KAAK,GAAGD,QAAQ,CAACC,KAAK;UAC5B,MAAM9E,aAAa,GAAG6E,QAAQ,CAAC7E,aAAa,IAAI,EAAE;;UAElD;UACAA,aAAa,CAACsC,OAAO,CAACvC,YAAY,IAAI;YACpC,MAAMxC,OAAO,GAAGwC,YAAY,CAACxC,OAAO;YACpC,MAAMwH,WAAW,GAAGhF,YAAY,CAACgF,WAAW;YAC5C,MAAMtH,MAAM,GAAGsC,YAAY,CAACtC,MAAM,IAAI,EAAE;;YAExC;YACA,MAAMuH,kBAAkB,GAAG,EAAE;;YAE7B;YACAvH,MAAM,CAAC6E,OAAO,CAAC2C,KAAK,IAAI;cACtB,MAAMC,YAAY,GAAGD,KAAK,CAACC,YAAY,CAAC,CAAC;cACzC,MAAMC,OAAO,GAAGF,KAAK,CAACE,OAAO,CAAC,CAAU;cACxC,MAAMC,YAAY,GAAGH,KAAK,CAACG,YAAY,CAAC,CAAC;cACzC,MAAMC,UAAU,GAAGJ,KAAK,CAACI,UAAU,CAAC,CAAI;cACxC,MAAMC,YAAY,GAAGL,KAAK,CAACK,YAAY,CAAC,CAAC;;cAEzC;cACAN,kBAAkB,CAACO,IAAI,CAAC;gBACtBL,YAAY;gBACZC,OAAO;gBACPC,YAAY;gBACZC,UAAU;gBACVC;cACF,CAAC,CAAC;;cAEF;cACAhM,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE;gBACpBiM,IAAI,EAAEjI,OAAO;gBACb;gBACAkI,IAAI,EAAEP,YAAY;gBAClBQ,IAAI,EAAEP,OAAO;gBACbQ,EAAE,EAAEP,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGA,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGA,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI;gBAClGQ,IAAI,EAAEP,UAAU;gBAChBQ,EAAE,EAAEP,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGA,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGA,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;cAChG,CAAC,CAAC;YACJ,CAAC,CAAC;;YAEF;YACA9M,kBAAkB,CAAC8B,GAAG,CAACiD,OAAO,EAAE;cAC9BuH,KAAK;cACLvH,OAAO;cACPE,MAAM,EAAEuH;YACV,CAAC,CAAC;;YAEF;YACAvN,MAAM,CAACqO,WAAW,CAAC;cACjBzE,IAAI,EAAE,MAAM;cACZzI,IAAI,EAAE;gBACJkM,KAAK;gBACLvH,OAAO;gBACPE,MAAM,EAAEuH;cACV;YACF,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,CAAC;QACJ,CAAC,MAAM;UACL1L,OAAO,CAACyM,IAAI,CAAC,kBAAkB,EAAE7E,WAAW,CAAC;QAC/C;QAEA;MACF;;MAIA;MACA,IAAIH,KAAK,KAAKxJ,WAAW,CAACM,MAAM,CAACG,GAAG,IAAIkJ,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAClE/H,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2H,WAAW,CAAC;;QAEpC;QACAzJ,MAAM,CAACqO,WAAW,CAAC;UACjBzE,IAAI,EAAE,KAAK;UACXzI,IAAI,EAAEsI,WAAW,CAACtI;QACpB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMoN,OAAO,GAAG9E,WAAW,CAACtI,IAAI;QAChC,MAAMkM,KAAK,GAAGkB,OAAO,CAAClB,KAAK;QAC3B,MAAMmB,MAAM,GAAGD,OAAO,CAACE,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAAC3D,OAAO,CAAC6D,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAMzD,QAAQ,GAAG/H,SAAS,CAACoD,OAAO,CAAC+B,YAAY,CAC7CC,UAAU,CAAC2F,OAAO,CAACU,OAAO,CAAC,EAC3BrG,UAAU,CAAC2F,OAAO,CAACW,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAAC9D,QAAQ,EAAE4D,WAAW,EAAEC,YAAY,CAAC;UAEtDvN,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;YACtBwN,IAAI,EAAEX,OAAO;YACbY,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,WAAW;YACjBW,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,OAAO;YACbW,EAAE,EAAEpE;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF;MACF;;MAGA;MACA,IAAIjC,KAAK,KAAKxJ,WAAW,CAACM,MAAM,CAACV,KAAK,IAAI+J,WAAW,CAACG,IAAI,KAAK,OAAO,EAAE;QACtE/H,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2H,WAAW,CAAC;QAErC,MAAMmG,SAAS,GAAGnG,WAAW,CAACtI,IAAI;QAClC,MAAM0O,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMvL,QAAQ,GAAG;UACfN,QAAQ,EAAE0E,UAAU,CAACgH,SAAS,CAAChD,OAAO,CAAC;UACvC3I,SAAS,EAAE2E,UAAU,CAACgH,SAAS,CAACjD,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMpB,QAAQ,GAAG/H,SAAS,CAACoD,OAAO,CAAC+B,YAAY,CAACnE,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAO4L,SAAS;UACd,KAAK,GAAG;YAAG;YACTT,iBAAiB,CAAC9D,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACX8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX8D,iBAAiB,CAAC9D,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAMyE,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CZ,iBAAiB,CAAC9D,QAAQ,EAAE,KAAKyE,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVX,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZ8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAM2E,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5Cf,iBAAiB,CAAC9D,QAAQ,EAAE,QAAQ8E,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACAtO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBwH,KAAK;QACLM,IAAI,EAAEH,WAAW,CAACG,IAAI;QACtBzI,IAAI,EAAEsI;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO1H,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEwH,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAM8G,cAAc,GAAGA,CAAA,KAAM;IAC3BzO,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMyO,KAAK,GAAG,QAAQzQ,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnE0B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyO,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB7O,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED0O,EAAE,CAACG,SAAS,GAAIjC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMnF,OAAO,GAAGG,IAAI,CAACC,KAAK,CAAC+E,KAAK,CAACvN,IAAI,CAAC;;QAEtC;QACA,IAAIoI,OAAO,CAACK,IAAI,KAAK,SAAS,EAAE;UAC9B/H,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEyH,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACK,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIL,OAAO,CAACK,IAAI,KAAK,SAAS,IAAIL,OAAO,CAACD,KAAK,IAAIC,OAAO,CAACqH,OAAO,EAAE;UAClE;UACAvH,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEI,IAAI,CAACmH,SAAS,CAACtH,OAAO,CAACqH,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAO7O,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDyO,EAAE,CAACM,OAAO,GAAI/O,KAAK,IAAK;MACtBF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDyO,EAAE,CAACO,OAAO,GAAG,MAAM;MACjBlP,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAkP,UAAU,CAACV,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACA3M,aAAa,CAACiD,OAAO,GAAG4J,EAAE;EAC5B,CAAC;EAED1S,SAAS,CAAC,MAAM;IACd,IAAI,CAACwF,YAAY,CAACsD,OAAO,EAAE;;IAE3B;IACAqK,aAAa,CAAC,CAAC;;IAEf;IACAvR,KAAK,GAAG,IAAIzB,KAAK,CAACiT,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIlT,KAAK,CAACmT,iBAAiB,CACxC,EAAE,EACFpR,MAAM,CAACqR,UAAU,GAAGrR,MAAM,CAACsR,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC3M,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCsO,MAAM,CAACxJ,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBnC,SAAS,CAACoB,OAAO,GAAGuK,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAItT,KAAK,CAACuT,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC1R,MAAM,CAACqR,UAAU,EAAErR,MAAM,CAACsR,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC5R,MAAM,CAAC6R,gBAAgB,CAAC;IAC/CvO,YAAY,CAACsD,OAAO,CAACkL,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI/T,KAAK,CAACgU,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DvS,KAAK,CAACmM,GAAG,CAACmG,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIjU,KAAK,CAACkU,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC1N,QAAQ,CAAC3B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CnD,KAAK,CAACmM,GAAG,CAACqG,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAInU,KAAK,CAACkU,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC5N,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CnD,KAAK,CAACmM,GAAG,CAACuG,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIpU,KAAK,CAACqU,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC7N,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCwP,SAAS,CAACE,KAAK,GAAGtP,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BmP,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBhT,KAAK,CAACmM,GAAG,CAACwG,SAAS,CAAC;;IAEpB;IACAhT,QAAQ,GAAG,IAAIlB,aAAa,CAACgT,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD1S,QAAQ,CAACsT,aAAa,GAAG,IAAI;IAC7BtT,QAAQ,CAACuT,aAAa,GAAG,IAAI;IAC7BvT,QAAQ,CAACwT,kBAAkB,GAAG,KAAK;IACnCxT,QAAQ,CAACwI,WAAW,GAAG,EAAE;IACzBxI,QAAQ,CAACyI,WAAW,GAAG,GAAG;IAC1BzI,QAAQ,CAAC0I,aAAa,GAAG9E,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC7D,QAAQ,CAAC2I,aAAa,GAAG,CAAC;IAC1B3I,QAAQ,CAACqI,MAAM,CAAC7E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BxD,QAAQ,CAACuI,MAAM,CAAC,CAAC;;IAEjB;IACA/F,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBqP,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB9R,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBmG,SAAS,EAAE,CAAC,CAACA,SAAS,CAACoB;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMkM,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIhV,UAAU,CAAC,CAAC;QACtCgV,aAAa,CAACC,IAAI,CAChB,GAAG1S,QAAQ,uBAAuB,EACjC2S,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAC1T,KAAK;;UAE/B;UACA,MAAM4T,gBAAgB,GAAG,IAAIrV,KAAK,CAACsV,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI3V,KAAK,CAAC4V,oBAAoB,CAAC;kBACjDvN,KAAK,EAAE,QAAQ;kBAAO;kBACtBwN,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIP,KAAK,CAACE,QAAQ,CAACzH,GAAG,EAAE;kBACtB0H,WAAW,CAAC1H,GAAG,GAAGuH,KAAK,CAACE,QAAQ,CAACzH,GAAG;gBACtC;;gBAEA;gBACAuH,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5B/R,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2R,KAAK,CAAChL,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM4K,YAAY,CAACY,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMT,KAAK,GAAGJ,YAAY,CAACY,QAAQ,CAAC,CAAC,CAAC;YACtCX,gBAAgB,CAACzH,GAAG,CAAC4H,KAAK,CAAC;UAC7B;;UAEA;UACA/T,KAAK,CAACmM,GAAG,CAACyH,gBAAgB,CAAC;;UAE3B;UACAzU,gBAAgB,GAAGyU,gBAAgB;UAEnCzR,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BgC,kBAAkB,CAAC,IAAI,CAAC;UACxBkP,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAa,GAAG,IAAK;UACPtS,OAAO,CAACC,GAAG,CAAC,aAAa,CAACqS,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEnH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACD+F,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMhB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAxC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIgD,gBAAgB,EAAE;UACpB,MAAMiB,YAAY,GAAG;YACnBtQ,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAMoQ,UAAU,GAAGhR,SAAS,CAACoD,OAAO,CAAC+B,YAAY,CAAC4L,YAAY,CAACtQ,SAAS,EAAEsQ,YAAY,CAACrQ,QAAQ,CAAC;UAChG;UACAoP,gBAAgB,CAAC9O,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxCyQ,gBAAgB,CAAC1H,QAAQ,CAAClJ,CAAC,GAAIO,IAAI,CAACC,EAAE,GAAGqR,YAAY,CAACnQ,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9EoQ,gBAAgB,CAACtK,YAAY,CAAC,CAAC;UAC/BsK,gBAAgB,CAACrK,iBAAiB,CAAC,IAAI,CAAC;UACxC/J,eAAe,GAAGoU,gBAAgB,CAAC9O,QAAQ,CAAClC,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM0S,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI5B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM2B,WAAW,GAAIC,WAAW,IAAK;UACnChT,OAAO,CAACC,GAAG,CAAC,WAAW4S,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI5W,UAAU,CAAC,CAAC;UAC/B4W,MAAM,CAAC3B,IAAI,CACTuB,GAAG,EACFtB,IAAI,IAAK;YACRvR,OAAO,CAACC,GAAG,CAAC,WAAW4S,GAAG,EAAE,CAAC;YAC7B1B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAe,GAAG,IAAK;YACPtS,OAAO,CAACC,GAAG,CAAC,SAAS,CAACqS,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEnH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAnL,KAAK,IAAK;YACTF,OAAO,CAACE,KAAK,CAAC,SAAS2S,GAAG,EAAE,EAAE3S,KAAK,CAAC;YACpC,IAAI8S,WAAW,GAAG,CAAC,EAAE;cACnBhT,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BkP,UAAU,CAAC,MAAM4D,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL5B,MAAM,CAAClR,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED6S,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI5W,UAAU,CAAC,CAAC;IAC/B4W,MAAM,CAAC3B,IAAI,CACT,GAAG1S,QAAQ,4BAA4B,EACvC,MAAO2S,IAAI,IAAK;MACd,IAAI;QACF,MAAM3H,KAAK,GAAG2H,IAAI,CAAC1T,KAAK;QACxB+L,KAAK,CAACsJ,KAAK,CAAClS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB4I,KAAK,CAACjH,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BnD,KAAK,CAACmM,GAAG,CAACJ,KAAK,CAAC;;QAEhB;QACA,MAAM6I,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOvS,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAoS,GAAG,IAAK;MACPtS,OAAO,CAACC,GAAG,CAAC,SAAS,CAACqS,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEnH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAnL,KAAK,IAAK;MACTF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAE;QACrBiT,IAAI,EAAEjT,KAAK,CAAC6H,IAAI;QAChBqL,IAAI,EAAElT,KAAK,CAACwH,OAAO;QACnB2L,KAAK,EAAE,GAAGzU,QAAQ,4BAA4B;QAC9C0U,KAAK,EAAE,GAAG1U,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAM2U,OAAO,GAAGA,CAAA,KAAM;MACpBxR,iBAAiB,CAACgD,OAAO,GAAGyO,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACA/W,KAAK,CAACuJ,MAAM,CAAC,CAAC;MAEd,IAAIxI,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACqH,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM4O,UAAU,GAAGzW,gBAAgB,CAAC2F,QAAQ,CAAClC,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMiT,eAAe,GAAG1W,gBAAgB,CAAC+M,QAAQ,CAAClJ,CAAC;;QAEnD;QACA;QACA,MAAM8S,gBAAgB,GAAG,EAAED,eAAe,GAAGtS,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMuS,YAAY,GAAG,IAAIxX,KAAK,CAAC6O,OAAO,CACpC,CAAC,EAAE,GAAG7J,IAAI,CAACyS,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGvS,IAAI,CAAC0S,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACArE,MAAM,CAAC3M,QAAQ,CAAC+C,IAAI,CAAC+N,UAAU,CAAC,CAACzJ,GAAG,CAAC4J,YAAY,CAAC;;QAElD;QACAtE,MAAM,CAACpK,EAAE,CAAClE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAM+S,YAAY,GAAGN,UAAU,CAAChT,KAAK,CAAC,CAAC;QACvC6O,MAAM,CAACxJ,MAAM,CAACiO,YAAY,CAAC;;QAE3B;QACAzE,MAAM,CAAC0E,sBAAsB,CAAC,CAAC;QAC/B1E,MAAM,CAACnI,YAAY,CAAC,CAAC;QACrBmI,MAAM,CAAClI,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACA5J,QAAQ,CAACqH,OAAO,GAAG,KAAK;;QAExB;QACArH,QAAQ,CAACqI,MAAM,CAACH,IAAI,CAAC+N,UAAU,CAAC;QAChCjW,QAAQ,CAACuI,MAAM,CAAC,CAAC;QAEjB/F,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBgU,IAAI,EAAER,UAAU,CAACnM,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEiI,MAAM,CAAC3M,QAAQ,CAAC2E,OAAO,CAAC,CAAC;UAC/B4M,IAAI,EAAEH,YAAY,CAACzM,OAAO,CAAC,CAAC;UAC5B6M,IAAI,EAAE7E,MAAM,CAAC8E,iBAAiB,CAAC,IAAIhY,KAAK,CAAC6O,OAAO,CAAC,CAAC,CAAC,CAAC3D,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI/J,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACqH,OAAO,GAAG,IAAI;;QAEvB;QACAyK,MAAM,CAACpK,EAAE,CAAClE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAII,IAAI,CAACiT,GAAG,CAAC/E,MAAM,CAAC3M,QAAQ,CAAC9B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCyO,MAAM,CAAC3M,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BxD,QAAQ,CAACqI,MAAM,CAAC7E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BsO,MAAM,CAACxJ,MAAM,CAACtI,QAAQ,CAACqI,MAAM,CAAC;UAC9BrI,QAAQ,CAACuI,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACAuJ,MAAM,CAACnI,YAAY,CAAC,CAAC;QACrBmI,MAAM,CAAClI,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAI7J,UAAU,KAAK,cAAc,EAAE;QACxC;QACAC,QAAQ,CAACuI,MAAM,CAAC,CAAC;MACnB;MAEA,IAAIvI,QAAQ,EAAEA,QAAQ,CAACuI,MAAM,CAAC,CAAC;MAC/B,IAAIlI,KAAK,IAAIyR,MAAM,EAAE;QACnBI,QAAQ,CAAC4E,MAAM,CAACzW,KAAK,EAAEyR,MAAM,CAAC;MAChC;IACF,CAAC;IAEDiE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzBjF,MAAM,CAACkF,MAAM,GAAGrW,MAAM,CAACqR,UAAU,GAAGrR,MAAM,CAACsR,WAAW;MACtDH,MAAM,CAAC0E,sBAAsB,CAAC,CAAC;MAC/BtE,QAAQ,CAACG,OAAO,CAAC1R,MAAM,CAACqR,UAAU,EAAErR,MAAM,CAACsR,WAAW,CAAC;IACzD,CAAC;IACDtR,MAAM,CAACsW,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACApW,MAAM,CAACuW,aAAa,GAAG,MAAM;MAC3B,IAAI/Q,SAAS,CAACoB,OAAO,EAAE;QACrBpB,SAAS,CAACoB,OAAO,CAACpC,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC2C,SAAS,CAACoB,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCnC,SAAS,CAACoB,OAAO,CAACoC,YAAY,CAAC,CAAC;QAChCxD,SAAS,CAACoB,OAAO,CAACqC,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAI5J,QAAQ,EAAE;UACZA,QAAQ,CAACqI,MAAM,CAAC7E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BxD,QAAQ,CAACqH,OAAO,GAAG,IAAI;UACvBrH,QAAQ,CAACuI,MAAM,CAAC,CAAC;QACnB;QAEAxI,UAAU,GAAG,QAAQ;QACrByC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAI8B,iBAAiB,CAACgD,OAAO,EAAE;QAC7B4P,oBAAoB,CAAC5S,iBAAiB,CAACgD,OAAO,CAAC;QAC/ChD,iBAAiB,CAACgD,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAI5H,oBAAoB,EAAE;QACxByX,aAAa,CAACzX,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAI2E,aAAa,CAACiD,OAAO,EAAE;QACzBjD,aAAa,CAACiD,OAAO,CAAC8P,KAAK,CAAC,CAAC;QAC7B/S,aAAa,CAACiD,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACA5G,MAAM,CAAC2W,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAI7E,QAAQ,IAAIjO,YAAY,CAACsD,OAAO,EAAE;QACpCtD,YAAY,CAACsD,OAAO,CAACgQ,WAAW,CAACrF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAACsF,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAInW,aAAa,EAAE;QACjBA,aAAa,CAACmK,OAAO,CAAC,CAACuB,SAAS,EAAErB,EAAE,KAAK;UACvC,IAAIqB,SAAS,CAACX,KAAK,IAAI/L,KAAK,EAAE;YAC5BA,KAAK,CAAC4M,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACF/K,aAAa,CAACoW,KAAK,CAAC,CAAC;MACvB;;MAEA;MACA,IAAIpX,KAAK,EAAE;QACT,OAAMA,KAAK,CAACuU,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC/BxU,KAAK,CAAC4M,MAAM,CAAC5M,KAAK,CAACuU,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC;MACF;;MAEA;MACAvU,KAAK,GAAG,IAAI;MACZL,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BX,gBAAgB,GAAG,IAAI;MAEvBgD,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhE,SAAS,CAAC,MAAM;IACd;IACAkD,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAM+V,uBAAuB,GAAGA,CAAA,KAAM;MACpClV,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCd,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACAhB,MAAM,CAACsW,gBAAgB,CAAC,oBAAoB,EAAES,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCjW,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXhB,MAAM,CAAC2W,mBAAmB,CAAC,oBAAoB,EAAEI,uBAAuB,CAAC;MACzEN,aAAa,CAACO,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEtY,OAAA,CAAAE,SAAA;IAAAqV,QAAA,gBACEvV,OAAA;MAAMwY,KAAK,EAAE9Q,UAAW;MAAA6N,QAAA,EAAC;IAAK;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrC5Y,OAAA,CAACH,MAAM;MACL2Y,KAAK,EAAEjR,uBAAwB;MAC/BsR,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEpP,wBAAyB;MACnCqP,OAAO,EAAEjZ,iBAAiB,CAAC+J,aAAa,CAAC2D,GAAG,CAAC5D,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxBiP,KAAK,EAAEpP,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJkP,IAAI,EAAC,OAAO;MACZC,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACbjT,MAAM,EAAE,IAAI;QACZkT,SAAS,EAAE;MACb;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF5Y,OAAA;MAAKqZ,GAAG,EAAEzU,YAAa;MAAC4T,KAAK,EAAE;QAAE/Q,KAAK,EAAE,MAAM;QAAEwF,MAAM,EAAE;MAAO;IAAE;MAAAwL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpE5Y,OAAA;MAAKwY,KAAK,EAAE3S,oBAAqB;MAAA0P,QAAA,gBAC/BvV,OAAA;QACEwY,KAAK,EAAE;UACL,GAAGnS,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EiC,KAAK,EAAEjC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF2T,OAAO,EAAEvR,kBAAmB;QAAAwN,QAAA,EAC7B;MAED;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT5Y,OAAA;QACEwY,KAAK,EAAE;UACL,GAAGnS,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EiC,KAAK,EAAEjC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF2T,OAAO,EAAErR,kBAAmB;QAAAsN,QAAA,EAC7B;MAED;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAjU,EAAA,CAxpCMD,WAAW;AAAA6U,EAAA,GAAX7U,WAAW;AAypCjB,SAAS8U,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACjS,KAAK,GAAG,GAAG;EAClBiS,MAAM,CAACzM,MAAM,GAAG,EAAE;;EAElB;EACA4M,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACjS,KAAK,GAAC,CAAC,EAAEiS,MAAM,CAACzM,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMkN,OAAO,GAAG,IAAI5a,KAAK,CAAC6a,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAI9a,KAAK,CAAC+a,cAAc,CAAC;IAC9C9M,GAAG,EAAE2M,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAIjb,KAAK,CAACkb,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAACnE,KAAK,CAAClS,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOqW,MAAM;AACf;;AAIA;AACAlZ,MAAM,CAACoZ,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMjI,MAAM,GAAGkH,QAAQ,CAACgB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIpI,MAAM,EAAE;MACV;MACA,MAAMqI,MAAM,GAAGrI,MAAM,CAAC3M,QAAQ,CAAClC,KAAK,CAAC,CAAC;;MAEtC;MACA6O,MAAM,CAAC3M,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BsO,MAAM,CAACpK,EAAE,CAAClE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBsO,MAAM,CAACxJ,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAwJ,MAAM,CAACnI,YAAY,CAAC,CAAC;MACrBmI,MAAM,CAAClI,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM5J,QAAQ,GAAGgZ,QAAQ,CAACgB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIpa,QAAQ,EAAE;QACZA,QAAQ,CAACqI,MAAM,CAAC7E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BxD,QAAQ,CAACuI,MAAM,CAAC,CAAC;MACnB;MAEA/F,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB4X,GAAG,EAAEF,MAAM,CAACrQ,OAAO,CAAC,CAAC;QACrBwQ,GAAG,EAAExI,MAAM,CAAC3M,QAAQ,CAAC2E,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOyQ,CAAC,EAAE;IACV/X,OAAO,CAACE,KAAK,CAAC,YAAY,EAAE6X,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAM3I,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFpP,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMgT,MAAM,GAAG,IAAI5W,UAAU,CAAC,CAAC;;IAE/B;IACA,MAAM,CAAC2b,WAAW,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,CAAC,GAAG,MAAMjH,OAAO,CAACkH,GAAG,CAAC,CACjFnF,MAAM,CAACoF,SAAS,CAAC,GAAGzZ,QAAQ,uBAAuB,CAAC,EACpDqU,MAAM,CAACoF,SAAS,CAAC,GAAGzZ,QAAQ,uBAAuB,CAAC,EACpDqU,MAAM,CAACoF,SAAS,CAAC,GAAGzZ,QAAQ,sBAAsB,CAAC,EACnDqU,MAAM,CAACoF,SAAS,CAAC,GAAGzZ,QAAQ,6BAA6B,CAAC,CAC3D,CAAC;;IAEF;IACAnB,qBAAqB,GAAGua,WAAW,CAACna,KAAK;IACzCJ,qBAAqB,CAACkU,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;QAChB,MAAME,WAAW,GAAG,IAAI3V,KAAK,CAAC4V,oBAAoB,CAAC;UACnD;UACEvN,KAAK,EAAE,QAAQ;UACfwN,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,GAAG;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC;;QAEF;QACA,IAAIP,KAAK,CAACE,QAAQ,CAACzH,GAAG,EAAE;UACtB0H,WAAW,CAAC1H,GAAG,GAAGuH,KAAK,CAACE,QAAQ,CAACzH,GAAG;QACtC;QACAuH,KAAK,CAAC0G,OAAO,GAAGvG,WAAW;MAC7B;IACF,CAAC,CAAC;;IAEF;IACArU,qBAAqB,GAAGua,WAAW,CAACpa,KAAK;IACzC;IACAH,qBAAqB,CAACwV,KAAK,CAAClS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxC;IACAtD,qBAAqB,CAACiU,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;;IAEF;IACAxU,oBAAoB,GAAGua,UAAU,CAACra,KAAK;IACvC;IACAF,oBAAoB,CAACuV,KAAK,CAAClS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC;IACArD,oBAAoB,CAACgU,QAAQ,CAAEC,KAAK,IAAK;MACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;;IAEF;IACAvU,0BAA0B,GAAGua,gBAAgB,CAACta,KAAK;IACnD;IACAD,0BAA0B,CAACsV,KAAK,CAAClS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C;IACApD,0BAA0B,CAAC+T,QAAQ,CAAEC,KAAK,IAAK;MAC7C,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;IAEFnS,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMsO,mBAAmB,GAAIzG,IAAI,IAAK;EACpC,MAAMwQ,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAACxQ,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMyF,iBAAiB,GAAGA,CAAC7K,QAAQ,EAAE2T,IAAI,EAAE7R,KAAK,KAAK;EACnD;EACA,MAAM4S,MAAM,GAAGhB,gBAAgB,CAACC,IAAI,CAAC;EACrCe,MAAM,CAAC1U,QAAQ,CAAC3B,GAAG,CAAC2B,QAAQ,CAAChC,CAAC,EAAE,EAAE,EAAE,CAACgC,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAE;;EAEnD;EACAsO,UAAU,CAAC,MAAM;IACftR,KAAK,CAAC4M,MAAM,CAAC4M,MAAM,CAAC;EACtB,CAAC,EAAE,GAAG,CAAC;;EAEP;EACAxZ,KAAK,CAACmM,GAAG,CAACqN,MAAM,CAAC;EAEjBrX,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;IACvB6N,EAAE,EAAEnL,QAAQ;IACZ6V,EAAE,EAAElC,IAAI;IACRmC,EAAE,EAAEhU;EACN,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMiU,mBAAmB,GAAIC,iBAAiB,IAAK;EACjD,IAAI,CAAC/a,0BAA0B,IAAI,CAACC,KAAK,EAAE;IACzCmC,OAAO,CAACE,KAAK,CAAC,cAAc,CAAC;IAC7B;EACF;;EAEA;EACAjB,gBAAgB,CAAC+J,OAAO,CAAE4P,QAAQ,IAAK;IACrC,IAAI/a,KAAK,IAAI+a,QAAQ,CAAChP,KAAK,EAAE;MAC3B/L,KAAK,CAAC4M,MAAM,CAACmO,QAAQ,CAAChP,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF3K,gBAAgB,CAACgW,KAAK,CAAC,CAAC;;EAExB;EACAtY,iBAAiB,CAAC+J,aAAa,CAACsC,OAAO,CAACvC,YAAY,IAAI;IACtD,IAAIA,YAAY,CAACpE,QAAQ,IAAIoE,YAAY,CAACrE,SAAS,IAAIqE,YAAY,CAACxC,OAAO,EAAE;MAC3E;MACA,MAAMyF,QAAQ,GAAGiP,iBAAiB,CAAC7R,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAACrE,SAAS,CAAC,EAClC2E,UAAU,CAACN,YAAY,CAACpE,QAAQ,CAClC,CAAC;;MAED;MACA,MAAMwW,iBAAiB,GAAGjb,0BAA0B,CAAC6C,KAAK,CAAC,CAAC;;MAE5D;MACAoY,iBAAiB,CAAClW,QAAQ,CAAC3B,GAAG,CAAC0I,QAAQ,CAAC/I,CAAC,EAAE,CAAC,EAAE,CAAC+I,QAAQ,CAAC7I,CAAC,CAAC;;MAE1D;MACAgY,iBAAiB,CAACC,QAAQ,GAAG;QAC3B/Q,IAAI,EAAE,cAAc;QACpB9D,OAAO,EAAEwC,YAAY,CAACxC,OAAO;QAC7B2C,IAAI,EAAEH,YAAY,CAACG;MACrB,CAAC;;MAED;MACAiS,iBAAiB,CAAClH,QAAQ,CAAEC,KAAK,IAAK;QACpC,IAAIA,KAAK,CAACC,MAAM,EAAE;UAChBD,KAAK,CAACkH,QAAQ,GAAG;YACf/Q,IAAI,EAAE,cAAc;YACpB9D,OAAO,EAAEwC,YAAY,CAACxC,OAAO;YAC7B2C,IAAI,EAAEH,YAAY,CAACG;UACrB,CAAC;QACH;MACF,CAAC,CAAC;;MAEF;MACA/I,KAAK,CAACmM,GAAG,CAAC6O,iBAAiB,CAAC;;MAE5B;MACA5Z,gBAAgB,CAAC+B,GAAG,CAACyF,YAAY,CAACxC,OAAO,EAAE;QACzC2F,KAAK,EAAEiP,iBAAiB;QACxBpS,YAAY,EAAEA,YAAY;QAC1B9D,QAAQ,EAAE+G;MACZ,CAAC,CAAC;MAEF1J,OAAO,CAACC,GAAG,CAAC,SAASwG,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACxC,OAAO,kBAAkByF,QAAQ,CAAC/I,CAAC,KAAK,CAAC+I,QAAQ,CAAC7I,CAAC,GAAG,CAAC;IACjH;EACF,CAAC,CAAC;AACJ,CAAC;AAED,eAAeU,WAAW;AAAC,IAAA6U,EAAA;AAAA2C,YAAA,CAAA3C,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}