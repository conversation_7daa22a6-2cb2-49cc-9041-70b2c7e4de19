{"ast": null, "code": "import adjust_lon from '../common/adjust_lon';\nimport asinz from '../common/asinz';\nimport { EPSLN, HALF_PI } from '../constants/values';\nexport function init() {\n  //double temp;      /* temporary variable    */\n\n  /* Place parameters in static storage for common use\n      -------------------------------------------------*/\n  this.sin_p14 = Math.sin(this.lat0);\n  this.cos_p14 = Math.cos(this.lat0);\n}\n\n/* Orthographic forward equations--mapping lat,long to x,y\n    ---------------------------------------------------*/\nexport function forward(p) {\n  var sinphi, cosphi; /* sin and cos value        */\n  var dlon; /* delta longitude value      */\n  var coslon; /* cos of longitude        */\n  var ksp; /* scale factor          */\n  var g, x, y;\n  var lon = p.x;\n  var lat = p.y;\n  /* Forward equations\n      -----------------*/\n  dlon = adjust_lon(lon - this.long0);\n  sinphi = Math.sin(lat);\n  cosphi = Math.cos(lat);\n  coslon = Math.cos(dlon);\n  g = this.sin_p14 * sinphi + this.cos_p14 * cosphi * coslon;\n  ksp = 1;\n  if (g > 0 || Math.abs(g) <= EPSLN) {\n    x = this.a * ksp * cosphi * Math.sin(dlon);\n    y = this.y0 + this.a * ksp * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon);\n  }\n  p.x = x;\n  p.y = y;\n  return p;\n}\nexport function inverse(p) {\n  var rh; /* height above ellipsoid      */\n  var z; /* angle          */\n  var sinz, cosz; /* sin of z and cos of z      */\n  var con;\n  var lon, lat;\n  /* Inverse equations\n      -----------------*/\n  p.x -= this.x0;\n  p.y -= this.y0;\n  rh = Math.sqrt(p.x * p.x + p.y * p.y);\n  z = asinz(rh / this.a);\n  sinz = Math.sin(z);\n  cosz = Math.cos(z);\n  lon = this.long0;\n  if (Math.abs(rh) <= EPSLN) {\n    lat = this.lat0;\n    p.x = lon;\n    p.y = lat;\n    return p;\n  }\n  lat = asinz(cosz * this.sin_p14 + p.y * sinz * this.cos_p14 / rh);\n  con = Math.abs(this.lat0) - HALF_PI;\n  if (Math.abs(con) <= EPSLN) {\n    if (this.lat0 >= 0) {\n      lon = adjust_lon(this.long0 + Math.atan2(p.x, -p.y));\n    } else {\n      lon = adjust_lon(this.long0 - Math.atan2(-p.x, p.y));\n    }\n    p.x = lon;\n    p.y = lat;\n    return p;\n  }\n  lon = adjust_lon(this.long0 + Math.atan2(p.x * sinz, rh * this.cos_p14 * cosz - p.y * this.sin_p14 * sinz));\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\nexport var names = [\"ortho\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};", "map": {"version": 3, "names": ["adjust_lon", "asinz", "EPSLN", "HALF_PI", "init", "sin_p14", "Math", "sin", "lat0", "cos_p14", "cos", "forward", "p", "sinphi", "cosphi", "dlon", "coslon", "ksp", "g", "x", "y", "lon", "lat", "long0", "abs", "a", "y0", "inverse", "rh", "z", "sinz", "co<PERSON>", "con", "x0", "sqrt", "atan2", "names"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/projections/ortho.js"], "sourcesContent": ["import adjust_lon from '../common/adjust_lon';\nimport asinz from '../common/asinz';\nimport {EPSLN, HALF_PI} from '../constants/values';\n\nexport function init() {\n  //double temp;      /* temporary variable    */\n\n  /* Place parameters in static storage for common use\n      -------------------------------------------------*/\n  this.sin_p14 = Math.sin(this.lat0);\n  this.cos_p14 = Math.cos(this.lat0);\n}\n\n/* Orthographic forward equations--mapping lat,long to x,y\n    ---------------------------------------------------*/\nexport function forward(p) {\n  var sinphi, cosphi; /* sin and cos value        */\n  var dlon; /* delta longitude value      */\n  var coslon; /* cos of longitude        */\n  var ksp; /* scale factor          */\n  var g, x, y;\n  var lon = p.x;\n  var lat = p.y;\n  /* Forward equations\n      -----------------*/\n  dlon = adjust_lon(lon - this.long0);\n\n  sinphi = Math.sin(lat);\n  cosphi = Math.cos(lat);\n\n  coslon = Math.cos(dlon);\n  g = this.sin_p14 * sinphi + this.cos_p14 * cosphi * coslon;\n  ksp = 1;\n  if ((g > 0) || (Math.abs(g) <= EPSLN)) {\n    x = this.a * ksp * cosphi * Math.sin(dlon);\n    y = this.y0 + this.a * ksp * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon);\n  }\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\nexport function inverse(p) {\n  var rh; /* height above ellipsoid      */\n  var z; /* angle          */\n  var sinz, cosz; /* sin of z and cos of z      */\n  var con;\n  var lon, lat;\n  /* Inverse equations\n      -----------------*/\n  p.x -= this.x0;\n  p.y -= this.y0;\n  rh = Math.sqrt(p.x * p.x + p.y * p.y);\n  z = asinz(rh / this.a);\n\n  sinz = Math.sin(z);\n  cosz = Math.cos(z);\n\n  lon = this.long0;\n  if (Math.abs(rh) <= EPSLN) {\n    lat = this.lat0;\n    p.x = lon;\n    p.y = lat;\n    return p;\n  }\n  lat = asinz(cosz * this.sin_p14 + (p.y * sinz * this.cos_p14) / rh);\n  con = Math.abs(this.lat0) - HALF_PI;\n  if (Math.abs(con) <= EPSLN) {\n    if (this.lat0 >= 0) {\n      lon = adjust_lon(this.long0 + Math.atan2(p.x, - p.y));\n    }\n    else {\n      lon = adjust_lon(this.long0 - Math.atan2(-p.x, p.y));\n    }\n    p.x = lon;\n    p.y = lat;\n    return p;\n  }\n  lon = adjust_lon(this.long0 + Math.atan2((p.x * sinz), rh * this.cos_p14 * cosz - p.y * this.sin_p14 * sinz));\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = [\"ortho\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAAQC,KAAK,EAAEC,OAAO,QAAO,qBAAqB;AAElD,OAAO,SAASC,IAAIA,CAAA,EAAG;EACrB;;EAEA;AACF;EACE,IAAI,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,IAAI,CAAC;EAClC,IAAI,CAACC,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAACF,IAAI,CAAC;AACpC;;AAEA;AACA;AACA,OAAO,SAASG,OAAOA,CAACC,CAAC,EAAE;EACzB,IAAIC,MAAM,EAAEC,MAAM,CAAC,CAAC;EACpB,IAAIC,IAAI,CAAC,CAAC;EACV,IAAIC,MAAM,CAAC,CAAC;EACZ,IAAIC,GAAG,CAAC,CAAC;EACT,IAAIC,CAAC,EAAEC,CAAC,EAAEC,CAAC;EACX,IAAIC,GAAG,GAAGT,CAAC,CAACO,CAAC;EACb,IAAIG,GAAG,GAAGV,CAAC,CAACQ,CAAC;EACb;AACF;EACEL,IAAI,GAAGf,UAAU,CAACqB,GAAG,GAAG,IAAI,CAACE,KAAK,CAAC;EAEnCV,MAAM,GAAGP,IAAI,CAACC,GAAG,CAACe,GAAG,CAAC;EACtBR,MAAM,GAAGR,IAAI,CAACI,GAAG,CAACY,GAAG,CAAC;EAEtBN,MAAM,GAAGV,IAAI,CAACI,GAAG,CAACK,IAAI,CAAC;EACvBG,CAAC,GAAG,IAAI,CAACb,OAAO,GAAGQ,MAAM,GAAG,IAAI,CAACJ,OAAO,GAAGK,MAAM,GAAGE,MAAM;EAC1DC,GAAG,GAAG,CAAC;EACP,IAAKC,CAAC,GAAG,CAAC,IAAMZ,IAAI,CAACkB,GAAG,CAACN,CAAC,CAAC,IAAIhB,KAAM,EAAE;IACrCiB,CAAC,GAAG,IAAI,CAACM,CAAC,GAAGR,GAAG,GAAGH,MAAM,GAAGR,IAAI,CAACC,GAAG,CAACQ,IAAI,CAAC;IAC1CK,CAAC,GAAG,IAAI,CAACM,EAAE,GAAG,IAAI,CAACD,CAAC,GAAGR,GAAG,IAAI,IAAI,CAACR,OAAO,GAAGI,MAAM,GAAG,IAAI,CAACR,OAAO,GAAGS,MAAM,GAAGE,MAAM,CAAC;EACvF;EACAJ,CAAC,CAACO,CAAC,GAAGA,CAAC;EACPP,CAAC,CAACQ,CAAC,GAAGA,CAAC;EACP,OAAOR,CAAC;AACV;AAEA,OAAO,SAASe,OAAOA,CAACf,CAAC,EAAE;EACzB,IAAIgB,EAAE,CAAC,CAAC;EACR,IAAIC,CAAC,CAAC,CAAC;EACP,IAAIC,IAAI,EAAEC,IAAI,CAAC,CAAC;EAChB,IAAIC,GAAG;EACP,IAAIX,GAAG,EAAEC,GAAG;EACZ;AACF;EACEV,CAAC,CAACO,CAAC,IAAI,IAAI,CAACc,EAAE;EACdrB,CAAC,CAACQ,CAAC,IAAI,IAAI,CAACM,EAAE;EACdE,EAAE,GAAGtB,IAAI,CAAC4B,IAAI,CAACtB,CAAC,CAACO,CAAC,GAAGP,CAAC,CAACO,CAAC,GAAGP,CAAC,CAACQ,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC;EACrCS,CAAC,GAAG5B,KAAK,CAAC2B,EAAE,GAAG,IAAI,CAACH,CAAC,CAAC;EAEtBK,IAAI,GAAGxB,IAAI,CAACC,GAAG,CAACsB,CAAC,CAAC;EAClBE,IAAI,GAAGzB,IAAI,CAACI,GAAG,CAACmB,CAAC,CAAC;EAElBR,GAAG,GAAG,IAAI,CAACE,KAAK;EAChB,IAAIjB,IAAI,CAACkB,GAAG,CAACI,EAAE,CAAC,IAAI1B,KAAK,EAAE;IACzBoB,GAAG,GAAG,IAAI,CAACd,IAAI;IACfI,CAAC,CAACO,CAAC,GAAGE,GAAG;IACTT,CAAC,CAACQ,CAAC,GAAGE,GAAG;IACT,OAAOV,CAAC;EACV;EACAU,GAAG,GAAGrB,KAAK,CAAC8B,IAAI,GAAG,IAAI,CAAC1B,OAAO,GAAIO,CAAC,CAACQ,CAAC,GAAGU,IAAI,GAAG,IAAI,CAACrB,OAAO,GAAImB,EAAE,CAAC;EACnEI,GAAG,GAAG1B,IAAI,CAACkB,GAAG,CAAC,IAAI,CAAChB,IAAI,CAAC,GAAGL,OAAO;EACnC,IAAIG,IAAI,CAACkB,GAAG,CAACQ,GAAG,CAAC,IAAI9B,KAAK,EAAE;IAC1B,IAAI,IAAI,CAACM,IAAI,IAAI,CAAC,EAAE;MAClBa,GAAG,GAAGrB,UAAU,CAAC,IAAI,CAACuB,KAAK,GAAGjB,IAAI,CAAC6B,KAAK,CAACvB,CAAC,CAACO,CAAC,EAAE,CAAEP,CAAC,CAACQ,CAAC,CAAC,CAAC;IACvD,CAAC,MACI;MACHC,GAAG,GAAGrB,UAAU,CAAC,IAAI,CAACuB,KAAK,GAAGjB,IAAI,CAAC6B,KAAK,CAAC,CAACvB,CAAC,CAACO,CAAC,EAAEP,CAAC,CAACQ,CAAC,CAAC,CAAC;IACtD;IACAR,CAAC,CAACO,CAAC,GAAGE,GAAG;IACTT,CAAC,CAACQ,CAAC,GAAGE,GAAG;IACT,OAAOV,CAAC;EACV;EACAS,GAAG,GAAGrB,UAAU,CAAC,IAAI,CAACuB,KAAK,GAAGjB,IAAI,CAAC6B,KAAK,CAAEvB,CAAC,CAACO,CAAC,GAAGW,IAAI,EAAGF,EAAE,GAAG,IAAI,CAACnB,OAAO,GAAGsB,IAAI,GAAGnB,CAAC,CAACQ,CAAC,GAAG,IAAI,CAACf,OAAO,GAAGyB,IAAI,CAAC,CAAC;EAC7GlB,CAAC,CAACO,CAAC,GAAGE,GAAG;EACTT,CAAC,CAACQ,CAAC,GAAGE,GAAG;EACT,OAAOV,CAAC;AACV;AAEA,OAAO,IAAIwB,KAAK,GAAG,CAAC,OAAO,CAAC;AAC5B,eAAe;EACbhC,IAAI,EAAEA,IAAI;EACVO,OAAO,EAAEA,OAAO;EAChBgB,OAAO,EAAEA,OAAO;EAChBS,KAAK,EAAEA;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}