{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport CaretDownFilled from \"@ant-design/icons/es/icons/CaretDownFilled\";\nimport FileOutlined from \"@ant-design/icons/es/icons/FileOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport MinusSquareOutlined from \"@ant-design/icons/es/icons/MinusSquareOutlined\";\nimport PlusSquareOutlined from \"@ant-design/icons/es/icons/PlusSquareOutlined\";\nimport classNames from 'classnames';\nimport { cloneElement } from '../../_util/reactNode';\nconst SwitcherIconCom = props => {\n  const {\n    prefixCls,\n    switcherIcon,\n    treeNodeProps,\n    showLine,\n    switcherLoadingIcon\n  } = props;\n  const {\n    isLeaf,\n    expanded,\n    loading\n  } = treeNodeProps;\n  if (loading) {\n    if (/*#__PURE__*/React.isValidElement(switcherLoadingIcon)) {\n      return switcherLoadingIcon;\n    }\n    return /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: `${prefixCls}-switcher-loading-icon`\n    });\n  }\n  let showLeafIcon;\n  if (showLine && typeof showLine === 'object') {\n    showLeafIcon = showLine.showLeafIcon;\n  }\n  if (isLeaf) {\n    if (!showLine) {\n      return null;\n    }\n    if (typeof showLeafIcon !== 'boolean' && !!showLeafIcon) {\n      const leafIcon = typeof showLeafIcon === 'function' ? showLeafIcon(treeNodeProps) : showLeafIcon;\n      const leafCls = `${prefixCls}-switcher-line-custom-icon`;\n      if (/*#__PURE__*/React.isValidElement(leafIcon)) {\n        return cloneElement(leafIcon, {\n          className: classNames(leafIcon.props.className || '', leafCls)\n        });\n      }\n      return leafIcon;\n    }\n    return showLeafIcon ? (/*#__PURE__*/React.createElement(FileOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    })) : (/*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-switcher-leaf-line`\n    }));\n  }\n  const switcherCls = `${prefixCls}-switcher-icon`;\n  const switcher = typeof switcherIcon === 'function' ? switcherIcon(treeNodeProps) : switcherIcon;\n  if (/*#__PURE__*/React.isValidElement(switcher)) {\n    return cloneElement(switcher, {\n      className: classNames(switcher.props.className || '', switcherCls)\n    });\n  }\n  if (switcher !== undefined) {\n    return switcher;\n  }\n  if (showLine) {\n    return expanded ? (/*#__PURE__*/React.createElement(MinusSquareOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    })) : (/*#__PURE__*/React.createElement(PlusSquareOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    }));\n  }\n  return /*#__PURE__*/React.createElement(CaretDownFilled, {\n    className: switcherCls\n  });\n};\nexport default SwitcherIconCom;", "map": {"version": 3, "names": ["React", "CaretDownFilled", "FileOutlined", "LoadingOutlined", "MinusSquareOutlined", "PlusSquareOutlined", "classNames", "cloneElement", "SwitcherIconCom", "props", "prefixCls", "switcherIcon", "treeNodeProps", "showLine", "switcherLoadingIcon", "<PERSON><PERSON><PERSON><PERSON>", "expanded", "loading", "isValidElement", "createElement", "className", "showLeafIcon", "leafIcon", "leafCls", "switcherCls", "switcher", "undefined"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/tree/utils/iconUtil.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport CaretDownFilled from \"@ant-design/icons/es/icons/CaretDownFilled\";\nimport FileOutlined from \"@ant-design/icons/es/icons/FileOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport MinusSquareOutlined from \"@ant-design/icons/es/icons/MinusSquareOutlined\";\nimport PlusSquareOutlined from \"@ant-design/icons/es/icons/PlusSquareOutlined\";\nimport classNames from 'classnames';\nimport { cloneElement } from '../../_util/reactNode';\nconst SwitcherIconCom = props => {\n  const {\n    prefixCls,\n    switcherIcon,\n    treeNodeProps,\n    showLine,\n    switcherLoadingIcon\n  } = props;\n  const {\n    isLeaf,\n    expanded,\n    loading\n  } = treeNodeProps;\n  if (loading) {\n    if (/*#__PURE__*/React.isValidElement(switcherLoadingIcon)) {\n      return switcherLoadingIcon;\n    }\n    return /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: `${prefixCls}-switcher-loading-icon`\n    });\n  }\n  let showLeafIcon;\n  if (showLine && typeof showLine === 'object') {\n    showLeafIcon = showLine.showLeafIcon;\n  }\n  if (isLeaf) {\n    if (!showLine) {\n      return null;\n    }\n    if (typeof showLeafIcon !== 'boolean' && !!showLeafIcon) {\n      const leafIcon = typeof showLeafIcon === 'function' ? showLeafIcon(treeNodeProps) : showLeafIcon;\n      const leafCls = `${prefixCls}-switcher-line-custom-icon`;\n      if (/*#__PURE__*/React.isValidElement(leafIcon)) {\n        return cloneElement(leafIcon, {\n          className: classNames(leafIcon.props.className || '', leafCls)\n        });\n      }\n      return leafIcon;\n    }\n    return showLeafIcon ? (/*#__PURE__*/React.createElement(FileOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    })) : (/*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-switcher-leaf-line`\n    }));\n  }\n  const switcherCls = `${prefixCls}-switcher-icon`;\n  const switcher = typeof switcherIcon === 'function' ? switcherIcon(treeNodeProps) : switcherIcon;\n  if (/*#__PURE__*/React.isValidElement(switcher)) {\n    return cloneElement(switcher, {\n      className: classNames(switcher.props.className || '', switcherCls)\n    });\n  }\n  if (switcher !== undefined) {\n    return switcher;\n  }\n  if (showLine) {\n    return expanded ? (/*#__PURE__*/React.createElement(MinusSquareOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    })) : (/*#__PURE__*/React.createElement(PlusSquareOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    }));\n  }\n  return /*#__PURE__*/React.createElement(CaretDownFilled, {\n    className: switcherCls\n  });\n};\nexport default SwitcherIconCom;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,mBAAmB,MAAM,gDAAgD;AAChF,OAAOC,kBAAkB,MAAM,+CAA+C;AAC9E,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,YAAY,QAAQ,uBAAuB;AACpD,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,SAAS;IACTC,YAAY;IACZC,aAAa;IACbC,QAAQ;IACRC;EACF,CAAC,GAAGL,KAAK;EACT,MAAM;IACJM,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC,GAAGL,aAAa;EACjB,IAAIK,OAAO,EAAE;IACX,IAAI,aAAajB,KAAK,CAACkB,cAAc,CAACJ,mBAAmB,CAAC,EAAE;MAC1D,OAAOA,mBAAmB;IAC5B;IACA,OAAO,aAAad,KAAK,CAACmB,aAAa,CAAChB,eAAe,EAAE;MACvDiB,SAAS,EAAE,GAAGV,SAAS;IACzB,CAAC,CAAC;EACJ;EACA,IAAIW,YAAY;EAChB,IAAIR,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC5CQ,YAAY,GAAGR,QAAQ,CAACQ,YAAY;EACtC;EACA,IAAIN,MAAM,EAAE;IACV,IAAI,CAACF,QAAQ,EAAE;MACb,OAAO,IAAI;IACb;IACA,IAAI,OAAOQ,YAAY,KAAK,SAAS,IAAI,CAAC,CAACA,YAAY,EAAE;MACvD,MAAMC,QAAQ,GAAG,OAAOD,YAAY,KAAK,UAAU,GAAGA,YAAY,CAACT,aAAa,CAAC,GAAGS,YAAY;MAChG,MAAME,OAAO,GAAG,GAAGb,SAAS,4BAA4B;MACxD,IAAI,aAAaV,KAAK,CAACkB,cAAc,CAACI,QAAQ,CAAC,EAAE;QAC/C,OAAOf,YAAY,CAACe,QAAQ,EAAE;UAC5BF,SAAS,EAAEd,UAAU,CAACgB,QAAQ,CAACb,KAAK,CAACW,SAAS,IAAI,EAAE,EAAEG,OAAO;QAC/D,CAAC,CAAC;MACJ;MACA,OAAOD,QAAQ;IACjB;IACA,OAAOD,YAAY,IAAI,aAAarB,KAAK,CAACmB,aAAa,CAACjB,YAAY,EAAE;MACpEkB,SAAS,EAAE,GAAGV,SAAS;IACzB,CAAC,CAAC,KAAK,aAAaV,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAE,GAAGV,SAAS;IACzB,CAAC,CAAC,CAAC;EACL;EACA,MAAMc,WAAW,GAAG,GAAGd,SAAS,gBAAgB;EAChD,MAAMe,QAAQ,GAAG,OAAOd,YAAY,KAAK,UAAU,GAAGA,YAAY,CAACC,aAAa,CAAC,GAAGD,YAAY;EAChG,IAAI,aAAaX,KAAK,CAACkB,cAAc,CAACO,QAAQ,CAAC,EAAE;IAC/C,OAAOlB,YAAY,CAACkB,QAAQ,EAAE;MAC5BL,SAAS,EAAEd,UAAU,CAACmB,QAAQ,CAAChB,KAAK,CAACW,SAAS,IAAI,EAAE,EAAEI,WAAW;IACnE,CAAC,CAAC;EACJ;EACA,IAAIC,QAAQ,KAAKC,SAAS,EAAE;IAC1B,OAAOD,QAAQ;EACjB;EACA,IAAIZ,QAAQ,EAAE;IACZ,OAAOG,QAAQ,IAAI,aAAahB,KAAK,CAACmB,aAAa,CAACf,mBAAmB,EAAE;MACvEgB,SAAS,EAAE,GAAGV,SAAS;IACzB,CAAC,CAAC,KAAK,aAAaV,KAAK,CAACmB,aAAa,CAACd,kBAAkB,EAAE;MAC1De,SAAS,EAAE,GAAGV,SAAS;IACzB,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAaV,KAAK,CAACmB,aAAa,CAAClB,eAAe,EAAE;IACvDmB,SAAS,EAAEI;EACb,CAAC,CAAC;AACJ,CAAC;AACD,eAAehB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}