{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\VideoPlayer.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport flvjs from 'flv.js';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst flv_BASE_URL = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\nconst FullscreenButton = ({\n  onClick\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  onClick: onClick,\n  style: {\n    position: 'absolute',\n    bottom: '10px',\n    right: '10px',\n    padding: '6px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 10,\n    transition: 'opacity 0.3s',\n    opacity: 0.6,\n    color: '#fff',\n    width: '24px',\n    height: '24px'\n  },\n  onMouseEnter: e => e.currentTarget.style.opacity = 1,\n  onMouseLeave: e => e.currentTarget.style.opacity = 0.6,\n  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    style: {\n      width: '16px',\n      height: '16px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M3 5v4h2V5h4V3H5c-1.1 0-2 .9-2 2zm2 10H3v4c0 1.1.9 2 2 2h4v-2H5v-4zm14 4h-4v2h4c1.1 0 2-.9 2-2v-4h-2v4zm0-16h-4v2h4v4h2V5c0-1.1-.9-2-2-2z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 6,\n  columnNumber: 3\n}, this);\n_c = FullscreenButton;\nconst VideoPlayer = ({\n  deviceId,\n  rtspUrl,\n  flvUrl\n}) => {\n  _s();\n  const videoRef = useRef(null);\n  const containerRef = useRef(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [hasRtspUrl, setHasRtspUrl] = useState(!!rtspUrl);\n  const [debugInfo, setDebugInfo] = useState('');\n  const playerRef = useRef(null);\n  const handleFullscreen = () => {\n    const container = containerRef.current;\n    if (!container) return;\n    if (!document.fullscreenElement) {\n      if (container.requestFullscreen) {\n        container.requestFullscreen().catch(e => console.warn('进入全屏失败:', e));\n      } else if (container.webkitRequestFullscreen) {\n        container.webkitRequestFullscreen();\n      } else if (container.msRequestFullscreen) {\n        container.msRequestFullscreen();\n      }\n    } else {\n      if (document.exitFullscreen) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen().catch(e => console.warn('退出全屏失败:', e));\n        }\n      } else if (document.webkitExitFullscreen) {\n        if (document.webkitFullscreenElement) {\n          document.webkitExitFullscreen();\n        }\n      } else if (document.msExitFullscreen) {\n        if (document.msFullscreenElement) {\n          document.msExitFullscreen();\n        }\n      }\n    }\n  };\n  const handleDoubleClick = () => {\n    const container = containerRef.current;\n    if (!container) return;\n    if (!document.fullscreenElement) {\n      if (container.requestFullscreen) {\n        container.requestFullscreen().catch(e => console.warn('进入全屏失败:', e));\n      } else if (container.webkitRequestFullscreen) {\n        container.webkitRequestFullscreen();\n      } else if (container.msRequestFullscreen) {\n        container.msRequestFullscreen();\n      }\n    } else {\n      if (document.exitFullscreen) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen().catch(e => console.warn('退出全屏失败:', e));\n        }\n      } else if (document.webkitExitFullscreen) {\n        if (document.webkitFullscreenElement) {\n          document.webkitExitFullscreen();\n        }\n      } else if (document.msExitFullscreen) {\n        if (document.msFullscreenElement) {\n          document.msExitFullscreen();\n        }\n      }\n    }\n  };\n  useEffect(() => {\n    if (!rtspUrl) {\n      setHasRtspUrl(false);\n      setLoading(false);\n      return;\n    }\n    setHasRtspUrl(true);\n    setLoading(true);\n    setError(null);\n    let retryCount = 0;\n    const maxRetries = 10;\n    let retryTimeout = null;\n    let healthCheckInterval = null;\n    let isDestroyed = false;\n\n    // 重连延迟计算（指数退避）\n    const getRetryDelay = count => {\n      return Math.min(1000 * Math.pow(1.5, count), 10000); // 最大10秒\n    };\n\n    // 检查流状态\n    const checkStreamHealth = async () => {\n      if (isDestroyed || !playerRef.current) return;\n      try {\n        const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n        const response = await fetch(url, {\n          method: 'HEAD',\n          cache: 'no-cache'\n        });\n        if (!response.ok) {\n          console.warn(`流健康检查失败: ${response.status}`);\n          if (playerRef.current && !isDestroyed) {\n            handlePlayerError('STREAM_UNAVAILABLE', 'Stream health check failed');\n          }\n        }\n      } catch (error) {\n        console.warn('流健康检查异常:', error);\n      }\n    };\n\n    // 处理播放器错误和重连\n    const handlePlayerError = (errorType, errorDetail) => {\n      if (isDestroyed) return;\n      console.error('播放器错误:', errorType, errorDetail);\n      setDebugInfo(`播放器错误: ${errorType}`);\n      if (retryCount < maxRetries) {\n        retryCount++;\n        const delay = getRetryDelay(retryCount);\n        console.log(`准备重试播放器 (${retryCount}/${maxRetries})，延迟 ${delay}ms`);\n        setDebugInfo(`重连中... (${retryCount}/${maxRetries})`);\n        retryTimeout = setTimeout(() => {\n          if (!isDestroyed) {\n            console.log('重新初始化播放器...');\n            const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n            initializePlayer(url);\n          }\n        }, delay);\n      } else {\n        console.error('播放器重试次数超过限制');\n        setError(`连接失败 (已重试${maxRetries}次)`);\n        setLoading(false);\n      }\n    };\n    const loadVideo = () => {\n      if (flvjs.isSupported()) {\n        const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n        setDebugInfo(`正在检查流状态...`);\n        console.log('开始初始化播放器...');\n        initializePlayer(url);\n        return;\n      } else {\n        setError('您的浏览器不支持FLV视频流');\n        setLoading(false);\n      }\n    };\n    const initializePlayer = flvUrl => {\n      console.log('开始初始化 FLV 播放器');\n      if (playerRef.current) {\n        playerRef.current.destroy();\n        playerRef.current = null;\n      }\n      const player = flvjs.createPlayer({\n        type: 'flv',\n        url: flvUrl,\n        isLive: true,\n        hasAudio: false,\n        hasVideo: true,\n        cors: true,\n        enableStashBuffer: false,\n        stashInitialSize: 128,\n        lazyLoad: false,\n        lazyLoadMaxDuration: 0,\n        fixAudioTimestampGap: false,\n        seekType: 'range',\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache'\n        }\n      });\n      player.on(flvjs.Events.ERROR, (errorType, errorDetail) => {\n        console.error('播放器错误:', errorType, errorDetail);\n        setDebugInfo(`播放器错误: ${errorType}`);\n        if (errorType === flvjs.ErrorTypes.NETWORK_ERROR) {\n          setTimeout(() => {\n            console.log('尝试重新加载...');\n            player.unload();\n            player.load();\n            player.play();\n          }, 1000);\n        }\n      });\n      player.on(flvjs.Events.MEDIA_INFO, mediaInfo => {\n        console.log('媒体信息:', mediaInfo);\n        setDebugInfo('获取到媒体信息');\n      });\n      player.on(flvjs.Events.STATISTICS_INFO, stats => {\n        console.log('播放统计:', stats);\n      });\n      try {\n        console.log('正在加载视频元素');\n        player.attachMediaElement(videoRef.current);\n        videoRef.current.addEventListener('playing', () => {\n          console.log('视频开始播放');\n          setLoading(false);\n        });\n        player.load();\n        playerRef.current = player;\n        player.play().catch(e => {\n          console.error('播放失败:', e);\n          setDebugInfo(`播放失败: ${e.message}`);\n        });\n      } catch (e) {\n        console.error('播放器初始化失败:', e);\n        setDebugInfo(`初始化失败: ${e.message}`);\n      }\n    };\n    loadVideo();\n    return () => {\n      if (retryTimeout) {\n        clearTimeout(retryTimeout);\n      }\n      if (playerRef.current) {\n        playerRef.current.pause();\n        playerRef.current.unload();\n        playerRef.current.detachMediaElement();\n        playerRef.current.destroy();\n        playerRef.current = null;\n      }\n    };\n  }, [deviceId, rtspUrl, flvUrl]);\n  if (!hasRtspUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#000',\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      onDoubleClick: handleDoubleClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u89C6\\u9891\\u6D41\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: \"(\\u8BE5\\u6444\\u50CF\\u5934\\u672A\\u914D\\u7F6ERTSP\\u5730\\u5740)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n        onClick: handleFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#000',\n        position: 'relative'\n      },\n      onDoubleClick: handleDoubleClick,\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        controls: false,\n        autoPlay: true,\n        muted: true,\n        playsInline: true,\n        style: {\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover'\n        },\n        onDoubleClick: handleDoubleClick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n        onClick: handleFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          color: '#fff',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u89C6\\u9891\\u6D41...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '11px',\n            marginTop: '4px'\n          },\n          children: debugInfo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#000',\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      onDoubleClick: handleDoubleClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u89C6\\u9891\\u6D41\\u9519\\u8BEF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: [\"(\", error, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n        onClick: handleFullscreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    style: {\n      width: '100%',\n      height: '100%',\n      backgroundColor: '#000',\n      position: 'relative'\n    },\n    onDoubleClick: handleDoubleClick,\n    children: [/*#__PURE__*/_jsxDEV(\"video\", {\n      ref: videoRef,\n      controls: false,\n      autoPlay: true,\n      muted: true,\n      playsInline: true,\n      style: {\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      },\n      onDoubleClick: handleDoubleClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n      onClick: handleFullscreen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '50%',\n        left: '50%',\n        transform: 'translate(-50%, -50%)',\n        color: '#fff',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u89C6\\u9891\\u6D41...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: debugInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 363,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlayer, \"FQSLvWf576lbC5TR6Jtpj6TPt6k=\");\n_c2 = VideoPlayer;\nexport default VideoPlayer;\nvar _c, _c2;\n$RefreshReg$(_c, \"FullscreenButton\");\n$RefreshReg$(_c2, \"VideoPlayer\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "flvjs", "jsxDEV", "_jsxDEV", "flv_BASE_URL", "process", "env", "REACT_APP_FLV_URL", "FullscreenButton", "onClick", "style", "position", "bottom", "right", "padding", "backgroundColor", "borderRadius", "cursor", "display", "alignItems", "justifyContent", "zIndex", "transition", "opacity", "color", "width", "height", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "children", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "VideoPlayer", "deviceId", "rtspUrl", "flvUrl", "_s", "videoRef", "containerRef", "error", "setError", "loading", "setLoading", "hasRtspUrl", "setHasRtspUrl", "debugInfo", "setDebugInfo", "playerRef", "handleFullscreen", "container", "current", "document", "fullscreenElement", "requestFullscreen", "catch", "console", "warn", "webkitRequestFullscreen", "msRequestFullscreen", "exitFullscreen", "webkitExitFullscreen", "webkitFullscreenElement", "msExitFullscreen", "msFullscreenElement", "handleDoubleClick", "retryCount", "maxRetries", "retryTimeout", "healthCheckInterval", "isDestroyed", "getRetryDelay", "count", "Math", "min", "pow", "checkStreamHealth", "url", "response", "fetch", "method", "cache", "ok", "status", "handlePlayerError", "errorType", "errorDetail", "delay", "log", "setTimeout", "initializePlayer", "loadVideo", "isSupported", "destroy", "player", "createPlayer", "type", "isLive", "hasAudio", "hasVideo", "cors", "enableStashBuffer", "stashInitialSize", "lazyLoad", "lazyLoadMaxDuration", "fixAudioTimestampGap", "seekType", "headers", "on", "Events", "ERROR", "ErrorTypes", "NETWORK_ERROR", "unload", "load", "play", "MEDIA_INFO", "mediaInfo", "STATISTICS_INFO", "stats", "attachMediaElement", "addEventListener", "message", "clearTimeout", "pause", "detachMediaElement", "ref", "onDoubleClick", "fontSize", "marginTop", "controls", "autoPlay", "muted", "playsInline", "objectFit", "top", "left", "transform", "textAlign", "_c2", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/VideoPlayer.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport flvjs from 'flv.js';\nconst flv_BASE_URL = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\n\nconst FullscreenButton = ({ onClick }) => (\n  <div\n    onClick={onClick}\n    style={{\n      position: 'absolute',\n      bottom: '10px',\n      right: '10px',\n      padding: '6px',\n      backgroundColor: 'rgba(0, 0, 0, 0.5)',\n      borderRadius: '4px',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 10,\n      transition: 'opacity 0.3s',\n      opacity: 0.6,\n      color: '#fff',\n      width: '24px',\n      height: '24px'\n    }}\n    onMouseEnter={(e) => e.currentTarget.style.opacity = 1}\n    onMouseLeave={(e) => e.currentTarget.style.opacity = 0.6}\n  >\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" style={{ width: '16px', height: '16px' }}>\n      <path d=\"M3 5v4h2V5h4V3H5c-1.1 0-2 .9-2 2zm2 10H3v4c0 1.1.9 2 2 2h4v-2H5v-4zm14 4h-4v2h4c1.1 0 2-.9 2-2v-4h-2v4zm0-16h-4v2h4v4h2V5c0-1.1-.9-2-2-2z\"/>\n    </svg>\n  </div>\n);\n\nconst VideoPlayer = ({ deviceId, rtspUrl, flvUrl }) => {\n  const videoRef = useRef(null);\n  const containerRef = useRef(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [hasRtspUrl, setHasRtspUrl] = useState(!!rtspUrl);\n  const [debugInfo, setDebugInfo] = useState('');\n  const playerRef = useRef(null);\n\n  const handleFullscreen = () => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    if (!document.fullscreenElement) {\n      if (container.requestFullscreen) {\n        container.requestFullscreen().catch(e => console.warn('进入全屏失败:', e));\n      } else if (container.webkitRequestFullscreen) {\n        container.webkitRequestFullscreen();\n      } else if (container.msRequestFullscreen) {\n        container.msRequestFullscreen();\n      }\n    } else {\n      if (document.exitFullscreen) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen().catch(e => console.warn('退出全屏失败:', e));\n        }\n      } else if (document.webkitExitFullscreen) {\n        if (document.webkitFullscreenElement) {\n          document.webkitExitFullscreen();\n        }\n      } else if (document.msExitFullscreen) {\n        if (document.msFullscreenElement) {\n          document.msExitFullscreen();\n        }\n      }\n    }\n  };\n\n  const handleDoubleClick = () => {\n    const container = containerRef.current;\n    if (!container) return;\n    if (!document.fullscreenElement) {\n      if (container.requestFullscreen) {\n        container.requestFullscreen().catch(e => console.warn('进入全屏失败:', e));\n      } else if (container.webkitRequestFullscreen) {\n        container.webkitRequestFullscreen();\n      } else if (container.msRequestFullscreen) {\n        container.msRequestFullscreen();\n      }\n    } else {\n      if (document.exitFullscreen) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen().catch(e => console.warn('退出全屏失败:', e));\n        }\n      } else if (document.webkitExitFullscreen) {\n        if (document.webkitFullscreenElement) {\n          document.webkitExitFullscreen();\n        }\n      } else if (document.msExitFullscreen) {\n        if (document.msFullscreenElement) {\n          document.msExitFullscreen();\n        }\n      }\n    }\n  };\n\n  useEffect(() => {\n    if (!rtspUrl) {\n      setHasRtspUrl(false);\n      setLoading(false);\n      return;\n    }\n\n    setHasRtspUrl(true);\n    setLoading(true);\n    setError(null);\n\n    let retryCount = 0;\n    const maxRetries = 10;\n    let retryTimeout = null;\n    let healthCheckInterval = null;\n    let isDestroyed = false;\n\n    // 重连延迟计算（指数退避）\n    const getRetryDelay = (count) => {\n      return Math.min(1000 * Math.pow(1.5, count), 10000); // 最大10秒\n    };\n\n    // 检查流状态\n    const checkStreamHealth = async () => {\n      if (isDestroyed || !playerRef.current) return;\n\n      try {\n        const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n        const response = await fetch(url, {\n          method: 'HEAD',\n          cache: 'no-cache'\n        });\n\n        if (!response.ok) {\n          console.warn(`流健康检查失败: ${response.status}`);\n          if (playerRef.current && !isDestroyed) {\n            handlePlayerError('STREAM_UNAVAILABLE', 'Stream health check failed');\n          }\n        }\n      } catch (error) {\n        console.warn('流健康检查异常:', error);\n      }\n    };\n\n    // 处理播放器错误和重连\n    const handlePlayerError = (errorType, errorDetail) => {\n      if (isDestroyed) return;\n\n      console.error('播放器错误:', errorType, errorDetail);\n      setDebugInfo(`播放器错误: ${errorType}`);\n\n      if (retryCount < maxRetries) {\n        retryCount++;\n        const delay = getRetryDelay(retryCount);\n\n        console.log(`准备重试播放器 (${retryCount}/${maxRetries})，延迟 ${delay}ms`);\n        setDebugInfo(`重连中... (${retryCount}/${maxRetries})`);\n\n        retryTimeout = setTimeout(() => {\n          if (!isDestroyed) {\n            console.log('重新初始化播放器...');\n            const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n            initializePlayer(url);\n          }\n        }, delay);\n      } else {\n        console.error('播放器重试次数超过限制');\n        setError(`连接失败 (已重试${maxRetries}次)`);\n        setLoading(false);\n      }\n    };\n\n    const loadVideo = () => {\n      if (flvjs.isSupported()) {\n        const url = flvUrl || `${flv_BASE_URL}/live/${deviceId}.flv`;\n        setDebugInfo(`正在检查流状态...`);\n\n        console.log('开始初始化播放器...');\n        initializePlayer(url);\n        return;\n      } else {\n        setError('您的浏览器不支持FLV视频流');\n        setLoading(false);\n      }\n    };\n\n    const initializePlayer = (flvUrl) => {\n      console.log('开始初始化 FLV 播放器');\n      if (playerRef.current) {\n        playerRef.current.destroy();\n        playerRef.current = null;\n      }\n\n      const player = flvjs.createPlayer({\n        type: 'flv',\n        url: flvUrl,\n        isLive: true,\n        hasAudio: false,\n        hasVideo: true,\n        cors: true,\n        enableStashBuffer: false,\n        stashInitialSize: 128,\n        lazyLoad: false,\n        lazyLoadMaxDuration: 0,\n        fixAudioTimestampGap: false,\n        seekType: 'range',\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache'\n        }\n      });\n\n      player.on(flvjs.Events.ERROR, (errorType, errorDetail) => {\n        console.error('播放器错误:', errorType, errorDetail);\n        setDebugInfo(`播放器错误: ${errorType}`);\n        \n        if (errorType === flvjs.ErrorTypes.NETWORK_ERROR) {\n          setTimeout(() => {\n            console.log('尝试重新加载...');\n            player.unload();\n            player.load();\n            player.play();\n          }, 1000);\n        }\n      });\n\n      player.on(flvjs.Events.MEDIA_INFO, (mediaInfo) => {\n        console.log('媒体信息:', mediaInfo);\n        setDebugInfo('获取到媒体信息');\n      });\n\n      player.on(flvjs.Events.STATISTICS_INFO, (stats) => {\n        console.log('播放统计:', stats);\n      });\n\n      try {\n        console.log('正在加载视频元素');\n        player.attachMediaElement(videoRef.current);\n        videoRef.current.addEventListener('playing', () => {\n          console.log('视频开始播放');\n          setLoading(false);\n        });\n        player.load();\n        playerRef.current = player;\n        player.play().catch(e => {\n          console.error('播放失败:', e);\n          setDebugInfo(`播放失败: ${e.message}`);\n        });\n      } catch (e) {\n        console.error('播放器初始化失败:', e);\n        setDebugInfo(`初始化失败: ${e.message}`);\n      }\n    };\n\n    loadVideo();\n\n    return () => {\n      if (retryTimeout) {\n        clearTimeout(retryTimeout);\n      }\n      if (playerRef.current) {\n        playerRef.current.pause();\n        playerRef.current.unload();\n        playerRef.current.detachMediaElement();\n        playerRef.current.destroy();\n        playerRef.current = null;\n      }\n    };\n  }, [deviceId, rtspUrl, flvUrl]);\n\n  if (!hasRtspUrl) {\n    return (\n      <div\n        ref={containerRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          backgroundColor: '#000',\n          position: 'relative',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}\n        onDoubleClick={handleDoubleClick}\n      >\n        <div>视频流</div>\n        <div style={{ fontSize: '11px', marginTop: '4px' }}>\n          (该摄像头未配置RTSP地址)\n        </div>\n        <FullscreenButton onClick={handleFullscreen} />\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div\n        ref={containerRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          backgroundColor: '#000',\n          position: 'relative'\n        }}\n        onDoubleClick={handleDoubleClick}\n      >\n        <video\n          ref={videoRef}\n          controls={false}\n          autoPlay\n          muted\n          playsInline\n          style={{\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n          }}\n          onDoubleClick={handleDoubleClick}\n        />\n        <FullscreenButton onClick={handleFullscreen} />\n        <div style={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          color: '#fff',\n          textAlign: 'center'\n        }}>\n          <div>正在加载视频流...</div>\n          <div style={{ fontSize: '11px', marginTop: '4px' }}>\n            {debugInfo}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div\n        ref={containerRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          backgroundColor: '#000',\n          position: 'relative',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}\n        onDoubleClick={handleDoubleClick}\n      >\n        <div>视频流错误</div>\n        <div style={{ fontSize: '11px', marginTop: '4px' }}>\n          ({error})\n        </div>\n        <FullscreenButton onClick={handleFullscreen} />\n      </div>\n    );\n  }\n\n  return (\n    <div\n      ref={containerRef}\n      style={{\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#000',\n        position: 'relative'\n      }}\n      onDoubleClick={handleDoubleClick}\n    >\n      <video\n        ref={videoRef}\n        controls={false}\n        autoPlay\n        muted\n        playsInline\n        style={{\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover',\n        }}\n        onDoubleClick={handleDoubleClick}\n      />\n      <FullscreenButton onClick={handleFullscreen} />\n      {loading && (\n        <div style={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          color: '#fff',\n          textAlign: 'center'\n        }}>\n          <div>正在加载视频流...</div>\n          <div style={{ fontSize: '11px', marginTop: '4px' }}>\n            {debugInfo}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default VideoPlayer; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC3B,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAQ,CAAC,kBACnCN,OAAA;EACEM,OAAO,EAAEA,OAAQ;EACjBC,KAAK,EAAE;IACLC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,KAAK;IACdC,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,cAAc;IAC1BC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAE;EACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACa,OAAO,GAAG,CAAE;EACvDO,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACa,OAAO,GAAG,GAAI;EAAAQ,QAAA,eAEzD5B,OAAA;IAAK6B,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,cAAc;IAACvB,KAAK,EAAE;MAAEe,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAK,QAAA,eACpF5B,OAAA;MAAM+B,CAAC,EAAC;IAA2I;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClJ;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,EAAA,GA5BI/B,gBAAgB;AA8BtB,MAAMgC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAMC,QAAQ,GAAG9C,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM+C,YAAY,GAAG/C,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC0C,OAAO,CAAC;EACvD,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAMuD,SAAS,GAAGxD,MAAM,CAAC,IAAI,CAAC;EAE9B,MAAMyD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAGX,YAAY,CAACY,OAAO;IACtC,IAAI,CAACD,SAAS,EAAE;IAEhB,IAAI,CAACE,QAAQ,CAACC,iBAAiB,EAAE;MAC/B,IAAIH,SAAS,CAACI,iBAAiB,EAAE;QAC/BJ,SAAS,CAACI,iBAAiB,CAAC,CAAC,CAACC,KAAK,CAAClC,CAAC,IAAImC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEpC,CAAC,CAAC,CAAC;MACtE,CAAC,MAAM,IAAI6B,SAAS,CAACQ,uBAAuB,EAAE;QAC5CR,SAAS,CAACQ,uBAAuB,CAAC,CAAC;MACrC,CAAC,MAAM,IAAIR,SAAS,CAACS,mBAAmB,EAAE;QACxCT,SAAS,CAACS,mBAAmB,CAAC,CAAC;MACjC;IACF,CAAC,MAAM;MACL,IAAIP,QAAQ,CAACQ,cAAc,EAAE;QAC3B,IAAIR,QAAQ,CAACC,iBAAiB,EAAE;UAC9BD,QAAQ,CAACQ,cAAc,CAAC,CAAC,CAACL,KAAK,CAAClC,CAAC,IAAImC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEpC,CAAC,CAAC,CAAC;QAClE;MACF,CAAC,MAAM,IAAI+B,QAAQ,CAACS,oBAAoB,EAAE;QACxC,IAAIT,QAAQ,CAACU,uBAAuB,EAAE;UACpCV,QAAQ,CAACS,oBAAoB,CAAC,CAAC;QACjC;MACF,CAAC,MAAM,IAAIT,QAAQ,CAACW,gBAAgB,EAAE;QACpC,IAAIX,QAAQ,CAACY,mBAAmB,EAAE;UAChCZ,QAAQ,CAACW,gBAAgB,CAAC,CAAC;QAC7B;MACF;IACF;EACF,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMf,SAAS,GAAGX,YAAY,CAACY,OAAO;IACtC,IAAI,CAACD,SAAS,EAAE;IAChB,IAAI,CAACE,QAAQ,CAACC,iBAAiB,EAAE;MAC/B,IAAIH,SAAS,CAACI,iBAAiB,EAAE;QAC/BJ,SAAS,CAACI,iBAAiB,CAAC,CAAC,CAACC,KAAK,CAAClC,CAAC,IAAImC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEpC,CAAC,CAAC,CAAC;MACtE,CAAC,MAAM,IAAI6B,SAAS,CAACQ,uBAAuB,EAAE;QAC5CR,SAAS,CAACQ,uBAAuB,CAAC,CAAC;MACrC,CAAC,MAAM,IAAIR,SAAS,CAACS,mBAAmB,EAAE;QACxCT,SAAS,CAACS,mBAAmB,CAAC,CAAC;MACjC;IACF,CAAC,MAAM;MACL,IAAIP,QAAQ,CAACQ,cAAc,EAAE;QAC3B,IAAIR,QAAQ,CAACC,iBAAiB,EAAE;UAC9BD,QAAQ,CAACQ,cAAc,CAAC,CAAC,CAACL,KAAK,CAAClC,CAAC,IAAImC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEpC,CAAC,CAAC,CAAC;QAClE;MACF,CAAC,MAAM,IAAI+B,QAAQ,CAACS,oBAAoB,EAAE;QACxC,IAAIT,QAAQ,CAACU,uBAAuB,EAAE;UACpCV,QAAQ,CAACS,oBAAoB,CAAC,CAAC;QACjC;MACF,CAAC,MAAM,IAAIT,QAAQ,CAACW,gBAAgB,EAAE;QACpC,IAAIX,QAAQ,CAACY,mBAAmB,EAAE;UAChCZ,QAAQ,CAACW,gBAAgB,CAAC,CAAC;QAC7B;MACF;IACF;EACF,CAAC;EAEDxE,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,OAAO,EAAE;MACZU,aAAa,CAAC,KAAK,CAAC;MACpBF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAE,aAAa,CAAC,IAAI,CAAC;IACnBF,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAIyB,UAAU,GAAG,CAAC;IAClB,MAAMC,UAAU,GAAG,EAAE;IACrB,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAIC,mBAAmB,GAAG,IAAI;IAC9B,IAAIC,WAAW,GAAG,KAAK;;IAEvB;IACA,MAAMC,aAAa,GAAIC,KAAK,IAAK;MAC/B,OAAOC,IAAI,CAACC,GAAG,CAAC,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEH,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACvD,CAAC;;IAED;IACA,MAAMI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAIN,WAAW,IAAI,CAACtB,SAAS,CAACG,OAAO,EAAE;MAEvC,IAAI;QACF,MAAM0B,GAAG,GAAGzC,MAAM,IAAI,GAAGvC,YAAY,SAASqC,QAAQ,MAAM;QAC5D,MAAM4C,QAAQ,GAAG,MAAMC,KAAK,CAACF,GAAG,EAAE;UAChCG,MAAM,EAAE,MAAM;UACdC,KAAK,EAAE;QACT,CAAC,CAAC;QAEF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;UAChB1B,OAAO,CAACC,IAAI,CAAC,YAAYqB,QAAQ,CAACK,MAAM,EAAE,CAAC;UAC3C,IAAInC,SAAS,CAACG,OAAO,IAAI,CAACmB,WAAW,EAAE;YACrCc,iBAAiB,CAAC,oBAAoB,EAAE,4BAA4B,CAAC;UACvE;QACF;MACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdgB,OAAO,CAACC,IAAI,CAAC,UAAU,EAAEjB,KAAK,CAAC;MACjC;IACF,CAAC;;IAED;IACA,MAAM4C,iBAAiB,GAAGA,CAACC,SAAS,EAAEC,WAAW,KAAK;MACpD,IAAIhB,WAAW,EAAE;MAEjBd,OAAO,CAAChB,KAAK,CAAC,QAAQ,EAAE6C,SAAS,EAAEC,WAAW,CAAC;MAC/CvC,YAAY,CAAC,UAAUsC,SAAS,EAAE,CAAC;MAEnC,IAAInB,UAAU,GAAGC,UAAU,EAAE;QAC3BD,UAAU,EAAE;QACZ,MAAMqB,KAAK,GAAGhB,aAAa,CAACL,UAAU,CAAC;QAEvCV,OAAO,CAACgC,GAAG,CAAC,YAAYtB,UAAU,IAAIC,UAAU,QAAQoB,KAAK,IAAI,CAAC;QAClExC,YAAY,CAAC,WAAWmB,UAAU,IAAIC,UAAU,GAAG,CAAC;QAEpDC,YAAY,GAAGqB,UAAU,CAAC,MAAM;UAC9B,IAAI,CAACnB,WAAW,EAAE;YAChBd,OAAO,CAACgC,GAAG,CAAC,aAAa,CAAC;YAC1B,MAAMX,GAAG,GAAGzC,MAAM,IAAI,GAAGvC,YAAY,SAASqC,QAAQ,MAAM;YAC5DwD,gBAAgB,CAACb,GAAG,CAAC;UACvB;QACF,CAAC,EAAEU,KAAK,CAAC;MACX,CAAC,MAAM;QACL/B,OAAO,CAAChB,KAAK,CAAC,aAAa,CAAC;QAC5BC,QAAQ,CAAC,YAAY0B,UAAU,IAAI,CAAC;QACpCxB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,MAAMgD,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAIjG,KAAK,CAACkG,WAAW,CAAC,CAAC,EAAE;QACvB,MAAMf,GAAG,GAAGzC,MAAM,IAAI,GAAGvC,YAAY,SAASqC,QAAQ,MAAM;QAC5Da,YAAY,CAAC,YAAY,CAAC;QAE1BS,OAAO,CAACgC,GAAG,CAAC,aAAa,CAAC;QAC1BE,gBAAgB,CAACb,GAAG,CAAC;QACrB;MACF,CAAC,MAAM;QACLpC,QAAQ,CAAC,gBAAgB,CAAC;QAC1BE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,MAAM+C,gBAAgB,GAAItD,MAAM,IAAK;MACnCoB,OAAO,CAACgC,GAAG,CAAC,eAAe,CAAC;MAC5B,IAAIxC,SAAS,CAACG,OAAO,EAAE;QACrBH,SAAS,CAACG,OAAO,CAAC0C,OAAO,CAAC,CAAC;QAC3B7C,SAAS,CAACG,OAAO,GAAG,IAAI;MAC1B;MAEA,MAAM2C,MAAM,GAAGpG,KAAK,CAACqG,YAAY,CAAC;QAChCC,IAAI,EAAE,KAAK;QACXnB,GAAG,EAAEzC,MAAM;QACX6D,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,IAAI;QACVC,iBAAiB,EAAE,KAAK;QACxBC,gBAAgB,EAAE,GAAG;QACrBC,QAAQ,EAAE,KAAK;QACfC,mBAAmB,EAAE,CAAC;QACtBC,oBAAoB,EAAE,KAAK;QAC3BC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU;UAC3B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MAEFb,MAAM,CAACc,EAAE,CAAClH,KAAK,CAACmH,MAAM,CAACC,KAAK,EAAE,CAACzB,SAAS,EAAEC,WAAW,KAAK;QACxD9B,OAAO,CAAChB,KAAK,CAAC,QAAQ,EAAE6C,SAAS,EAAEC,WAAW,CAAC;QAC/CvC,YAAY,CAAC,UAAUsC,SAAS,EAAE,CAAC;QAEnC,IAAIA,SAAS,KAAK3F,KAAK,CAACqH,UAAU,CAACC,aAAa,EAAE;UAChDvB,UAAU,CAAC,MAAM;YACfjC,OAAO,CAACgC,GAAG,CAAC,WAAW,CAAC;YACxBM,MAAM,CAACmB,MAAM,CAAC,CAAC;YACfnB,MAAM,CAACoB,IAAI,CAAC,CAAC;YACbpB,MAAM,CAACqB,IAAI,CAAC,CAAC;UACf,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC;MAEFrB,MAAM,CAACc,EAAE,CAAClH,KAAK,CAACmH,MAAM,CAACO,UAAU,EAAGC,SAAS,IAAK;QAChD7D,OAAO,CAACgC,GAAG,CAAC,OAAO,EAAE6B,SAAS,CAAC;QAC/BtE,YAAY,CAAC,SAAS,CAAC;MACzB,CAAC,CAAC;MAEF+C,MAAM,CAACc,EAAE,CAAClH,KAAK,CAACmH,MAAM,CAACS,eAAe,EAAGC,KAAK,IAAK;QACjD/D,OAAO,CAACgC,GAAG,CAAC,OAAO,EAAE+B,KAAK,CAAC;MAC7B,CAAC,CAAC;MAEF,IAAI;QACF/D,OAAO,CAACgC,GAAG,CAAC,UAAU,CAAC;QACvBM,MAAM,CAAC0B,kBAAkB,CAAClF,QAAQ,CAACa,OAAO,CAAC;QAC3Cb,QAAQ,CAACa,OAAO,CAACsE,gBAAgB,CAAC,SAAS,EAAE,MAAM;UACjDjE,OAAO,CAACgC,GAAG,CAAC,QAAQ,CAAC;UACrB7C,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,CAAC;QACFmD,MAAM,CAACoB,IAAI,CAAC,CAAC;QACblE,SAAS,CAACG,OAAO,GAAG2C,MAAM;QAC1BA,MAAM,CAACqB,IAAI,CAAC,CAAC,CAAC5D,KAAK,CAAClC,CAAC,IAAI;UACvBmC,OAAO,CAAChB,KAAK,CAAC,OAAO,EAAEnB,CAAC,CAAC;UACzB0B,YAAY,CAAC,SAAS1B,CAAC,CAACqG,OAAO,EAAE,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOrG,CAAC,EAAE;QACVmC,OAAO,CAAChB,KAAK,CAAC,WAAW,EAAEnB,CAAC,CAAC;QAC7B0B,YAAY,CAAC,UAAU1B,CAAC,CAACqG,OAAO,EAAE,CAAC;MACrC;IACF,CAAC;IAED/B,SAAS,CAAC,CAAC;IAEX,OAAO,MAAM;MACX,IAAIvB,YAAY,EAAE;QAChBuD,YAAY,CAACvD,YAAY,CAAC;MAC5B;MACA,IAAIpB,SAAS,CAACG,OAAO,EAAE;QACrBH,SAAS,CAACG,OAAO,CAACyE,KAAK,CAAC,CAAC;QACzB5E,SAAS,CAACG,OAAO,CAAC8D,MAAM,CAAC,CAAC;QAC1BjE,SAAS,CAACG,OAAO,CAAC0E,kBAAkB,CAAC,CAAC;QACtC7E,SAAS,CAACG,OAAO,CAAC0C,OAAO,CAAC,CAAC;QAC3B7C,SAAS,CAACG,OAAO,GAAG,IAAI;MAC1B;IACF,CAAC;EACH,CAAC,EAAE,CAACjB,QAAQ,EAAEC,OAAO,EAAEC,MAAM,CAAC,CAAC;EAE/B,IAAI,CAACQ,UAAU,EAAE;IACf,oBACEhD,OAAA;MACEkI,GAAG,EAAEvF,YAAa;MAClBpC,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdX,eAAe,EAAE,MAAM;QACvBJ,QAAQ,EAAE,UAAU;QACpBO,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MACFkH,aAAa,EAAE9D,iBAAkB;MAAAzC,QAAA,gBAEjC5B,OAAA;QAAA4B,QAAA,EAAK;MAAG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACdnC,OAAA;QAAKO,KAAK,EAAE;UAAE6H,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAzG,QAAA,EAAC;MAEpD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnC,OAAA,CAACK,gBAAgB;QAACC,OAAO,EAAE+C;MAAiB;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAEV;EAEA,IAAIW,OAAO,EAAE;IACX,oBACE9C,OAAA;MACEkI,GAAG,EAAEvF,YAAa;MAClBpC,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdX,eAAe,EAAE,MAAM;QACvBJ,QAAQ,EAAE;MACZ,CAAE;MACF2H,aAAa,EAAE9D,iBAAkB;MAAAzC,QAAA,gBAEjC5B,OAAA;QACEkI,GAAG,EAAExF,QAAS;QACd4F,QAAQ,EAAE,KAAM;QAChBC,QAAQ;QACRC,KAAK;QACLC,WAAW;QACXlI,KAAK,EAAE;UACLe,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdmH,SAAS,EAAE;QACb,CAAE;QACFP,aAAa,EAAE9D;MAAkB;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACFnC,OAAA,CAACK,gBAAgB;QAACC,OAAO,EAAE+C;MAAiB;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CnC,OAAA;QAAKO,KAAK,EAAE;UACVC,QAAQ,EAAE,UAAU;UACpBmI,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE,uBAAuB;UAClCxH,KAAK,EAAE,MAAM;UACbyH,SAAS,EAAE;QACb,CAAE;QAAAlH,QAAA,gBACA5B,OAAA;UAAA4B,QAAA,EAAK;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrBnC,OAAA;UAAKO,KAAK,EAAE;YAAE6H,QAAQ,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAM,CAAE;UAAAzG,QAAA,EAChDsB;QAAS;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIS,KAAK,EAAE;IACT,oBACE5C,OAAA;MACEkI,GAAG,EAAEvF,YAAa;MAClBpC,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdX,eAAe,EAAE,MAAM;QACvBJ,QAAQ,EAAE,UAAU;QACpBO,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MACFkH,aAAa,EAAE9D,iBAAkB;MAAAzC,QAAA,gBAEjC5B,OAAA;QAAA4B,QAAA,EAAK;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChBnC,OAAA;QAAKO,KAAK,EAAE;UAAE6H,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAzG,QAAA,GAAC,GACjD,EAACgB,KAAK,EAAC,GACV;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnC,OAAA,CAACK,gBAAgB;QAACC,OAAO,EAAE+C;MAAiB;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAEV;EAEA,oBACEnC,OAAA;IACEkI,GAAG,EAAEvF,YAAa;IAClBpC,KAAK,EAAE;MACLe,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdX,eAAe,EAAE,MAAM;MACvBJ,QAAQ,EAAE;IACZ,CAAE;IACF2H,aAAa,EAAE9D,iBAAkB;IAAAzC,QAAA,gBAEjC5B,OAAA;MACEkI,GAAG,EAAExF,QAAS;MACd4F,QAAQ,EAAE,KAAM;MAChBC,QAAQ;MACRC,KAAK;MACLC,WAAW;MACXlI,KAAK,EAAE;QACLe,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdmH,SAAS,EAAE;MACb,CAAE;MACFP,aAAa,EAAE9D;IAAkB;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,eACFnC,OAAA,CAACK,gBAAgB;MAACC,OAAO,EAAE+C;IAAiB;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC9CW,OAAO,iBACN9C,OAAA;MAAKO,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBmI,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,uBAAuB;QAClCxH,KAAK,EAAE,MAAM;QACbyH,SAAS,EAAE;MACb,CAAE;MAAAlH,QAAA,gBACA5B,OAAA;QAAA4B,QAAA,EAAK;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrBnC,OAAA;QAAKO,KAAK,EAAE;UAAE6H,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAzG,QAAA,EAChDsB;MAAS;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACM,EAAA,CAjXIJ,WAAW;AAAA0G,GAAA,GAAX1G,WAAW;AAmXjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAA2G,GAAA;AAAAC,YAAA,CAAA5G,EAAA;AAAA4G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}