{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { removeCSS, updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { useContext } from 'react';\nimport StyleContext, { ATTR_MARK, ATTR_TOKEN, CSS_IN_JS_INSTANCE } from \"../StyleContext\";\nimport { isClientSide, toStyleStr } from \"../util\";\nimport { transformToken } from \"../util/css-variables\";\nimport useGlobalCache from \"./useGlobalCache\";\nimport { uniqueHash } from \"./useStyleRegister\";\nexport var CSS_VAR_PREFIX = 'cssVar';\nvar useCSSVarRegister = function useCSSVarRegister(config, fn) {\n  var key = config.key,\n    prefix = config.prefix,\n    unitless = config.unitless,\n    ignore = config.ignore,\n    token = config.token,\n    _config$scope = config.scope,\n    scope = _config$scope === void 0 ? '' : _config$scope;\n  var _useContext = useContext(StyleContext),\n    instanceId = _useContext.cache.instanceId,\n    container = _useContext.container;\n  var tokenKey = token._tokenKey;\n  var stylePath = [].concat(_toConsumableArray(config.path), [key, scope, tokenKey]);\n  var cache = useGlobalCache(CSS_VAR_PREFIX, stylePath, function () {\n    var originToken = fn();\n    var _transformToken = transformToken(originToken, key, {\n        prefix: prefix,\n        unitless: unitless,\n        ignore: ignore,\n        scope: scope\n      }),\n      _transformToken2 = _slicedToArray(_transformToken, 2),\n      mergedToken = _transformToken2[0],\n      cssVarsStr = _transformToken2[1];\n    var styleId = uniqueHash(stylePath, cssVarsStr);\n    return [mergedToken, cssVarsStr, styleId, key];\n  }, function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 3),\n      styleId = _ref2[2];\n    if (isClientSide) {\n      removeCSS(styleId, {\n        mark: ATTR_MARK\n      });\n    }\n  }, function (_ref3) {\n    var _ref4 = _slicedToArray(_ref3, 3),\n      cssVarsStr = _ref4[1],\n      styleId = _ref4[2];\n    if (!cssVarsStr) {\n      return;\n    }\n    var style = updateCSS(cssVarsStr, styleId, {\n      mark: ATTR_MARK,\n      prepend: 'queue',\n      attachTo: container,\n      priority: -999\n    });\n    style[CSS_IN_JS_INSTANCE] = instanceId;\n\n    // Used for `useCacheToken` to remove on batch when token removed\n    style.setAttribute(ATTR_TOKEN, key);\n  });\n  return cache;\n};\nexport var extract = function extract(cache, effectStyles, options) {\n  var _cache = _slicedToArray(cache, 4),\n    styleStr = _cache[1],\n    styleId = _cache[2],\n    cssVarKey = _cache[3];\n  var _ref5 = options || {},\n    plain = _ref5.plain;\n  if (!styleStr) {\n    return null;\n  }\n  var order = -999;\n\n  // ====================== Style ======================\n  // Used for rc-util\n  var sharedAttrs = {\n    'data-rc-order': 'prependQueue',\n    'data-rc-priority': \"\".concat(order)\n  };\n  var styleText = toStyleStr(styleStr, cssVarKey, styleId, sharedAttrs, plain);\n  return [order, styleId, styleText];\n};\nexport default useCSSVarRegister;", "map": {"version": 3, "names": ["_slicedToArray", "_toConsumableArray", "removeCSS", "updateCSS", "useContext", "StyleContext", "ATTR_MARK", "ATTR_TOKEN", "CSS_IN_JS_INSTANCE", "isClientSide", "toStyleStr", "transformToken", "useGlobalCache", "uniqueHash", "CSS_VAR_PREFIX", "useCSSVarRegister", "config", "fn", "key", "prefix", "unitless", "ignore", "token", "_config$scope", "scope", "_useContext", "instanceId", "cache", "container", "<PERSON><PERSON><PERSON>", "_token<PERSON>ey", "stylePath", "concat", "path", "originToken", "_transformToken", "_transformToken2", "mergedToken", "cssVarsStr", "styleId", "_ref", "_ref2", "mark", "_ref3", "_ref4", "style", "prepend", "attachTo", "priority", "setAttribute", "extract", "effectStyles", "options", "_cache", "styleStr", "cssVarKey", "_ref5", "plain", "order", "sharedAttrs", "styleText"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/cssinjs/es/hooks/useCSSVarRegister.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { removeCSS, updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { useContext } from 'react';\nimport StyleContext, { ATTR_MARK, ATTR_TOKEN, CSS_IN_JS_INSTANCE } from \"../StyleContext\";\nimport { isClientSide, toStyleStr } from \"../util\";\nimport { transformToken } from \"../util/css-variables\";\nimport useGlobalCache from \"./useGlobalCache\";\nimport { uniqueHash } from \"./useStyleRegister\";\nexport var CSS_VAR_PREFIX = 'cssVar';\nvar useCSSVarRegister = function useCSSVarRegister(config, fn) {\n  var key = config.key,\n    prefix = config.prefix,\n    unitless = config.unitless,\n    ignore = config.ignore,\n    token = config.token,\n    _config$scope = config.scope,\n    scope = _config$scope === void 0 ? '' : _config$scope;\n  var _useContext = useContext(StyleContext),\n    instanceId = _useContext.cache.instanceId,\n    container = _useContext.container;\n  var tokenKey = token._tokenKey;\n  var stylePath = [].concat(_toConsumableArray(config.path), [key, scope, tokenKey]);\n  var cache = useGlobalCache(CSS_VAR_PREFIX, stylePath, function () {\n    var originToken = fn();\n    var _transformToken = transformToken(originToken, key, {\n        prefix: prefix,\n        unitless: unitless,\n        ignore: ignore,\n        scope: scope\n      }),\n      _transformToken2 = _slicedToArray(_transformToken, 2),\n      mergedToken = _transformToken2[0],\n      cssVarsStr = _transformToken2[1];\n    var styleId = uniqueHash(stylePath, cssVarsStr);\n    return [mergedToken, cssVarsStr, styleId, key];\n  }, function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 3),\n      styleId = _ref2[2];\n    if (isClientSide) {\n      removeCSS(styleId, {\n        mark: ATTR_MARK\n      });\n    }\n  }, function (_ref3) {\n    var _ref4 = _slicedToArray(_ref3, 3),\n      cssVarsStr = _ref4[1],\n      styleId = _ref4[2];\n    if (!cssVarsStr) {\n      return;\n    }\n    var style = updateCSS(cssVarsStr, styleId, {\n      mark: ATTR_MARK,\n      prepend: 'queue',\n      attachTo: container,\n      priority: -999\n    });\n    style[CSS_IN_JS_INSTANCE] = instanceId;\n\n    // Used for `useCacheToken` to remove on batch when token removed\n    style.setAttribute(ATTR_TOKEN, key);\n  });\n  return cache;\n};\nexport var extract = function extract(cache, effectStyles, options) {\n  var _cache = _slicedToArray(cache, 4),\n    styleStr = _cache[1],\n    styleId = _cache[2],\n    cssVarKey = _cache[3];\n  var _ref5 = options || {},\n    plain = _ref5.plain;\n  if (!styleStr) {\n    return null;\n  }\n  var order = -999;\n\n  // ====================== Style ======================\n  // Used for rc-util\n  var sharedAttrs = {\n    'data-rc-order': 'prependQueue',\n    'data-rc-priority': \"\".concat(order)\n  };\n  var styleText = toStyleStr(styleStr, cssVarKey, styleId, sharedAttrs, plain);\n  return [order, styleId, styleText];\n};\nexport default useCSSVarRegister;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,SAASC,SAAS,EAAEC,SAAS,QAAQ,2BAA2B;AAChE,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,YAAY,IAAIC,SAAS,EAAEC,UAAU,EAAEC,kBAAkB,QAAQ,iBAAiB;AACzF,SAASC,YAAY,EAAEC,UAAU,QAAQ,SAAS;AAClD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,IAAIC,cAAc,GAAG,QAAQ;AACpC,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,MAAM,EAAEC,EAAE,EAAE;EAC7D,IAAIC,GAAG,GAAGF,MAAM,CAACE,GAAG;IAClBC,MAAM,GAAGH,MAAM,CAACG,MAAM;IACtBC,QAAQ,GAAGJ,MAAM,CAACI,QAAQ;IAC1BC,MAAM,GAAGL,MAAM,CAACK,MAAM;IACtBC,KAAK,GAAGN,MAAM,CAACM,KAAK;IACpBC,aAAa,GAAGP,MAAM,CAACQ,KAAK;IAC5BA,KAAK,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,aAAa;EACvD,IAAIE,WAAW,GAAGrB,UAAU,CAACC,YAAY,CAAC;IACxCqB,UAAU,GAAGD,WAAW,CAACE,KAAK,CAACD,UAAU;IACzCE,SAAS,GAAGH,WAAW,CAACG,SAAS;EACnC,IAAIC,QAAQ,GAAGP,KAAK,CAACQ,SAAS;EAC9B,IAAIC,SAAS,GAAG,EAAE,CAACC,MAAM,CAAC/B,kBAAkB,CAACe,MAAM,CAACiB,IAAI,CAAC,EAAE,CAACf,GAAG,EAAEM,KAAK,EAAEK,QAAQ,CAAC,CAAC;EAClF,IAAIF,KAAK,GAAGf,cAAc,CAACE,cAAc,EAAEiB,SAAS,EAAE,YAAY;IAChE,IAAIG,WAAW,GAAGjB,EAAE,CAAC,CAAC;IACtB,IAAIkB,eAAe,GAAGxB,cAAc,CAACuB,WAAW,EAAEhB,GAAG,EAAE;QACnDC,MAAM,EAAEA,MAAM;QACdC,QAAQ,EAAEA,QAAQ;QAClBC,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAEA;MACT,CAAC,CAAC;MACFY,gBAAgB,GAAGpC,cAAc,CAACmC,eAAe,EAAE,CAAC,CAAC;MACrDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MACjCE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IAClC,IAAIG,OAAO,GAAG1B,UAAU,CAACkB,SAAS,EAAEO,UAAU,CAAC;IAC/C,OAAO,CAACD,WAAW,EAAEC,UAAU,EAAEC,OAAO,EAAErB,GAAG,CAAC;EAChD,CAAC,EAAE,UAAUsB,IAAI,EAAE;IACjB,IAAIC,KAAK,GAAGzC,cAAc,CAACwC,IAAI,EAAE,CAAC,CAAC;MACjCD,OAAO,GAAGE,KAAK,CAAC,CAAC,CAAC;IACpB,IAAIhC,YAAY,EAAE;MAChBP,SAAS,CAACqC,OAAO,EAAE;QACjBG,IAAI,EAAEpC;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,UAAUqC,KAAK,EAAE;IAClB,IAAIC,KAAK,GAAG5C,cAAc,CAAC2C,KAAK,EAAE,CAAC,CAAC;MAClCL,UAAU,GAAGM,KAAK,CAAC,CAAC,CAAC;MACrBL,OAAO,GAAGK,KAAK,CAAC,CAAC,CAAC;IACpB,IAAI,CAACN,UAAU,EAAE;MACf;IACF;IACA,IAAIO,KAAK,GAAG1C,SAAS,CAACmC,UAAU,EAAEC,OAAO,EAAE;MACzCG,IAAI,EAAEpC,SAAS;MACfwC,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAEnB,SAAS;MACnBoB,QAAQ,EAAE,CAAC;IACb,CAAC,CAAC;IACFH,KAAK,CAACrC,kBAAkB,CAAC,GAAGkB,UAAU;;IAEtC;IACAmB,KAAK,CAACI,YAAY,CAAC1C,UAAU,EAAEW,GAAG,CAAC;EACrC,CAAC,CAAC;EACF,OAAOS,KAAK;AACd,CAAC;AACD,OAAO,IAAIuB,OAAO,GAAG,SAASA,OAAOA,CAACvB,KAAK,EAAEwB,YAAY,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM,GAAGrD,cAAc,CAAC2B,KAAK,EAAE,CAAC,CAAC;IACnC2B,QAAQ,GAAGD,MAAM,CAAC,CAAC,CAAC;IACpBd,OAAO,GAAGc,MAAM,CAAC,CAAC,CAAC;IACnBE,SAAS,GAAGF,MAAM,CAAC,CAAC,CAAC;EACvB,IAAIG,KAAK,GAAGJ,OAAO,IAAI,CAAC,CAAC;IACvBK,KAAK,GAAGD,KAAK,CAACC,KAAK;EACrB,IAAI,CAACH,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,IAAII,KAAK,GAAG,CAAC,GAAG;;EAEhB;EACA;EACA,IAAIC,WAAW,GAAG;IAChB,eAAe,EAAE,cAAc;IAC/B,kBAAkB,EAAE,EAAE,CAAC3B,MAAM,CAAC0B,KAAK;EACrC,CAAC;EACD,IAAIE,SAAS,GAAGlD,UAAU,CAAC4C,QAAQ,EAAEC,SAAS,EAAEhB,OAAO,EAAEoB,WAAW,EAAEF,KAAK,CAAC;EAC5E,OAAO,CAACC,KAAK,EAAEnB,OAAO,EAAEqB,SAAS,CAAC;AACpC,CAAC;AACD,eAAe7C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}