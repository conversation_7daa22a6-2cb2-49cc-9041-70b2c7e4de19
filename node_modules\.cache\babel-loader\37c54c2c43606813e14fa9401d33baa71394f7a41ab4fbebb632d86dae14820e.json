{"ast": null, "code": "import mlfn from '../common/mlfn';\nimport e0fn from '../common/e0fn';\nimport e1fn from '../common/e1fn';\nimport e2fn from '../common/e2fn';\nimport e3fn from '../common/e3fn';\nimport gN from '../common/gN';\nimport adjust_lon from '../common/adjust_lon';\nimport adjust_lat from '../common/adjust_lat';\nimport imlfn from '../common/imlfn';\nimport { HALF_PI, EPSLN } from '../constants/values';\nexport function init() {\n  if (!this.sphere) {\n    this.e0 = e0fn(this.es);\n    this.e1 = e1fn(this.es);\n    this.e2 = e2fn(this.es);\n    this.e3 = e3fn(this.es);\n    this.ml0 = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, this.lat0);\n  }\n}\n\n/* Cassini forward equations--mapping lat,long to x,y\n  -----------------------------------------------------------------------*/\nexport function forward(p) {\n  /* Forward equations\n      -----------------*/\n  var x, y;\n  var lam = p.x;\n  var phi = p.y;\n  lam = adjust_lon(lam - this.long0);\n  if (this.sphere) {\n    x = this.a * Math.asin(Math.cos(phi) * Math.sin(lam));\n    y = this.a * (Math.atan2(Math.tan(phi), Math.cos(lam)) - this.lat0);\n  } else {\n    //ellipsoid\n    var sinphi = Math.sin(phi);\n    var cosphi = Math.cos(phi);\n    var nl = gN(this.a, this.e, sinphi);\n    var tl = Math.tan(phi) * Math.tan(phi);\n    var al = lam * Math.cos(phi);\n    var asq = al * al;\n    var cl = this.es * cosphi * cosphi / (1 - this.es);\n    var ml = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, phi);\n    x = nl * al * (1 - asq * tl * (1 / 6 - (8 - tl + 8 * cl) * asq / 120));\n    y = ml - this.ml0 + nl * sinphi / cosphi * asq * (0.5 + (5 - tl + 6 * cl) * asq / 24);\n  }\n  p.x = x + this.x0;\n  p.y = y + this.y0;\n  return p;\n}\n\n/* Inverse equations\n  -----------------*/\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y -= this.y0;\n  var x = p.x / this.a;\n  var y = p.y / this.a;\n  var phi, lam;\n  if (this.sphere) {\n    var dd = y + this.lat0;\n    phi = Math.asin(Math.sin(dd) * Math.cos(x));\n    lam = Math.atan2(Math.tan(x), Math.cos(dd));\n  } else {\n    /* ellipsoid */\n    var ml1 = this.ml0 / this.a + y;\n    var phi1 = imlfn(ml1, this.e0, this.e1, this.e2, this.e3);\n    if (Math.abs(Math.abs(phi1) - HALF_PI) <= EPSLN) {\n      p.x = this.long0;\n      p.y = HALF_PI;\n      if (y < 0) {\n        p.y *= -1;\n      }\n      return p;\n    }\n    var nl1 = gN(this.a, this.e, Math.sin(phi1));\n    var rl1 = nl1 * nl1 * nl1 / this.a / this.a * (1 - this.es);\n    var tl1 = Math.pow(Math.tan(phi1), 2);\n    var dl = x * this.a / nl1;\n    var dsq = dl * dl;\n    phi = phi1 - nl1 * Math.tan(phi1) / rl1 * dl * dl * (0.5 - (1 + 3 * tl1) * dl * dl / 24);\n    lam = dl * (1 - dsq * (tl1 / 3 + (1 + 3 * tl1) * tl1 * dsq / 15)) / Math.cos(phi1);\n  }\n  p.x = adjust_lon(lam + this.long0);\n  p.y = adjust_lat(phi);\n  return p;\n}\nexport var names = [\"Cassini\", \"Cassini_Soldner\", \"cass\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};", "map": {"version": 3, "names": ["mlfn", "e0fn", "e1fn", "e2fn", "e3fn", "gN", "adjust_lon", "adjust_lat", "imlfn", "HALF_PI", "EPSLN", "init", "sphere", "e0", "es", "e1", "e2", "e3", "ml0", "a", "lat0", "forward", "p", "x", "y", "lam", "phi", "long0", "Math", "asin", "cos", "sin", "atan2", "tan", "sinphi", "cosphi", "nl", "e", "tl", "al", "asq", "cl", "ml", "x0", "y0", "inverse", "dd", "ml1", "phi1", "abs", "nl1", "rl1", "tl1", "pow", "dl", "dsq", "names"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/projections/cass.js"], "sourcesContent": ["import mlfn from '../common/mlfn';\nimport e0fn from '../common/e0fn';\nimport e1fn from '../common/e1fn';\nimport e2fn from '../common/e2fn';\nimport e3fn from '../common/e3fn';\nimport gN from '../common/gN';\nimport adjust_lon from '../common/adjust_lon';\nimport adjust_lat from '../common/adjust_lat';\nimport imlfn from '../common/imlfn';\nimport {HALF_PI, EPSLN} from '../constants/values';\n\nexport function init() {\n  if (!this.sphere) {\n    this.e0 = e0fn(this.es);\n    this.e1 = e1fn(this.es);\n    this.e2 = e2fn(this.es);\n    this.e3 = e3fn(this.es);\n    this.ml0 = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, this.lat0);\n  }\n}\n\n/* Cassini forward equations--mapping lat,long to x,y\n  -----------------------------------------------------------------------*/\nexport function forward(p) {\n\n  /* Forward equations\n      -----------------*/\n  var x, y;\n  var lam = p.x;\n  var phi = p.y;\n  lam = adjust_lon(lam - this.long0);\n\n  if (this.sphere) {\n    x = this.a * Math.asin(Math.cos(phi) * Math.sin(lam));\n    y = this.a * (Math.atan2(Math.tan(phi), Math.cos(lam)) - this.lat0);\n  }\n  else {\n    //ellipsoid\n    var sinphi = Math.sin(phi);\n    var cosphi = Math.cos(phi);\n    var nl = gN(this.a, this.e, sinphi);\n    var tl = Math.tan(phi) * Math.tan(phi);\n    var al = lam * Math.cos(phi);\n    var asq = al * al;\n    var cl = this.es * cosphi * cosphi / (1 - this.es);\n    var ml = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, phi);\n\n    x = nl * al * (1 - asq * tl * (1 / 6 - (8 - tl + 8 * cl) * asq / 120));\n    y = ml - this.ml0 + nl * sinphi / cosphi * asq * (0.5 + (5 - tl + 6 * cl) * asq / 24);\n\n\n  }\n\n  p.x = x + this.x0;\n  p.y = y + this.y0;\n  return p;\n}\n\n/* Inverse equations\n  -----------------*/\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y -= this.y0;\n  var x = p.x / this.a;\n  var y = p.y / this.a;\n  var phi, lam;\n\n  if (this.sphere) {\n    var dd = y + this.lat0;\n    phi = Math.asin(Math.sin(dd) * Math.cos(x));\n    lam = Math.atan2(Math.tan(x), Math.cos(dd));\n  }\n  else {\n    /* ellipsoid */\n    var ml1 = this.ml0 / this.a + y;\n    var phi1 = imlfn(ml1, this.e0, this.e1, this.e2, this.e3);\n    if (Math.abs(Math.abs(phi1) - HALF_PI) <= EPSLN) {\n      p.x = this.long0;\n      p.y = HALF_PI;\n      if (y < 0) {\n        p.y *= -1;\n      }\n      return p;\n    }\n    var nl1 = gN(this.a, this.e, Math.sin(phi1));\n\n    var rl1 = nl1 * nl1 * nl1 / this.a / this.a * (1 - this.es);\n    var tl1 = Math.pow(Math.tan(phi1), 2);\n    var dl = x * this.a / nl1;\n    var dsq = dl * dl;\n    phi = phi1 - nl1 * Math.tan(phi1) / rl1 * dl * dl * (0.5 - (1 + 3 * tl1) * dl * dl / 24);\n    lam = dl * (1 - dsq * (tl1 / 3 + (1 + 3 * tl1) * tl1 * dsq / 15)) / Math.cos(phi1);\n\n  }\n\n  p.x = adjust_lon(lam + this.long0);\n  p.y = adjust_lat(phi);\n  return p;\n\n}\n\nexport var names = [\"Cassini\", \"Cassini_Soldner\", \"cass\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,gBAAgB;AACjC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,EAAE,MAAM,cAAc;AAC7B,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAAQC,OAAO,EAAEC,KAAK,QAAO,qBAAqB;AAElD,OAAO,SAASC,IAAIA,CAAA,EAAG;EACrB,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,EAAE,GAAGZ,IAAI,CAAC,IAAI,CAACa,EAAE,CAAC;IACvB,IAAI,CAACC,EAAE,GAAGb,IAAI,CAAC,IAAI,CAACY,EAAE,CAAC;IACvB,IAAI,CAACE,EAAE,GAAGb,IAAI,CAAC,IAAI,CAACW,EAAE,CAAC;IACvB,IAAI,CAACG,EAAE,GAAGb,IAAI,CAAC,IAAI,CAACU,EAAE,CAAC;IACvB,IAAI,CAACI,GAAG,GAAG,IAAI,CAACC,CAAC,GAAGnB,IAAI,CAAC,IAAI,CAACa,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACG,IAAI,CAAC;EACzE;AACF;;AAEA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,CAAC,EAAE;EAEzB;AACF;EACE,IAAIC,CAAC,EAAEC,CAAC;EACR,IAAIC,GAAG,GAAGH,CAAC,CAACC,CAAC;EACb,IAAIG,GAAG,GAAGJ,CAAC,CAACE,CAAC;EACbC,GAAG,GAAGnB,UAAU,CAACmB,GAAG,GAAG,IAAI,CAACE,KAAK,CAAC;EAElC,IAAI,IAAI,CAACf,MAAM,EAAE;IACfW,CAAC,GAAG,IAAI,CAACJ,CAAC,GAAGS,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACJ,GAAG,CAAC,GAAGE,IAAI,CAACG,GAAG,CAACN,GAAG,CAAC,CAAC;IACrDD,CAAC,GAAG,IAAI,CAACL,CAAC,IAAIS,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACK,GAAG,CAACP,GAAG,CAAC,EAAEE,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC,GAAG,IAAI,CAACL,IAAI,CAAC;EACrE,CAAC,MACI;IACH;IACA,IAAIc,MAAM,GAAGN,IAAI,CAACG,GAAG,CAACL,GAAG,CAAC;IAC1B,IAAIS,MAAM,GAAGP,IAAI,CAACE,GAAG,CAACJ,GAAG,CAAC;IAC1B,IAAIU,EAAE,GAAG/B,EAAE,CAAC,IAAI,CAACc,CAAC,EAAE,IAAI,CAACkB,CAAC,EAAEH,MAAM,CAAC;IACnC,IAAII,EAAE,GAAGV,IAAI,CAACK,GAAG,CAACP,GAAG,CAAC,GAAGE,IAAI,CAACK,GAAG,CAACP,GAAG,CAAC;IACtC,IAAIa,EAAE,GAAGd,GAAG,GAAGG,IAAI,CAACE,GAAG,CAACJ,GAAG,CAAC;IAC5B,IAAIc,GAAG,GAAGD,EAAE,GAAGA,EAAE;IACjB,IAAIE,EAAE,GAAG,IAAI,CAAC3B,EAAE,GAAGqB,MAAM,GAAGA,MAAM,IAAI,CAAC,GAAG,IAAI,CAACrB,EAAE,CAAC;IAClD,IAAI4B,EAAE,GAAG,IAAI,CAACvB,CAAC,GAAGnB,IAAI,CAAC,IAAI,CAACa,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,EAAE,EAAES,GAAG,CAAC;IAE/DH,CAAC,GAAGa,EAAE,GAAGG,EAAE,IAAI,CAAC,GAAGC,GAAG,GAAGF,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE,GAAG,CAAC,GAAGG,EAAE,IAAID,GAAG,GAAG,GAAG,CAAC,CAAC;IACtEhB,CAAC,GAAGkB,EAAE,GAAG,IAAI,CAACxB,GAAG,GAAGkB,EAAE,GAAGF,MAAM,GAAGC,MAAM,GAAGK,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,GAAGF,EAAE,GAAG,CAAC,GAAGG,EAAE,IAAID,GAAG,GAAG,EAAE,CAAC;EAGvF;EAEAlB,CAAC,CAACC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACoB,EAAE;EACjBrB,CAAC,CAACE,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACoB,EAAE;EACjB,OAAOtB,CAAC;AACV;;AAEA;AACA;AACA,OAAO,SAASuB,OAAOA,CAACvB,CAAC,EAAE;EACzBA,CAAC,CAACC,CAAC,IAAI,IAAI,CAACoB,EAAE;EACdrB,CAAC,CAACE,CAAC,IAAI,IAAI,CAACoB,EAAE;EACd,IAAIrB,CAAC,GAAGD,CAAC,CAACC,CAAC,GAAG,IAAI,CAACJ,CAAC;EACpB,IAAIK,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAG,IAAI,CAACL,CAAC;EACpB,IAAIO,GAAG,EAAED,GAAG;EAEZ,IAAI,IAAI,CAACb,MAAM,EAAE;IACf,IAAIkC,EAAE,GAAGtB,CAAC,GAAG,IAAI,CAACJ,IAAI;IACtBM,GAAG,GAAGE,IAAI,CAACC,IAAI,CAACD,IAAI,CAACG,GAAG,CAACe,EAAE,CAAC,GAAGlB,IAAI,CAACE,GAAG,CAACP,CAAC,CAAC,CAAC;IAC3CE,GAAG,GAAGG,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACK,GAAG,CAACV,CAAC,CAAC,EAAEK,IAAI,CAACE,GAAG,CAACgB,EAAE,CAAC,CAAC;EAC7C,CAAC,MACI;IACH;IACA,IAAIC,GAAG,GAAG,IAAI,CAAC7B,GAAG,GAAG,IAAI,CAACC,CAAC,GAAGK,CAAC;IAC/B,IAAIwB,IAAI,GAAGxC,KAAK,CAACuC,GAAG,EAAE,IAAI,CAAClC,EAAE,EAAE,IAAI,CAACE,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,EAAE,CAAC;IACzD,IAAIW,IAAI,CAACqB,GAAG,CAACrB,IAAI,CAACqB,GAAG,CAACD,IAAI,CAAC,GAAGvC,OAAO,CAAC,IAAIC,KAAK,EAAE;MAC/CY,CAAC,CAACC,CAAC,GAAG,IAAI,CAACI,KAAK;MAChBL,CAAC,CAACE,CAAC,GAAGf,OAAO;MACb,IAAIe,CAAC,GAAG,CAAC,EAAE;QACTF,CAAC,CAACE,CAAC,IAAI,CAAC,CAAC;MACX;MACA,OAAOF,CAAC;IACV;IACA,IAAI4B,GAAG,GAAG7C,EAAE,CAAC,IAAI,CAACc,CAAC,EAAE,IAAI,CAACkB,CAAC,EAAET,IAAI,CAACG,GAAG,CAACiB,IAAI,CAAC,CAAC;IAE5C,IAAIG,GAAG,GAAGD,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAC/B,CAAC,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,GAAG,IAAI,CAACL,EAAE,CAAC;IAC3D,IAAIsC,GAAG,GAAGxB,IAAI,CAACyB,GAAG,CAACzB,IAAI,CAACK,GAAG,CAACe,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,IAAIM,EAAE,GAAG/B,CAAC,GAAG,IAAI,CAACJ,CAAC,GAAG+B,GAAG;IACzB,IAAIK,GAAG,GAAGD,EAAE,GAAGA,EAAE;IACjB5B,GAAG,GAAGsB,IAAI,GAAGE,GAAG,GAAGtB,IAAI,CAACK,GAAG,CAACe,IAAI,CAAC,GAAGG,GAAG,GAAGG,EAAE,GAAGA,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGF,GAAG,IAAIE,EAAE,GAAGA,EAAE,GAAG,EAAE,CAAC;IACxF7B,GAAG,GAAG6B,EAAE,IAAI,CAAC,GAAGC,GAAG,IAAIH,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGA,GAAG,IAAIA,GAAG,GAAGG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG3B,IAAI,CAACE,GAAG,CAACkB,IAAI,CAAC;EAEpF;EAEA1B,CAAC,CAACC,CAAC,GAAGjB,UAAU,CAACmB,GAAG,GAAG,IAAI,CAACE,KAAK,CAAC;EAClCL,CAAC,CAACE,CAAC,GAAGjB,UAAU,CAACmB,GAAG,CAAC;EACrB,OAAOJ,CAAC;AAEV;AAEA,OAAO,IAAIkC,KAAK,GAAG,CAAC,SAAS,EAAE,iBAAiB,EAAE,MAAM,CAAC;AACzD,eAAe;EACb7C,IAAI,EAAEA,IAAI;EACVU,OAAO,EAAEA,OAAO;EAChBwB,OAAO,EAAEA,OAAO;EAChBW,KAAK,EAAEA;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}