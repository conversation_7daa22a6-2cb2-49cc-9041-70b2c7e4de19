{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\App.js\";\n// import React from 'react';\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/lib/locale/zh_CN';\n\n// 导入页面组件\nimport Login from './pages/Login';\nimport MainLayout from './components/layout/MainLayout';\nimport RealTimeTraffic from './pages/RealTimeTraffic';\nimport DeviceStatus from './pages/DeviceStatus';\nimport RoadMonitoring from './pages/RoadMonitoring';\nimport SystemManagement from './pages/SystemManagement';\n\n// 导入样式\nimport './App.css';\n\n// 改进的身份验证检查，增加权限检查\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst isAuthenticated = () => {\n  const token = localStorage.getItem('token');\n  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';\n  console.log(\"认证状态检查:\", {\n    token: !!token,\n    isLoggedIn,\n    user: localStorage.getItem('user')\n  });\n  return token && isLoggedIn;\n};\n\n// 检查用户是否有访问系统管理的权限\nconst hasSystemManagementAccess = () => {\n  try {\n    var _user$user;\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    // 只有管理员可以访问系统管理页面\n    const userRole = user.role || ((_user$user = user.user) === null || _user$user === void 0 ? void 0 : _user$user.role) || 'user';\n    console.log('系统管理权限检查:', {\n      用户数据: user,\n      角色: userRole,\n      是否有权限: userRole === 'admin'\n    });\n    return userRole === 'admin';\n  } catch (e) {\n    console.error('检查管理权限失败:', e);\n    return false;\n  }\n};\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: isAuthenticated() ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/real-time-traffic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 33\n          }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 72\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: isAuthenticated() ? /*#__PURE__*/_jsxDEV(MainLayout, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 42\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 59\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/real-time-traffic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 35\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"real-time-traffic\",\n            element: /*#__PURE__*/_jsxDEV(RealTimeTraffic, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 54\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"device-status\",\n            element: /*#__PURE__*/_jsxDEV(DeviceStatus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"road-monitoring\",\n            element: /*#__PURE__*/_jsxDEV(RoadMonitoring, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"system-management\",\n            element: hasSystemManagementAccess() ? /*#__PURE__*/_jsxDEV(SystemManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/real-time-traffic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useNavigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "<PERSON><PERSON>", "MainLayout", "RealTimeTraffic", "DeviceStatus", "RoadMonitoring", "SystemManagement", "jsxDEV", "_jsxDEV", "isAuthenticated", "token", "localStorage", "getItem", "isLoggedIn", "console", "log", "user", "hasSystemManagementAccess", "_user$user", "JSON", "parse", "userRole", "role", "用户数据", "角色", "是否有权限", "e", "error", "App", "locale", "children", "path", "element", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/App.js"], "sourcesContent": ["// import React from 'react';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\r\nimport { ConfigProvider } from 'antd';\r\nimport zhCN from 'antd/lib/locale/zh_CN';\r\n\r\n// 导入页面组件\r\nimport Login from './pages/Login';\r\nimport MainLayout from './components/layout/MainLayout';\r\nimport RealTimeTraffic from './pages/RealTimeTraffic';\r\nimport DeviceStatus from './pages/DeviceStatus';\r\nimport RoadMonitoring from './pages/RoadMonitoring';\r\nimport SystemManagement from './pages/SystemManagement';\r\n\r\n// 导入样式\r\nimport './App.css';\r\n\r\n// 改进的身份验证检查，增加权限检查\r\nconst isAuthenticated = () => {\r\n  const token = localStorage.getItem('token');\r\n  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';\r\n  console.log(\"认证状态检查:\", { \r\n    token: !!token, \r\n    isLoggedIn,\r\n    user: localStorage.getItem('user')\r\n  }); \r\n  return token && isLoggedIn;\r\n};\r\n\r\n// 检查用户是否有访问系统管理的权限\r\nconst hasSystemManagementAccess = () => {\r\n  try {\r\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n    // 只有管理员可以访问系统管理页面\r\n    const userRole = user.role || user.user?.role || 'user';\r\n    console.log('系统管理权限检查:', {\r\n      用户数据: user,\r\n      角色: userRole,\r\n      是否有权限: userRole === 'admin'\r\n    });\r\n    return userRole === 'admin';\r\n  } catch (e) {\r\n    console.error('检查管理权限失败:', e);\r\n    return false;\r\n  }\r\n};\r\n\r\nfunction App() {\r\n\r\n  return (\r\n    <ConfigProvider locale={zhCN}>\r\n      <Router>\r\n        <Routes>\r\n          <Route path=\"/login\" element={\r\n            isAuthenticated() ? <Navigate to=\"/real-time-traffic\" /> : <Login />\r\n          } />\r\n          <Route \r\n            path=\"/\" \r\n            element={isAuthenticated() ? <MainLayout /> : <Navigate to=\"/login\" />}\r\n          >\r\n            <Route index element={<Navigate to=\"/real-time-traffic\" />} />\r\n            <Route path=\"real-time-traffic\" element={<RealTimeTraffic />} />\r\n            <Route path=\"device-status\" element={<DeviceStatus />} />\r\n            <Route path=\"road-monitoring\" element={<RoadMonitoring />} />\r\n            <Route \r\n              path=\"system-management\" \r\n              element={\r\n                hasSystemManagementAccess() ? \r\n                <SystemManagement /> : \r\n                <Navigate to=\"/real-time-traffic\" />\r\n              } \r\n            />\r\n          </Route>\r\n          <Route path=\"*\" element={<Navigate to=\"/\" />} />\r\n        </Routes>\r\n      </Router>\r\n    </ConfigProvider>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAChG,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,uBAAuB;;AAExC;AACA,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;;AAEvD;AACA,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,UAAU,GAAGF,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM;EAChEE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;IACrBL,KAAK,EAAE,CAAC,CAACA,KAAK;IACdG,UAAU;IACVG,IAAI,EAAEL,YAAY,CAACC,OAAO,CAAC,MAAM;EACnC,CAAC,CAAC;EACF,OAAOF,KAAK,IAAIG,UAAU;AAC5B,CAAC;;AAED;AACA,MAAMI,yBAAyB,GAAGA,CAAA,KAAM;EACtC,IAAI;IAAA,IAAAC,UAAA;IACF,MAAMF,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACT,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC7D;IACA,MAAMS,QAAQ,GAAGL,IAAI,CAACM,IAAI,MAAAJ,UAAA,GAAIF,IAAI,CAACA,IAAI,cAAAE,UAAA,uBAATA,UAAA,CAAWI,IAAI,KAAI,MAAM;IACvDR,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvBQ,IAAI,EAAEP,IAAI;MACVQ,EAAE,EAAEH,QAAQ;MACZI,KAAK,EAAEJ,QAAQ,KAAK;IACtB,CAAC,CAAC;IACF,OAAOA,QAAQ,KAAK,OAAO;EAC7B,CAAC,CAAC,OAAOK,CAAC,EAAE;IACVZ,OAAO,CAACa,KAAK,CAAC,WAAW,EAAED,CAAC,CAAC;IAC7B,OAAO,KAAK;EACd;AACF,CAAC;AAED,SAASE,GAAGA,CAAA,EAAG;EAEb,oBACEpB,OAAA,CAACT,cAAc;IAAC8B,MAAM,EAAE7B,IAAK;IAAA8B,QAAA,eAC3BtB,OAAA,CAACd,MAAM;MAAAoC,QAAA,eACLtB,OAAA,CAACb,MAAM;QAAAmC,QAAA,gBACLtB,OAAA,CAACZ,KAAK;UAACmC,IAAI,EAAC,QAAQ;UAACC,OAAO,EAC1BvB,eAAe,CAAC,CAAC,gBAAGD,OAAA,CAACX,QAAQ;YAACoC,EAAE,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACP,KAAK;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACpE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACJ7B,OAAA,CAACZ,KAAK;UACJmC,IAAI,EAAC,GAAG;UACRC,OAAO,EAAEvB,eAAe,CAAC,CAAC,gBAAGD,OAAA,CAACN,UAAU;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACX,QAAQ;YAACoC,EAAE,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAP,QAAA,gBAEvEtB,OAAA,CAACZ,KAAK;YAAC0C,KAAK;YAACN,OAAO,eAAExB,OAAA,CAACX,QAAQ;cAACoC,EAAE,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9D7B,OAAA,CAACZ,KAAK;YAACmC,IAAI,EAAC,mBAAmB;YAACC,OAAO,eAAExB,OAAA,CAACL,eAAe;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChE7B,OAAA,CAACZ,KAAK;YAACmC,IAAI,EAAC,eAAe;YAACC,OAAO,eAAExB,OAAA,CAACJ,YAAY;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzD7B,OAAA,CAACZ,KAAK;YAACmC,IAAI,EAAC,iBAAiB;YAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7D7B,OAAA,CAACZ,KAAK;YACJmC,IAAI,EAAC,mBAAmB;YACxBC,OAAO,EACLf,yBAAyB,CAAC,CAAC,gBAC3BT,OAAA,CAACF,gBAAgB;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACpB7B,OAAA,CAACX,QAAQ;cAACoC,EAAE,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACpC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACR7B,OAAA,CAACZ,KAAK;UAACmC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAExB,OAAA,CAACX,QAAQ;YAACoC,EAAE,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB;AAACE,EAAA,GA/BQX,GAAG;AAiCZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}